import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  ParseUUIDPipe,
} from '@nestjs/common';
import { PositionService } from './position.service';
import {
  CreatePositionDto,
  PositionResponseDto,
  UpdatePositionDto,
} from './dto/department.dto';

@Controller('api/v1/companies/:companyId/departments/:departmentId/positions')
export class PositionController {
  constructor(private readonly positionService: PositionService) {}

  @Post()
  create(
    @Param('departmentId', ParseUUIDPipe) departmentId: string,
    @Body() createPositionDto: CreatePositionDto,
  ): Promise<PositionResponseDto> {
    return this.positionService.createPosition(departmentId, createPositionDto);
  }

  @Get()
  findAll(
    @Param('departmentId', ParseUUIDPipe) departmentId: string,
  ): Promise<PositionResponseDto[]> {
    return this.positionService.getAllPositionsInDepartment(departmentId);
  }

  @Get(':id')
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<PositionResponseDto> {
    return this.positionService.getPositionById(id);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePositionDto: UpdatePositionDto,
  ): Promise<PositionResponseDto> {
    return this.positionService.updatePosition(id, updatePositionDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.positionService.deletePosition(id);
  }
}
