import { Injectable, NotFoundException } from '@nestjs/common';
import {
  CreatePositionDto,
  PositionResponseDto,
  UpdatePositionDto,
} from './dto/department.dto';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';

@Injectable()
export class PositionService {
  constructor(private prisma: PrismaService) {}

  async createPosition(
    departmentId: string,
    data: CreatePositionDto,
  ): Promise<PositionResponseDto> {
    // Verify department exists and belongs to company
    const department = await this.prisma.department.findUnique({
      where: { id: departmentId },
    });

    if (!department) {
      throw new NotFoundException(
        `Department with ID ${departmentId} not found or doesn't belong to company`,
      );
    }

    return this.prisma.position.create({
      data: {
        ...data,
        departmentId,
        requiredSkills: data.requiredSkills || [],
      },
    });
  }

  async getAllPositionsInDepartment(
    departmentId: string,
  ): Promise<PositionResponseDto[]> {
    // Verify department belongs to company
    const department = await this.prisma.department.findUnique({
      where: { id: departmentId },
    });

    if (!department) {
      throw new NotFoundException(
        `Department with ID ${departmentId} not found or doesn't belong to company`,
      );
    }

    return this.prisma.position.findMany({
      where: { departmentId },
    });
  }

  async getPositionById(id: string): Promise<PositionResponseDto> {
    const position = await this.prisma.position.findUnique({
      where: { id },
    });

    if (!position) {
      throw new NotFoundException(
        `Position with ID ${id} not found in department`,
      );
    }

    return position;
  }

  async updatePosition(
    id: string,
    data: UpdatePositionDto,
  ): Promise<PositionResponseDto> {
    try {
      return await this.prisma.position.update({
        where: { id },
        data: {
          ...data,
          requiredSkills: data.requiredSkills,
        },
      });
    } catch (error) {
      throw new NotFoundException(
        `Position with ID ${id} not found in department`,
      );
    }
  }

  async deletePosition(id: string): Promise<void> {
    try {
      await this.prisma.position.delete({
        where: { id },
      });
    } catch (error) {
      throw new NotFoundException(
        `Position with ID ${id} not found in department`,
      );
    }
  }
}
