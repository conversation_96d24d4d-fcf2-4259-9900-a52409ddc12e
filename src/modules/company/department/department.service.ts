import { Injectable, NotFoundException } from '@nestjs/common';

import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
import {
  CreateDepartmentDto,
  DepartmentResponseDto,
  UpdateDepartmentDto,
} from './dto/department.dto';

@Injectable()
export class DepartmentService {
  constructor(private prisma: PrismaService) {}

  async createDepartment(
    companyId: string,
    data: CreateDepartmentDto,
  ): Promise<DepartmentResponseDto> {
    return this.prisma.department.create({
      data: {
        ...data,
        company: { connect: { id: companyId } },
      },
    });
  }

  async getAllDepartments(companyId: string): Promise<DepartmentResponseDto[]> {
    return this.prisma.department.findMany({
      where: { companyId },
      include: {
        positions: true,
      },
    });
  }

  async getDepartmentById(id: string): Promise<DepartmentResponseDto> {
    const department = await this.prisma.department.findUnique({
      where: { id },
      include: {
        positions: true,
      },
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    return department;
  }

  async updateDepartment(
    id: string,
    data: UpdateDepartmentDto,
  ): Promise<DepartmentResponseDto> {
    try {
      return await this.prisma.department.update({
        where: { id },
        data,
      });
    } catch (error) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }
  }

  async deleteDepartment(id: string): Promise<void> {
    try {
      await this.prisma.department.delete({
        where: { id },
      });
    } catch (error) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }
  }
}
