import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  MinLength,
  MaxLength,
  IsUUID,
  ArrayNotEmpty,
  IsArray,
} from 'class-validator';

export class CreateDepartmentDto {
  @ApiProperty({
    description: 'Department name',
    example: 'Human Resources',
    minLength: 3,
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(50)
  departmentName: string;

  @ApiPropertyOptional({
    description: 'Department description',
    example: 'Employee and contract management',
    maxLength: 255,
    nullable: true,
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  description?: string;
}

export class UpdateDepartmentDto {
  @ApiPropertyOptional({
    description: 'Department name',
    example: 'Human Resources',
    minLength: 3,
    maxLength: 50,
    nullable: true,
  })
  @IsString()
  @IsOptional()
  @MinLength(3)
  @MaxLength(50)
  departmentName?: string;

  @ApiPropertyOptional({
    description: 'Department description',
    example: 'Employee and contract management',
    maxLength: 255,
    nullable: true,
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  description?: string;
}

export class DepartmentResponseDto {
  @ApiProperty({
    description: 'Unique department ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Department name',
    example: 'Human Resources',
  })
  @IsString()
  departmentName: string;

  @ApiPropertyOptional({
    description: 'Department description',
    example: 'Employee and contract management',
    nullable: true,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Associated company ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  companyId: string;
}
export class CreatePositionDto {
  @ApiProperty({
    description: 'Position title',
    example: 'Full Stack Developer',
    minLength: 3,
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(50)
  positionTitle: string;

  @ApiPropertyOptional({
    description: 'Position description',
    example: 'Web and mobile application development',
    maxLength: 255,
    nullable: true,
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiPropertyOptional({
    description: 'Required skills for the position',
    example: ['JavaScript', 'TypeScript', 'Angular'],
    type: [String],
    nullable: true,
  })
  @IsArray()
  @IsOptional()
  requiredSkills?: string[];
}

export class UpdatePositionDto {
  @ApiPropertyOptional({
    description: 'Position title',
    example: 'Senior Full Stack Developer',
    minLength: 3,
    maxLength: 50,
    nullable: true,
  })
  @IsString()
  @IsOptional()
  @MinLength(3)
  @MaxLength(50)
  positionTitle?: string;

  @ApiPropertyOptional({
    description: 'Position description',
    example: 'Complex web and mobile application development',
    maxLength: 255,
    nullable: true,
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  description?: string;

  @ApiPropertyOptional({
    description: 'Required skills for the position',
    example: ['JavaScript', 'TypeScript', 'Angular', 'Node.js'],
    type: [String],
    nullable: true,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  requiredSkills?: string[];
}

export class PositionResponseDto {
  @ApiProperty({
    description: 'Unique position ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Position title',
    example: 'Full Stack Developer',
  })
  @IsString()
  positionTitle: string;

  @ApiPropertyOptional({
    description: 'Position description',
    example: 'Web and mobile application development',
    nullable: true,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Associated department ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  departmentId: string;

  @ApiProperty({
    description: 'Required skills for the position',
    example: ['JavaScript', 'TypeScript', 'Angular'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  requiredSkills: string[];
}
