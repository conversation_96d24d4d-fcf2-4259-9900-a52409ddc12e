import {
  Injectable,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import {
  CompanyWithRelations,
  CreateCompanyDto,
  UpdateCompanyDto,
} from './dto/company.dto';

@Injectable()
export class CompanyService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new company with all associated data in a transactional operation
   * @param createCompanyDto Data for company creation
   * @param creatorUserId ID of the user creating the company
   * @returns The created company with all relations
   * @throws ConflictException if company name or email already exists
   * @throws InternalServerErrorException for other database errors
   */
  async createCompany(
    createCompanyDto: CreateCompanyDto,
    creatorUserId: string,
  ): Promise<CompanyWithRelations> {
    const { address, taxSettings, payrollConfiguration, ...companyData } =
      createCompanyDto;

    try {
      return (await this.prisma.$transaction(
        async (tx) => {
          // Create the company with associated data
          const company = await tx.company.create({
            data: {
              ...companyData,
              address: address ? { create: address } : undefined,
              taxSettings: taxSettings ? { create: taxSettings } : undefined,
              payrollConfiguration: payrollConfiguration
                ? { create: payrollConfiguration }
                : undefined,
            },
            include: this.getCompanyIncludeRelations(),
          });
          console.log(company, creatorUserId);

          try {
            await tx.employeeData.create({
              data: {
                id: creatorUserId,
                companyId: company.id,
                hireDate: new Date(),
                // position: {
                //   connect: {
                //     id: await this.getDefaultPositionId(tx, company.id),
                //   },
                // },
              },
            });
          } catch (error) {
            console.log(error);
          }

          return company;
        },
        { timeout: 20000 },
      )) as CompanyWithRelations;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2002'
      ) {
        throw new ConflictException(
          'Company with this name or email already exists',
        );
      }
      throw new InternalServerErrorException(
        'Failed to create company',
        error.message,
      );
    }
  }

  /**
   * Updates a company and its related data
   * @param companyId ID of the company to update
   * @param updateCompanyDto Data for company update
   * @returns The updated company with all relations
   * @throws NotFoundException if company doesn't exist
   * @throws ConflictException if new company name or email conflicts
   */
  async updateCompany(
    companyId: string,
    updateCompanyDto: UpdateCompanyDto,
  ): Promise<CompanyWithRelations> {
    await this.verifyCompanyExists(companyId);

    const { address, taxSettings, payrollConfiguration, ...companyData } =
      updateCompanyDto;

    try {
      return (await this.prisma.company.update({
        where: { id: companyId },
        data: {
          ...companyData,
          address: address
            ? { upsert: { create: address, update: address } }
            : undefined,
          taxSettings: taxSettings
            ? { upsert: { create: taxSettings, update: taxSettings } }
            : undefined,
          payrollConfiguration: payrollConfiguration
            ? {
                upsert: {
                  create: payrollConfiguration,
                  update: payrollConfiguration,
                },
              }
            : undefined,
        },
        include: this.getCompanyIncludeRelations(),
      })) as CompanyWithRelations;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2002'
      ) {
        throw new ConflictException(
          'Company with this name or email already exists',
        );
      }
      throw error;
    }
  }

  /**
   * Retrieves all companies with their relations
   * @param page Pagination page number
   * @param limit Number of items per page
   * @returns Paginated list of companies with relations
   */
  async getAllCompanies(
    page = 1,
    limit = 10,
  ): Promise<{ data; total: number }> {
    const [total, data] = await Promise.all([
      this.prisma.company.count(),
      this.prisma.company.findMany({
        skip: (page - 1) * limit,
        take: limit,
        include: this.getCompanyIncludeRelations(),
        orderBy: { companyName: 'asc' },
      }),
    ]);

    return { data, total };
  }

  /**
   * Retrieves a specific company by ID
   * @param companyId ID of the company to retrieve
   * @returns The company with all relations
   * @throws NotFoundException if company doesn't exist
   */
  async getCompanyById(companyId: string): Promise<CompanyWithRelations> {
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
      include: this.getCompanyIncludeRelations(),
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    return company as CompanyWithRelations;
  }

  /**
   * Retrieves a company by user ID (for employee context)
   * @param userId ID of the user/employee
   * @returns The company with basic relations
   * @throws NotFoundException if user is not associated with a company
   */
  async getCompanyByUserId(userId: string): Promise<CompanyWithRelations> {
    const employee = await this.prisma.employeeData.findUnique({
      where: { id: userId },
      include: { company: true },
    });

    if (!employee?.company) {
      throw new NotFoundException('User is not associated with any company');
    }

    return this.getCompanyById(employee.company.id);
  }

  /**
   * Deletes a company and its related data in a transaction
   * @param companyId ID of the company to delete
   * @returns The deleted company
   * @throws NotFoundException if company doesn't exist
   */
  async deleteCompany(companyId: string) {
    await this.verifyCompanyExists(companyId);

    return this.prisma.$transaction(async (tx) => {
      // Delete all related records first
      await Promise.all([
        tx.companyTaxSettings.deleteMany({ where: { companyId } }),
        tx.payrollConfiguration.deleteMany({ where: { companyId } }),
        tx.employeeData.deleteMany({ where: { companyId } }),
        // Add other relations as needed
      ]);

      return tx.company.delete({
        where: { id: companyId },
        include: { address: true },
      });
    });
  }

  /**
   * Verifies if a company exists
   * @param companyId ID of the company to verify
   * @throws NotFoundException if company doesn't exist
   */
  private async verifyCompanyExists(companyId: string): Promise<void> {
    const exists = await this.prisma.company.count({
      where: { id: companyId },
    });

    if (!exists) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }
  }

  /**
   * Gets the default position ID for a new employee in a company
   * @param tx Prisma transaction client
   * @param companyId Company ID
   * @returns Default position ID
   */
  // private async getDefaultPositionId(
  //   tx: Prisma.TransactionClient,
  //   companyId: string,
  // ): Promise<string> {
  //   const defaultPosition = await tx.position.findFirst({
  //     where: {
  //       companyId,
  //       positionTitle: 'Employee',
  //     },
  //     select: { id: true },
  //   });

  //   if (!defaultPosition) {
  //     throw new InternalServerErrorException(
  //       'Default employee position not found',
  //     );
  //   }

  //   return defaultPosition.id;
  // }

  /**
   * Defines the relations to include when querying a company
   * @returns Prisma include object
   */
  private getCompanyIncludeRelations(): Prisma.CompanyInclude {
    return {
      address: true,
      employees: {
        include: {
          user: {
            select: {
              id: true,
              email: true,
              role: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                  avatar: true,
                },
              },
            },
          },
          position: true,
          department: true,
        },
      },
      taxSettings: true,
      payrollConfiguration: true,
      departments: true,
      documents: true,
    };
  }
}
