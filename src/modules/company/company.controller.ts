import {
  Body,
  Controller,
  Post,
  HttpCode,
  HttpStatus,
  Patch,
  UseGuards,
  Delete,
  Param,
  Get,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNoContentResponse,
  ApiForbiddenResponse,
  ApiUnauthorizedResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';

import { CurrentUser } from 'src/modules/shared/decorators/current-user.decorator';
import { CompanyService } from './company.service';
import {
  CompanyWithRelations,
  CreateCompanyDto,
  UpdateCompanyDto,
} from './dto/company.dto';
import { Roles } from '../shared/decorators/allowed-role.decorator';
import { RoleEnum } from '@prisma/client';
import { RolesGuard } from '../shared/guards/role.guard';
import { PaginationDto } from 'src/@types/types';

@ApiTags('Companies')
@ApiBearerAuth()
@Controller('api/v1/companies')
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new company',
    description: 'Allows authorized users to create a new company profile.',
  })
  @ApiCreatedResponse({
    description: 'Company created successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid input data format' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized access' })
  @ApiConflictResponse({ description: 'Company name or email already exists' })
  @ApiBody({ type: CreateCompanyDto })
  async createCompany(
    @CurrentUser('id') creatorId: string,
    @Body() createCompanyDto: CreateCompanyDto,
  ): Promise<CompanyWithRelations> {
    return this.companyService.createCompany(createCompanyDto, creatorId);
  }

  @Patch(':companyId')
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.ADMIN_HR, RoleEnum.OWNER)
  @UseGuards(RolesGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update company information',
    description:
      'Update company details. Restricted to company owners and admins.',
  })
  @ApiOkResponse({
    description: 'Company updated successfully',
  })
  @ApiNotFoundResponse({ description: 'Company not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized access' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  @ApiParam({
    name: 'companyId',
    type: String,
    description: 'UUID of the company to update',
    example: '123e4567-e89b-12d3-a456-************',
  })
  async updateCompany(
    @Param('companyId') companyId: string,
    @Body() updateCompanyDto: UpdateCompanyDto,
  ): Promise<CompanyWithRelations> {
    return this.companyService.updateCompany(companyId, updateCompanyDto);
  }

  @Get()
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.ADMIN_HR)
  @UseGuards(RolesGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get all companies (paginated)',
    description:
      'Retrieve a paginated list of all companies. Admin access only.',
  })
  @ApiOkResponse({
    description: 'Paginated list of companies',
  })
  @ApiQuery({ type: PaginationDto })
  @ApiUnauthorizedResponse({ description: 'Unauthorized access' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async getAllCompanies(
    @Query() pagination: PaginationDto,
  ): Promise<{ data: CompanyWithRelations[]; total: number }> {
    return this.companyService.getAllCompanies(
      pagination.page,
      pagination.limit,
    );
  }

  @Get(':companyId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get company details by ID',
    description: 'Retrieve detailed information about a specific company.',
  })
  @ApiOkResponse({
    description: 'Company details retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Company not found' })
  @ApiParam({
    name: 'companyId',
    type: String,
    description: 'UUID of the company to retrieve',
    example: '123e4567-e89b-12d3-a456-************',
  })
  async getCompanyById(
    @Param('companyId') companyId: string,
  ): Promise<CompanyWithRelations> {
    return this.companyService.getCompanyById(companyId);
  }

  @Get('me/company')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get current user company details',
    description:
      'Retrieve company details for the currently authenticated user.',
  })
  @ApiOkResponse({
    description: 'Company details retrieved successfully',
  })
  @ApiNotFoundResponse({
    description: 'User is not associated with any company',
  })
  async getCurrentUserCompany(
    @CurrentUser('id') userId: string,
  ): Promise<CompanyWithRelations> {
    return this.companyService.getCompanyByUserId(userId);
  }

  @Delete(':companyId')
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @UseGuards(RolesGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete a company',
    description:
      'Permanently delete a company. Restricted to owners and super admins.',
  })
  @ApiNoContentResponse({ description: 'Company deleted successfully' })
  @ApiNotFoundResponse({ description: 'Company not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized access' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  @ApiParam({
    name: 'companyId',
    type: String,
    description: 'UUID of the company to delete',
    example: '123e4567-e89b-12d3-a456-************',
  })
  async deleteCompany(@Param('companyId') companyId: string): Promise<void> {
    await this.companyService.deleteCompany(companyId);
  }
}
