import {
  IsString,
  IsEmail,
  IsOptional,
  IsArray,
  IsNumber,
  ValidateNested,
  IsNotEmpty,
  IsEnum,
  Max,
  <PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  PayrollCycleEnum,
  BonusCalculationEnum,
  TaxPaymentFrequencyEnum,
  Prisma,
} from '@prisma/client';

export class AddressDto {
  @ApiProperty({ description: 'Street address', example: '123 Main St' })
  @IsString()
  @IsNotEmpty()
  street: string;

  @ApiProperty({ description: 'City', example: 'New York' })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiPropertyOptional({
    description: 'Postal/ZIP code',
    example: '10001',
  })
  @IsOptional()
  @IsString()
  postalCode?: string;

  @ApiProperty({ description: 'Country', example: 'United States' })
  @IsString()
  @IsNotEmpty()
  country: string;
}

export class CompanyTaxSettingsDto {
  @ApiProperty({
    description: 'Income tax rate (percentage)',
    example: 15.0,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  incomeTaxRate: number;

  @ApiProperty({
    description: 'Social security rate (percentage)',
    example: 10.0,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  socialSecurityRate: number;

  @ApiProperty({
    description: 'Unemployment insurance rate (percentage)',
    example: 5.0,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  unEmploymentInsuranceRate: number;

  @ApiProperty({
    description: 'Health insurance rate (percentage)',
    example: 8.0,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  healthInsuranceRate: number;

  @ApiProperty({
    description: 'Pension contribution rate (percentage)',
    example: 7.0,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  pensionContributionRate: number;

  @ApiPropertyOptional({
    description: 'Income tax threshold amount',
    example: 12000,
  })
  @IsOptional()
  @IsNumber()
  incomeTaxThreshold?: number;

  @ApiPropertyOptional({
    description: 'Social security threshold amount',
    example: 50000,
  })
  @IsOptional()
  @IsNumber()
  socialSecurityThreshold?: number;

  @ApiPropertyOptional({
    description: 'Tax payment frequency',
    enum: TaxPaymentFrequencyEnum,
  })
  @IsOptional()
  @IsEnum(TaxPaymentFrequencyEnum)
  taxPaymentFrequency?: TaxPaymentFrequencyEnum;
}

export class PayrollConfigurationDto {
  @ApiProperty({
    description: 'Payroll cycle',
    enum: PayrollCycleEnum,
    example: PayrollCycleEnum.MONTHLY,
  })
  @IsEnum(PayrollCycleEnum)
  payrollCycle: PayrollCycleEnum;

  @ApiProperty({
    description: 'Day of the month for salary payment (1-31)',
    example: 25,
    minimum: 1,
    maximum: 31,
  })
  @IsNumber()
  @Min(1)
  @Max(31)
  paymentDay: number;

  @ApiProperty({
    description: 'Overtime pay multiplier',
    example: 1.5,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  overtimeMultiplier: number;

  @ApiPropertyOptional({
    description: 'Maximum allowed overtime hours per period',
    example: 20,
  })
  @IsOptional()
  @IsNumber()
  maxOvertimeHours?: number;

  @ApiPropertyOptional({
    description: 'Bonus calculation type',
    enum: BonusCalculationEnum,
  })
  @IsOptional()
  @IsEnum(BonusCalculationEnum)
  bonusType?: BonusCalculationEnum;

  @ApiPropertyOptional({
    description: 'Performance bonus rate (percentage)',
    example: 10,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  performanceBonusRate?: number;
}

export class CreateCompanyDto {
  @ApiProperty({
    description: 'Official legal name of the company',
    example: 'Tech Innovations Incorporated',
  })
  @IsString()
  @IsNotEmpty()
  officialName: string;

  @ApiProperty({
    description: 'Registered company name (unique identifier)',
    example: 'Tech Innovations Inc.',
  })
  @IsString()
  @IsNotEmpty()
  companyName: string;

  @ApiProperty({
    description: 'Email address of the company (must be unique)',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'Phone numbers associated with the company',
    example: ['+1234567890', '+9876543210'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  phoneNumbers?: string[];

  @ApiPropertyOptional({
    description: 'Website URL of the company',
    example: 'https://www.techinnovations.com',
  })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiPropertyOptional({
    description: 'Logo URL of the company',
    example: 'https://www.techinnovations.com/logo.png',
  })
  @IsOptional()
  @IsString()
  logo?: string;

  @ApiProperty({
    description: 'Tax identification number of the company',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  taxIdentificationNumber: string;

  @ApiProperty({
    description: 'Industry in which the company operates',
    example: 'Technology',
  })
  @IsString()
  @IsNotEmpty()
  industry: string;

  @ApiProperty({
    description: 'Brief description of the company',
    example: 'A leading company in tech innovation.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Address information associated with the company',
    type: AddressDto,
  })
  @ValidateNested()
  @Type(() => AddressDto)
  address: AddressDto;

  @ApiProperty({
    description: 'Company tax settings',
    type: CompanyTaxSettingsDto,
  })
  @ValidateNested()
  @Type(() => CompanyTaxSettingsDto)
  taxSettings: CompanyTaxSettingsDto;

  @ApiProperty({
    description: 'Payroll configuration settings',
    type: PayrollConfigurationDto,
  })
  @ValidateNested()
  @Type(() => PayrollConfigurationDto)
  payrollConfiguration: PayrollConfigurationDto;
}

export class UpdateCompanyDto {
  @ApiPropertyOptional({
    description: 'Official legal name of the company',
    example: 'Tech Innovations Incorporated',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  officialName?: string;

  @ApiPropertyOptional({
    description: 'Registered company name (unique identifier)',
    example: 'Tech Innovations Inc.',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  companyName?: string;

  @ApiPropertyOptional({
    description: 'Email address of the company (must be unique)',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    description: 'Phone numbers associated with the company',
    example: ['+1234567890', '+9876543210'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  phoneNumbers?: string[];

  @ApiPropertyOptional({
    description: 'Website URL of the company',
    example: 'https://www.techinnovations.com',
  })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiPropertyOptional({
    description: 'Logo URL of the company',
    example: 'https://www.techinnovations.com/logo.png',
  })
  @IsOptional()
  @IsString()
  logo?: string;

  @ApiPropertyOptional({
    description: 'Tax identification number of the company',
    example: '1234567890',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  taxIdentificationNumber?: string;

  @ApiPropertyOptional({
    description: 'Industry in which the company operates',
    example: 'Technology',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  industry?: string;

  @ApiPropertyOptional({
    description: 'Brief description of the company',
    example: 'A leading company in tech innovation.',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  description?: string;

  @ApiPropertyOptional({
    description: 'Address information associated with the company',
    type: AddressDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  address?: AddressDto;

  @ApiPropertyOptional({
    description: 'Company tax settings',
    type: CompanyTaxSettingsDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CompanyTaxSettingsDto)
  taxSettings?: CompanyTaxSettingsDto;

  @ApiPropertyOptional({
    description: 'Payroll configuration settings',
    type: PayrollConfigurationDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PayrollConfigurationDto)
  payrollConfiguration?: PayrollConfigurationDto;
}

// Types for better type safety
export type CompanyWithRelations = Prisma.CompanyGetPayload<{
  include: {
    address: true;
    employees: {
      include: {
        user: {
          select: {
            id: true;
            email: true;
            role: true;
            profile: {
              select: {
                firstName: true;
                lastName: true;
                avatar: true;
              };
            };
          };
        };
        position: true;
        department: true;
      };
    };
    taxSettings: true;
    payrollConfiguration: true;
    departments: true;
    documents: true;
    jobOffers: true; // Ajouté pour correspondre au modèle Prisma
  };
}>;
