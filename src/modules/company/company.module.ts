import { Module } from '@nestjs/common';
import { CompanyService } from './company.service';
import { CompanyController } from './company.controller';
import { PositionController } from './department/position.controller';
import { DepartmentController } from './department/department.controller';
import { PositionService } from './department/position.service';
import { DepartmentService } from './department/department.service';

@Module({
  controllers: [CompanyController, PositionController, DepartmentController],
  providers: [CompanyService, PositionService, DepartmentService],
})
export class CompanyModule {}
