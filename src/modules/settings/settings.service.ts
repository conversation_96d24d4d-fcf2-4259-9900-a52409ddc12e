import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../shared/db/prisma/prisma.service';
import { 
  CreateCompanySettingsDto, 
  UpdateCompanySettingsDto,
  PayrollConfigurationDto,
  TaxConfigurationDto,
  NotificationSettingsDto,
  SecuritySettingsDto
} from './dto/company-settings.dto';

@Injectable()
export class SettingsService {
  constructor(private readonly prisma: PrismaService) {}

  async createCompanySettings(createSettingsDto: CreateCompanySettingsDto) {
    const { companyId, ...settingsData } = createSettingsDto;

    // Vérifier que l'entreprise existe
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    // Vérifier qu'il n'y a pas déjà des paramètres pour cette entreprise
    const existingSettings = await this.prisma.companySettings.findUnique({
      where: { companyId },
    });

    if (existingSettings) {
      throw new ConflictException(`Settings already exist for company ${companyId}`);
    }

    return this.prisma.companySettings.create({
      data: {
        ...settingsData,
        company: { connect: { id: companyId } },
      },
      include: {
        company: {
          select: {
            id: true,
            companyName: true,
          },
        },
      },
    });
  }

  async findByCompanyId(companyId: string) {
    const settings = await this.prisma.companySettings.findUnique({
      where: { companyId },
      include: {
        company: {
          select: {
            id: true,
            companyName: true,
          },
        },
      },
    });

    if (!settings) {
      // Créer des paramètres par défaut si ils n'existent pas
      return this.createDefaultSettings(companyId);
    }

    return settings;
  }

  async update(companyId: string, updateSettingsDto: UpdateCompanySettingsDto) {
    const existingSettings = await this.prisma.companySettings.findUnique({
      where: { companyId },
    });

    if (!existingSettings) {
      // Créer les paramètres s'ils n'existent pas
      return this.createCompanySettings({ ...updateSettingsDto, companyId });
    }

    return this.prisma.companySettings.update({
      where: { companyId },
      data: updateSettingsDto,
      include: {
        company: {
          select: {
            id: true,
            companyName: true,
          },
        },
      },
    });
  }

  async remove(companyId: string) {
    const settings = await this.prisma.companySettings.findUnique({
      where: { companyId },
    });

    if (!settings) {
      throw new NotFoundException(`Settings not found for company ${companyId}`);
    }

    return this.prisma.companySettings.delete({
      where: { companyId },
    });
  }

  // Gestion de la configuration de paie
  async updatePayrollConfiguration(companyId: string, payrollConfig: PayrollConfigurationDto) {
    // Récupérer la configuration de paie existante
    const existingConfig = await this.prisma.payrollConfiguration.findUnique({
      where: { companyId },
    });

    if (existingConfig) {
      return this.prisma.payrollConfiguration.update({
        where: { companyId },
        data: payrollConfig,
      });
    } else {
      return this.prisma.payrollConfiguration.create({
        data: {
          ...payrollConfig,
          company: { connect: { id: companyId } },
        },
      });
    }
  }

  async getPayrollConfiguration(companyId: string) {
    const config = await this.prisma.payrollConfiguration.findUnique({
      where: { companyId },
      include: {
        company: {
          select: {
            id: true,
            companyName: true,
          },
        },
      },
    });

    if (!config) {
      // Créer une configuration par défaut
      return this.createDefaultPayrollConfiguration(companyId);
    }

    return config;
  }

  // Gestion de la configuration fiscale
  async updateTaxConfiguration(companyId: string, taxConfig: TaxConfigurationDto) {
    const existingConfig = await this.prisma.companyTaxSettings.findUnique({
      where: { companyId },
    });

    if (existingConfig) {
      return this.prisma.companyTaxSettings.update({
        where: { companyId },
        data: taxConfig,
      });
    } else {
      return this.prisma.companyTaxSettings.create({
        data: {
          ...taxConfig,
          company: { connect: { id: companyId } },
        },
      });
    }
  }

  async getTaxConfiguration(companyId: string) {
    const config = await this.prisma.companyTaxSettings.findUnique({
      where: { companyId },
      include: {
        company: {
          select: {
            id: true,
            companyName: true,
          },
        },
      },
    });

    if (!config) {
      return this.createDefaultTaxConfiguration(companyId);
    }

    return config;
  }

  // Paramètres de notification
  async updateNotificationSettings(companyId: string, notificationSettings: NotificationSettingsDto) {
    // Pour l'instant, on stocke dans les paramètres généraux de l'entreprise
    // Dans une version future, on pourrait créer une table séparée
    const settings = await this.findByCompanyId(companyId);
    
    // Simuler la mise à jour des paramètres de notification
    return {
      ...settings,
      notificationSettings,
      updatedAt: new Date(),
    };
  }

  async getNotificationSettings(companyId: string) {
    const settings = await this.findByCompanyId(companyId);
    
    // Retourner des paramètres de notification par défaut
    return {
      companyId,
      birthdayNotifications: true,
      contractExpiryNotifications: true,
      contractExpiryNoticeDays: 30,
      leaveRequestNotifications: true,
      newApplicationNotifications: true,
      automaticReportNotifications: true,
      systemNotificationEmail: settings.company?.email || '',
    };
  }

  // Paramètres de sécurité
  async updateSecuritySettings(companyId: string, securitySettings: SecuritySettingsDto) {
    const settings = await this.findByCompanyId(companyId);
    
    // Simuler la mise à jour des paramètres de sécurité
    return {
      ...settings,
      securitySettings,
      updatedAt: new Date(),
    };
  }

  async getSecuritySettings(companyId: string) {
    const settings = await this.findByCompanyId(companyId);
    
    // Retourner des paramètres de sécurité par défaut
    return {
      companyId,
      maxLoginAttempts: 5,
      lockoutDuration: 30,
      passwordExpiryDays: 90,
      passwordHistoryCount: 5,
      allowedIpAddresses: '',
      auditLogins: true,
    };
  }

  // Réinitialiser aux paramètres par défaut
  async resetToDefaults(companyId: string) {
    const defaultSettings = this.getDefaultSettings();
    
    return this.update(companyId, defaultSettings);
  }

  // Export/Import des paramètres
  async exportSettings(companyId: string) {
    const [
      generalSettings,
      payrollConfig,
      taxConfig,
      notificationSettings,
      securitySettings,
    ] = await Promise.all([
      this.findByCompanyId(companyId),
      this.getPayrollConfiguration(companyId),
      this.getTaxConfiguration(companyId),
      this.getNotificationSettings(companyId),
      this.getSecuritySettings(companyId),
    ]);

    return {
      general: generalSettings,
      payroll: payrollConfig,
      tax: taxConfig,
      notifications: notificationSettings,
      security: securitySettings,
      exportedAt: new Date(),
    };
  }

  async importSettings(companyId: string, settingsData: any) {
    const results = [];

    try {
      if (settingsData.general) {
        const general = await this.update(companyId, settingsData.general);
        results.push({ type: 'general', success: true, data: general });
      }

      if (settingsData.payroll) {
        const payroll = await this.updatePayrollConfiguration(companyId, settingsData.payroll);
        results.push({ type: 'payroll', success: true, data: payroll });
      }

      if (settingsData.tax) {
        const tax = await this.updateTaxConfiguration(companyId, settingsData.tax);
        results.push({ type: 'tax', success: true, data: tax });
      }

      if (settingsData.notifications) {
        const notifications = await this.updateNotificationSettings(companyId, settingsData.notifications);
        results.push({ type: 'notifications', success: true, data: notifications });
      }

      if (settingsData.security) {
        const security = await this.updateSecuritySettings(companyId, settingsData.security);
        results.push({ type: 'security', success: true, data: security });
      }

      return {
        success: true,
        imported: results.length,
        results,
        importedAt: new Date(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        partialResults: results,
        importedAt: new Date(),
      };
    }
  }

  // Méthodes privées pour créer des configurations par défaut

  private async createDefaultSettings(companyId: string) {
    const defaultSettings = this.getDefaultSettings();
    
    return this.prisma.companySettings.create({
      data: {
        ...defaultSettings,
        company: { connect: { id: companyId } },
      },
      include: {
        company: {
          select: {
            id: true,
            companyName: true,
          },
        },
      },
    });
  }

  private async createDefaultPayrollConfiguration(companyId: string) {
    const defaultConfig = {
      payDay: 30,
      autoCalculateOvertime: true,
      overtimeRate: 1.5,
      overtimeThreshold: 40,
      autoCalculateTax: true,
      defaultTaxRate: 15,
      employerSocialContribution: 8.5,
      employeeSocialContribution: 3.5,
    };

    return this.prisma.payrollConfiguration.create({
      data: {
        ...defaultConfig,
        company: { connect: { id: companyId } },
      },
    });
  }

  private async createDefaultTaxConfiguration(companyId: string) {
    const defaultConfig = {
      vatRate: 16, // TVA en RDC
      corporateTaxRate: 30, // Impôt sur les sociétés en RDC
      taxExemptionThreshold: 0,
      taxBrackets: [
        { min: 0, max: 50000, rate: 0 },
        { min: 50000, max: 200000, rate: 15 },
        { min: 200000, max: 500000, rate: 25 },
        { min: 500000, max: Infinity, rate: 30 },
      ],
    };

    return this.prisma.companyTaxSettings.create({
      data: {
        ...defaultConfig,
        company: { connect: { id: companyId } },
      },
    });
  }

  private getDefaultSettings() {
    return {
      workingDaysPerWeek: 5,
      workingHoursPerDay: 8.0,
      timeZone: 'Africa/Kinshasa',
      currency: 'CDF',
      language: 'fr',
      annualLeaveEntitlement: 21,
      sickLeaveEntitlement: 10,
      maternityLeaveEntitlement: 98, // 14 semaines selon la loi RDC
      emailNotifications: true,
      smsNotifications: false,
      passwordMinLength: 8,
      sessionTimeout: 30,
      twoFactorAuth: false,
    };
  }
}
