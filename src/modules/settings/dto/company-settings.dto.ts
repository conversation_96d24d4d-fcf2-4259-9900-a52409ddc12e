import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsUUID,
  IsN<PERSON>ber,
  Min,
  Max,
  IsIn,
} from 'class-validator';

export class CreateCompanySettingsDto {
  @ApiProperty({ description: 'ID de l\'entreprise' })
  @IsUUID()
  companyId: string;

  @ApiPropertyOptional({ description: 'Jours de travail par semaine', default: 5 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(7)
  workingDaysPerWeek?: number;

  @ApiPropertyOptional({ description: 'Heures de travail par jour', default: 8.0 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(24)
  workingHoursPerDay?: number;

  @ApiPropertyOptional({ description: 'Fuseau horaire', default: 'Africa/Kinshasa' })
  @IsOptional()
  @IsString()
  timeZone?: string;

  @ApiPropertyOptional({ description: 'Devise', default: 'CDF' })
  @IsOptional()
  @IsString()
  @IsIn(['CDF', 'USD', 'EUR'])
  currency?: string;

  @ApiPropertyOptional({ description: 'Langue', default: 'fr' })
  @IsOptional()
  @IsString()
  @IsIn(['fr', 'en', 'sw']) // français, anglais, swahili
  language?: string;

  @ApiPropertyOptional({ description: 'Jours de congés annuels', default: 21 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(365)
  annualLeaveEntitlement?: number;

  @ApiPropertyOptional({ description: 'Jours de congés maladie', default: 10 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(365)
  sickLeaveEntitlement?: number;

  @ApiPropertyOptional({ description: 'Jours de congé maternité', default: 98 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(365)
  maternityLeaveEntitlement?: number;

  @ApiPropertyOptional({ description: 'Notifications par email', default: true })
  @IsOptional()
  @IsBoolean()
  emailNotifications?: boolean;

  @ApiPropertyOptional({ description: 'Notifications par SMS', default: false })
  @IsOptional()
  @IsBoolean()
  smsNotifications?: boolean;

  @ApiPropertyOptional({ description: 'Longueur minimale du mot de passe', default: 8 })
  @IsOptional()
  @IsNumber()
  @Min(4)
  @Max(50)
  passwordMinLength?: number;

  @ApiPropertyOptional({ description: 'Timeout de session en minutes', default: 30 })
  @IsOptional()
  @IsNumber()
  @Min(5)
  @Max(1440) // 24 heures max
  sessionTimeout?: number;

  @ApiPropertyOptional({ description: 'Authentification à deux facteurs', default: false })
  @IsOptional()
  @IsBoolean()
  twoFactorAuth?: boolean;
}

export class UpdateCompanySettingsDto extends PartialType(CreateCompanySettingsDto) {
  @ApiPropertyOptional({ description: 'ID de l\'entreprise' })
  @IsOptional()
  @IsUUID()
  companyId?: string;
}

export class CompanySettingsResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  companyId: string;

  @ApiProperty()
  workingDaysPerWeek: number;

  @ApiProperty()
  workingHoursPerDay: number;

  @ApiProperty()
  timeZone: string;

  @ApiProperty()
  currency: string;

  @ApiProperty()
  language: string;

  @ApiProperty()
  annualLeaveEntitlement: number;

  @ApiProperty()
  sickLeaveEntitlement: number;

  @ApiProperty()
  maternityLeaveEntitlement: number;

  @ApiProperty()
  emailNotifications: boolean;

  @ApiProperty()
  smsNotifications: boolean;

  @ApiProperty()
  passwordMinLength: number;

  @ApiProperty()
  sessionTimeout: number;

  @ApiProperty()
  twoFactorAuth: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class PayrollConfigurationDto {
  @ApiPropertyOptional({ description: 'Jour de paie du mois (1-31)', default: 30 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(31)
  payDay?: number;

  @ApiPropertyOptional({ description: 'Calcul automatique des heures supplémentaires', default: true })
  @IsOptional()
  @IsBoolean()
  autoCalculateOvertime?: boolean;

  @ApiPropertyOptional({ description: 'Taux des heures supplémentaires (multiplicateur)', default: 1.5 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(3)
  overtimeRate?: number;

  @ApiPropertyOptional({ description: 'Seuil des heures supplémentaires par semaine', default: 40 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(168)
  overtimeThreshold?: number;

  @ApiPropertyOptional({ description: 'Calcul automatique des taxes', default: true })
  @IsOptional()
  @IsBoolean()
  autoCalculateTax?: boolean;

  @ApiPropertyOptional({ description: 'Taux d\'imposition par défaut (%)', default: 15 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  defaultTaxRate?: number;

  @ApiPropertyOptional({ description: 'Cotisations sociales employeur (%)', default: 8.5 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  employerSocialContribution?: number;

  @ApiPropertyOptional({ description: 'Cotisations sociales employé (%)', default: 3.5 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  employeeSocialContribution?: number;
}

export class TaxConfigurationDto {
  @ApiPropertyOptional({ description: 'Numéro d\'identification fiscale de l\'entreprise' })
  @IsOptional()
  @IsString()
  companyTaxId?: string;

  @ApiPropertyOptional({ description: 'Taux de TVA (%)', default: 16 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  vatRate?: number;

  @ApiPropertyOptional({ description: 'Taux d\'impôt sur les sociétés (%)', default: 30 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  corporateTaxRate?: number;

  @ApiPropertyOptional({ description: 'Seuil d\'exonération fiscale', default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  taxExemptionThreshold?: number;

  @ApiPropertyOptional({ description: 'Tranches d\'imposition', type: 'object' })
  @IsOptional()
  taxBrackets?: Array<{
    min: number;
    max: number;
    rate: number;
  }>;
}

export class NotificationSettingsDto {
  @ApiPropertyOptional({ description: 'Notifications d\'anniversaire', default: true })
  @IsOptional()
  @IsBoolean()
  birthdayNotifications?: boolean;

  @ApiPropertyOptional({ description: 'Notifications de fin de contrat', default: true })
  @IsOptional()
  @IsBoolean()
  contractExpiryNotifications?: boolean;

  @ApiPropertyOptional({ description: 'Jours d\'avance pour les notifications de fin de contrat', default: 30 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  contractExpiryNoticeDays?: number;

  @ApiPropertyOptional({ description: 'Notifications de demandes de congé', default: true })
  @IsOptional()
  @IsBoolean()
  leaveRequestNotifications?: boolean;

  @ApiPropertyOptional({ description: 'Notifications de nouvelles candidatures', default: true })
  @IsOptional()
  @IsBoolean()
  newApplicationNotifications?: boolean;

  @ApiPropertyOptional({ description: 'Notifications de rapports automatiques', default: true })
  @IsOptional()
  @IsBoolean()
  automaticReportNotifications?: boolean;

  @ApiPropertyOptional({ description: 'Email pour les notifications système' })
  @IsOptional()
  @IsString()
  systemNotificationEmail?: string;
}

export class SecuritySettingsDto {
  @ApiPropertyOptional({ description: 'Verrouillage de compte après échecs de connexion', default: 5 })
  @IsOptional()
  @IsNumber()
  @Min(3)
  @Max(20)
  maxLoginAttempts?: number;

  @ApiPropertyOptional({ description: 'Durée de verrouillage en minutes', default: 30 })
  @IsOptional()
  @IsNumber()
  @Min(5)
  @Max(1440)
  lockoutDuration?: number;

  @ApiPropertyOptional({ description: 'Expiration des mots de passe en jours', default: 90 })
  @IsOptional()
  @IsNumber()
  @Min(30)
  @Max(365)
  passwordExpiryDays?: number;

  @ApiPropertyOptional({ description: 'Historique des mots de passe à retenir', default: 5 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  passwordHistoryCount?: number;

  @ApiPropertyOptional({ description: 'Adresses IP autorisées (whitelist)' })
  @IsOptional()
  @IsString()
  allowedIpAddresses?: string;

  @ApiPropertyOptional({ description: 'Audit des connexions', default: true })
  @IsOptional()
  @IsBoolean()
  auditLogins?: boolean;
}
