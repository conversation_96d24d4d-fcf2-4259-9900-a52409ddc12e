import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Put,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { SettingsService } from './settings.service';
import {
  CreateCompanySettingsDto,
  UpdateCompanySettingsDto,
  CompanySettingsResponseDto,
  PayrollConfigurationDto,
  TaxConfigurationDto,
  NotificationSettingsDto,
  SecuritySettingsDto,
} from './dto/company-settings.dto';
import { JwtGuard } from '../shared/guards/jtw.guard';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Roles } from '../shared/decorators/roles.decorator';
import { RoleEnum } from '@prisma/client';

@ApiTags('Settings')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('api/v1/settings')
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  // ===== PARAMÈTRES GÉNÉRAUX =====

  @Post()
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Créer les paramètres d\'une entreprise' })
  @ApiResponse({
    status: 201,
    description: 'Paramètres créés avec succès',
    type: CompanySettingsResponseDto,
  })
  @ApiResponse({ status: 409, description: 'Paramètres déjà existants' })
  create(@Body() createSettingsDto: CreateCompanySettingsDto) {
    return this.settingsService.createCompanySettings(createSettingsDto);
  }

  @Get(':companyId')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer les paramètres d\'une entreprise' })
  @ApiResponse({
    status: 200,
    description: 'Paramètres récupérés avec succès',
    type: CompanySettingsResponseDto,
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  findOne(@Param('companyId') companyId: string) {
    return this.settingsService.findByCompanyId(companyId);
  }

  @Put(':companyId')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Modifier les paramètres d\'une entreprise' })
  @ApiResponse({
    status: 200,
    description: 'Paramètres modifiés avec succès',
    type: CompanySettingsResponseDto,
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  update(
    @Param('companyId') companyId: string,
    @Body() updateSettingsDto: UpdateCompanySettingsDto,
  ) {
    return this.settingsService.update(companyId, updateSettingsDto);
  }

  @Delete(':companyId')
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Supprimer les paramètres d\'une entreprise' })
  @ApiResponse({ status: 200, description: 'Paramètres supprimés avec succès' })
  @ApiResponse({ status: 404, description: 'Paramètres non trouvés' })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  remove(@Param('companyId') companyId: string) {
    return this.settingsService.remove(companyId);
  }

  @Post(':companyId/reset')
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Réinitialiser aux paramètres par défaut' })
  @ApiResponse({
    status: 200,
    description: 'Paramètres réinitialisés avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  resetToDefaults(@Param('companyId') companyId: string) {
    return this.settingsService.resetToDefaults(companyId);
  }

  // ===== CONFIGURATION DE PAIE =====

  @Get(':companyId/payroll')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer la configuration de paie' })
  @ApiResponse({
    status: 200,
    description: 'Configuration de paie récupérée avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  getPayrollConfiguration(@Param('companyId') companyId: string) {
    return this.settingsService.getPayrollConfiguration(companyId);
  }

  @Put(':companyId/payroll')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Modifier la configuration de paie' })
  @ApiResponse({
    status: 200,
    description: 'Configuration de paie modifiée avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  updatePayrollConfiguration(
    @Param('companyId') companyId: string,
    @Body() payrollConfig: PayrollConfigurationDto,
  ) {
    return this.settingsService.updatePayrollConfiguration(companyId, payrollConfig);
  }

  // ===== CONFIGURATION FISCALE =====

  @Get(':companyId/tax')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer la configuration fiscale' })
  @ApiResponse({
    status: 200,
    description: 'Configuration fiscale récupérée avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  getTaxConfiguration(@Param('companyId') companyId: string) {
    return this.settingsService.getTaxConfiguration(companyId);
  }

  @Put(':companyId/tax')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Modifier la configuration fiscale' })
  @ApiResponse({
    status: 200,
    description: 'Configuration fiscale modifiée avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  updateTaxConfiguration(
    @Param('companyId') companyId: string,
    @Body() taxConfig: TaxConfigurationDto,
  ) {
    return this.settingsService.updateTaxConfiguration(companyId, taxConfig);
  }

  // ===== PARAMÈTRES DE NOTIFICATION =====

  @Get(':companyId/notifications')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer les paramètres de notification' })
  @ApiResponse({
    status: 200,
    description: 'Paramètres de notification récupérés avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  getNotificationSettings(@Param('companyId') companyId: string) {
    return this.settingsService.getNotificationSettings(companyId);
  }

  @Put(':companyId/notifications')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Modifier les paramètres de notification' })
  @ApiResponse({
    status: 200,
    description: 'Paramètres de notification modifiés avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  updateNotificationSettings(
    @Param('companyId') companyId: string,
    @Body() notificationSettings: NotificationSettingsDto,
  ) {
    return this.settingsService.updateNotificationSettings(companyId, notificationSettings);
  }

  // ===== PARAMÈTRES DE SÉCURITÉ =====

  @Get(':companyId/security')
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer les paramètres de sécurité' })
  @ApiResponse({
    status: 200,
    description: 'Paramètres de sécurité récupérés avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  getSecuritySettings(@Param('companyId') companyId: string) {
    return this.settingsService.getSecuritySettings(companyId);
  }

  @Put(':companyId/security')
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Modifier les paramètres de sécurité' })
  @ApiResponse({
    status: 200,
    description: 'Paramètres de sécurité modifiés avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  updateSecuritySettings(
    @Param('companyId') companyId: string,
    @Body() securitySettings: SecuritySettingsDto,
  ) {
    return this.settingsService.updateSecuritySettings(companyId, securitySettings);
  }

  // ===== EXPORT/IMPORT =====

  @Get(':companyId/export')
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Exporter tous les paramètres' })
  @ApiResponse({
    status: 200,
    description: 'Paramètres exportés avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  exportSettings(@Param('companyId') companyId: string) {
    return this.settingsService.exportSettings(companyId);
  }

  @Post(':companyId/import')
  @Roles(RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Importer des paramètres' })
  @ApiResponse({
    status: 200,
    description: 'Paramètres importés avec succès',
  })
  @ApiParam({ name: 'companyId', description: 'ID de l\'entreprise' })
  importSettings(
    @Param('companyId') companyId: string,
    @Body() settingsData: any,
  ) {
    return this.settingsService.importSettings(companyId, settingsData);
  }
}
