import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';

import { IAM_ERRORS } from 'src/common/constantes/errors.contantes';
import { ROLES_KEY } from '../decorators/allowed-role.decorator';
import { RoleEnum } from '@prisma/client';
import { JwtPayload } from 'src/modules/iam/auth/email-password-auth/email-password-auth.service';
import { REQUEST_USER_KEY } from '../common/request-keys';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const contextRoles = this.reflector.getAllAndOverride<RoleEnum[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );
    if (!contextRoles) {
      return true;
    }
    const user: JwtPayload = context.switchToHttp().getRequest()[
      REQUEST_USER_KEY
    ];
    if (!user) {
      throw new UnauthorizedException(IAM_ERRORS.ERR_MISSING_TOKEN);
    }
    return contextRoles.some((role) => user.role === role);
  }
}
