import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CredentialDto {
  @IsNotEmpty()
  @IsEmail()
  @IsString()
  @ApiProperty({
    example: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@example.com',
    description: 'The email address of the user',
  })
  @IsOptional()
  email?: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    example: '+243977962086',
    description: 'The phone of the user',
  })
  @IsOptional()
  phone?: string;
}
