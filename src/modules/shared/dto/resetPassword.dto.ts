import { IsEmail, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ValidationErrors } from 'src/utils/validation-errors';

export class ResetPasswordDto {
  @IsNotEmpty({ message: ValidationErrors.emptyFieldError('password') })
  @ApiProperty({
    example: '123',
    description: 'otp sended',
  })
  otp: string;

  @IsNotEmpty({ message: ValidationErrors.emptyFieldError('password') })
  @ApiProperty({
    example: 'P@ssw0rd',
    description: 'The new password of the user',
  })
  password: string;

  @IsNotEmpty({ message: ValidationErrors.emptyFieldError('password') })
  @ApiProperty({
    example: 'P@ssw0rd',
    description: 'The confirm password password of the user',
  })
  confirmPassword: string;
}

export class Otp {
  @IsNotEmpty({ message: ValidationErrors.emptyFieldError('password') })
  @ApiProperty({
    example: '123',
    description: 'otp sended',
  })
  otp: string;
}

export class Email {
  @IsNotEmpty()
  @ApiProperty({
    example: 'samu<PERSON><PERSON><PERSON><PERSON>@gmail.com',
    description: 'email of user',
  })
  @IsEmail()
  email: string;
}
