import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { JwtPayload } from 'src/modules/iam/auth/email-password-auth/email-password-auth.service';

export const CurrentUser = createParamDecorator(
  async (data: keyof JwtPayload | undefined, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest();

    // Récupère le token JWT dans les en-têtes de la requête
    const authHeader = request.headers['authorization'];
    if (!authHeader) {
      throw new Error('Authorization header not found');
    }

    // Le token est de la forme "Bearer <token>", donc on extrait le token
    const token = authHeader.split(' ')[1];
    if (!token) {
      throw new Error('Token not found in the authorization header');
    }

    // Décode le token JWT pour obtenir les informations de l'utilisateur
    const jwtService = new JwtService({ secret: process.env.JWT_SECRET }); // Assure-toi d'utiliser ta clé secrète
    let decodedUser;
    try {
      decodedUser = jwtService.decode(token) as JwtPayload; // Décodage du token
    } catch (e) {
      throw new Error('Invalid token');
    }

    if (!decodedUser) {
      throw new Error('User not found in token');
    }

    // Si 'data' est fourni, retourne uniquement cette donnée spécifique
    if (data) {
      return decodedUser[data];
    }

    // Sinon, retourne l'objet utilisateur complet
    return decodedUser;
  },
);
