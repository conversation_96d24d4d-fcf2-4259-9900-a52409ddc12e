import { Test, TestingModule } from '@nestjs/testing';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import { MailService } from './mailer.service';
import { EmailType } from '../../iam/@types/emailType';

describe('MailService', () => {
  let mailService: MailService;
  let mailerService: MailerService;
  let configService: ConfigService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MailService,
        {
          provide: MailerService,
          useValue: {
            sendMail: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    mailService = module.get<MailService>(MailService);
    mailerService = module.get<MailerService>(MailerService);
    configService = module.get<ConfigService>(ConfigService);
  });
  afterAll(() => {
    jest.resetAllMocks();
  });

  describe('sendMail', () => {
    it('should send a confirmation email', async () => {
      // Arrange
      const mailData = {
        type: EmailType.USER_CONFIRMATION,
        user: {
          firstName: 'John',
          lastName: 'doe',
          email: '<EMAIL>',
        },
        token: '12345',
      };
      const baseUrl = 'http://localhost:3000/';
      jest.spyOn(configService, 'get').mockReturnValue(baseUrl);

      // Act
      await mailService.sendMail(mailData);

      // Assert
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: mailData.user.email,
        subject: 'Welcome to Tutorat',
        template: './confirmation',
        context: {
          name: mailData.user.firstName,
          url: `${baseUrl}verify-user?token=${mailData.token}`,
          email: mailData.user.email,
        },
      });
    });

    it('should send a password reset email', async () => {
      // Arrange
      const mailData = {
        type: EmailType.PASSWORD_RESET,
        user: {
          firstName: 'John',
          lastName: 'doe',
          email: '<EMAIL>',
        },
        token: '12345',
      };
      const baseUrl = 'http://localhost:3000/';
      jest.spyOn(configService, 'get').mockReturnValue(baseUrl);

      // Act
      await mailService.sendMail(mailData);

      // Assert
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: mailData.user.email,
        subject: 'Reset your password',
        template: './password-reset',
        context: {
          name: mailData.user.firstName,
          url: `${baseUrl}reset-password?token=${mailData.token}`,
        },
      });
    });

    it('should throw an error for an invalid mail type', async () => {
      // Arrange
      const mailData = {
        type: 'invalid' as EmailType,
        user: {
          firstName: 'John',
          email: '<EMAIL>',
          lastName: 'doe',
        },
        token: '12345',
      };

      // Act and Assert
      await expect(mailService.sendMail(mailData)).rejects.toThrowError(
        'Invalid mail type',
      );
    });
  });
});
