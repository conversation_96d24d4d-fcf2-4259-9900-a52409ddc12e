import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { Module } from '@nestjs/common';
import { join } from 'path';
import { MailService } from './mailer.service';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        // Vérification des variables d'environnement
        if (
          !configService.get('MAIL_SENDER') ||
          !configService.get('MAIL_PASSWORD')
        ) {
          throw new Error('Mail credentials are not configured properly');
        }

        return {
          transport: {
            host: configService.get('MAIL_HOST', 'smtp.gmail.com'),
            port: configService.get('MAIL_PORT', 465),
            secure: configService.get('MAIL_SECURE', true),
            auth: {
              user: configService.get('MAIL_SENDER'),
              pass: configService.get('MAIL_PASSWORD'),
            },
            tls: {
              rejectUnauthorized: false, // Pour les environnements de développement seulement
            },
          },
          defaults: {
            from: `"${configService.get(
              'MAIL_FROM_NAME',
              'LuminaRH Global',
            )}" <${configService.get('MAIL_FROM_ADDRESS')}>`,
          },
          template: {
            dir: join(__dirname, 'templates'),
            adapter: new HandlebarsAdapter(),
            options: {
              strict: true,
            },
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [MailService],
  exports: [MailService],
})
export class MailModule {}
