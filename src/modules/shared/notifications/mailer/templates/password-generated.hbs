<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="x-apple-disable-message-reformatting">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>LuminaRH Global - Nouveau mot de passe</title>
    <style type="text/css">
        /* Base Styles */
        body, table, td, a {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        /* Reset */
        body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            background-color: #f4f4f4;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.5;
            color: #333333;
        }
        
        /* Email Container */
        .email-container {
            max-width: 650px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            padding: 30px 20px;
            text-align: center;
            color: #ffffff;
            border-bottom: 6px solid #F59E0B;
        }
        
        .logo {
            height: 50px;
            margin-bottom: 15px;
        }
        
        .header-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: block;
        }
        
        .header-title {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
            line-height: 1.2;
            color: #ffffff;
        }
        
        /* Content */
        .content {
            padding: 30px;
            line-height: 1.6;
        }
        
        .security-badge {
            display: inline-block;
            background: linear-gradient(135deg, #F59E0B 0%, #EF4444 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 22px;
            font-weight: 700;
            margin: 0 0 20px;
            color: #1E293B;
        }
        
        p {
            margin: 0 0 15px;
            font-size: 16px;
            color: #64748B;
        }
        
        /* Password Section */
        .password-section {
            background: #EFF6FF;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            text-align: center;
            border: 1px solid #DBEAFE;
        }
        
        .password-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 15px;
            color: #1E293B;
        }
        
        .password-display {
            background: #ffffff;
            border: 2px dashed #2563EB;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .password-label {
            font-size: 14px;
            color: #64748B;
            font-weight: 600;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        
        .password-token {
            font-size: 22px;
            font-weight: 700;
            color: #2563EB;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            line-height: 1.4;
            background: #EFF6FF;
            padding: 12px;
            border-radius: 6px;
        }
        
        .validity-info {
            display: inline-block;
            margin: 15px 0;
            font-size: 14px;
            color: #F59E0B;
            font-weight: 600;
            background: #FEF3C7;
            padding: 10px 20px;
            border-radius: 20px;
        }
        
        /* Button */
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            color: #ffffff !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 15px 0;
        }
        
        /* Security Warning */
        .security-warning {
            background: #FEF2F2;
            border-left: 4px solid #EF4444;
            border-radius: 0 8px 8px 0;
            padding: 20px;
            margin: 25px 0;
            position: relative;
        }
        
        .security-title {
            font-size: 18px;
            font-weight: 700;
            color: #EF4444;
            margin: 0 0 10px;
        }
        
        .security-list {
            margin: 15px 0 0 0;
            padding: 0 0 0 25px;
        }
        
        .security-list li {
            margin-bottom: 8px;
            color: #64748B;
            font-size: 15px;
        }
        
        /* Contact Section */
        .contact-section {
            background: #F0FDF4;
            border-left: 4px solid #10B981;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .contact-title {
            font-weight: 600;
            color: #1E293B;
            margin: 0 0 10px;
        }
        
        /* Footer */
        .footer {
            padding: 25px;
            text-align: center;
            background: #F8FAFC;
            border-top: 1px solid #E2E8F0;
        }
        
        .social-icons {
            margin: 15px 0;
        }
        
        .social-icon {
            display: inline-block;
            margin: 0 8px;
            color: #2563EB;
            text-decoration: none;
            font-weight: 600;
        }
        
        .footer-text {
            font-size: 14px;
            color: #64748B;
            margin: 10px 0;
        }
        
        /* Responsive */
        @media screen and (max-width: 600px) {
            .email-container {
                border-radius: 0;
            }
            
            .header {
                padding: 25px 15px;
            }
            
            .header-title {
                font-size: 22px;
            }
            
            .content {
                padding: 20px;
            }
            
            .password-section {
                padding: 20px 15px;
            }
            
            .password-token {
                font-size: 20px;
                padding: 10px;
            }
            
            .cta-button {
                padding: 14px 24px;
                font-size: 15px;
            }
        }
        
        @media screen and (max-width: 480px) {
            .header-title {
                font-size: 20px;
            }
            
            h1 {
                font-size: 20px;
            }
            
            .password-token {
                font-size: 18px;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4;">
    <!-- Email Container -->
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 650px; margin: 0 auto;">
        <tr>
            <td style="padding: 20px;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <!-- Header -->
                    <tr>
                        <td class="header" style="border-radius: 16px 16px 0 0;">
                            <img src="https://res.cloudinary.com/dwskoyncb/image/upload/v1747252165/logo-b-w_qnxeue.png" class="logo" alt="LuminaRH Global" style="height: 50px;">
                            <div class="header-icon" style="font-size: 40px;">🔐</div>
                            <h1 class="header-title" style="margin: 0;">Réinitialisation Sécurisée</h1>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td class="content" style="background: #ffffff;">
                            <span class="security-badge" style="display: inline-block;">🛡️ Action Sécurisée</span>
                            
                            <h1 style="margin: 0 0 20px;">Bonjour <strong>{{name}}</strong></h1>
                            
                            <p style="margin: 0 0 15px;">Votre demande de réinitialisation de mot de passe a été <strong>traitée avec succès</strong>. Pour des raisons de sécurité, nous avons généré un mot de passe temporaire pour votre compte <strong>LuminaRH Global</strong>.</p>
                            
                            <!-- Password Section -->
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" class="password-section">
                                <tr>
                                    <td>
                                        <div class="password-title" style="margin: 0 0 15px;">🔑 Votre accès temporaire</div>
                                        
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" class="password-display">
                                            <tr>
                                                <td>
                                                    <div class="password-label" style="margin: 0 0 10px;">Mot de passe temporaire</div>
                                                    <div class="password-token" style="font-family: 'Courier New', monospace;">{{token}}</div>
                                                </td>
                                            </tr>
                                        </table>
                                        
                                        <div class="validity-info" style="display: inline-block;">⏰ Valable 24 heures uniquement</div>
                                        
                                        <br>
                                        <a href="{{myurl}}" class="cta-button" style="display: inline-block;">Accéder à mon compte</a>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Security Warning -->
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" class="security-warning">
                                <tr>
                                    <td>
                                        <div class="security-title" style="margin: 0 0 10px;">⚠️ Mesures de sécurité importantes</div>
                                        <ul class="security-list" style="margin: 15px 0 0 0; padding: 0 0 0 25px;">
                                            <li style="margin-bottom: 8px;">Changez immédiatement ce mot de passe après connexion</li>
                                            <li style="margin-bottom: 8px;">Accédez à "Mon Profil" → "Sécurité" pour la modification</li>
                                            <li style="margin-bottom: 8px;">Utilisez un mot de passe fort et unique</li>
                                            <li style="margin-bottom: 8px;">Ce mot de passe expire automatiquement dans 24h</li>
                                        </ul>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Contact Section -->
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" class="contact-section">
                                <tr>
                                    <td>
                                        <div class="contact-title" style="margin: 0 0 10px;">🆘 Besoin d'aide ?</div>
                                        <p style="margin: 0;">Si vous n'avez pas demandé cette réinitialisation ou rencontrez des difficultés, contactez immédiatement notre équipe support. Votre sécurité est notre priorité.</p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td class="footer" style="border-radius: 0 0 16px 16px;">
                            <div class="social-icons">
                                <a href="https://facebook.com/luminarhglobal" class="social-icon" style="display: inline-block; margin: 0 8px;">Facebook</a> | 
                                <a href="https://linkedin.com/company/luminarhglobal" class="social-icon" style="display: inline-block; margin: 0 8px;">LinkedIn</a> | 
                                <a href="https://twitter.com/luminarhglobal" class="social-icon" style="display: inline-block; margin: 0 8px;">Twitter</a>
                            </div>
                            
                            <p class="footer-text" style="margin: 10px 0;"><strong>© {{year}} LuminaRH Global</strong> - Tous droits réservés</p>
                            <p class="footer-text" style="margin: 10px 0; font-size: 12px;">Email automatique de sécurité - Ne pas répondre directement</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>