import { MailerService } from '@nestjs-modules/mailer';
import { Injectable } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';
import { EmailType } from '../../@types/emailType';

@Injectable()
export class MailService {
  constructor(
    private mailerService: MailerService,
    private readonly config: ConfigService,
  ) {}

  async sendMail(mailData: {
    type: EmailType;
    userEmail: string;
    userName: string;
    token?: string;
    message?: string;
    mysubject?: string;
    myurl?: string;
  }) {
    let subject = '';
    let template = '';
    let context: any = {
      name: mailData.userName,
      year: new Date().getFullYear(),
    };
    const baseUrl = this.config.get('CLIENT_URL');

    switch (mailData.type) {
      case EmailType.PASSWORD_GENERATED:
        subject = 'Votre nouveau mot de passe LuminaRH Global';
        template = 'password-generated';
        context = {
          ...context,
          token: mailData.token,
          myurl: `${baseUrl}auth/login`,
        };
        break;

      case EmailType.WELCOME:
        subject = 'Bienvenue sur LuminaRH Global';
        template = 'welcome';
        context = {
          ...context,
          email: mailData.userEmail,
          url: `${baseUrl}dashboard`,
        };
        break;

      // ... autres cas existants ...
    }

    await this.mailerService.sendMail({
      to: mailData.userEmail,
      subject: mailData.mysubject || subject,
      template,
      context,
    });
  }
}
