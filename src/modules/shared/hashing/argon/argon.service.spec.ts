import { ArgonService } from './argon.service';
import * as argon from 'argon2';

// Mock the 'argon2' library
jest.mock('argon2');

describe('ArgonService', () => {
  let argonService: ArgonService;

  beforeEach(() => {
    argonService = new ArgonService();
  });

  describe('hash', () => {
    it('should hash data using argon2', async () => {
      const data = 'password';
      const hashedData = 'hashed-password';

      // Mock the argon.hash function
      (argon.hash as jest.Mock).mockResolvedValue(hashedData);

      const result = await argonService.hash(data);

      expect(result).toBe(hashedData);
      expect(argon.hash).toHaveBeenCalledWith(data);
    });
  });

  describe('compare', () => {
    it('should compare data with encrypted string using argon2', async () => {
      const data = 'password';
      const encrypted = 'hashed-password';

      // Mock the argon.verify function
      (argon.verify as jest.Mock).mockResolvedValue(true);

      const result = await argonService.compare(data, encrypted);

      expect(result).toBe(true);
      expect(argon.verify).toHaveBeenCalledWith(data, encrypted);
    });
  });
});
