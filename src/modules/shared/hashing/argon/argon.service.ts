import { Injectable } from '@nestjs/common';
import { HashingService } from '../hashing.service';
import * as argon from 'argon2';

@Injectable()
export class ArgonService implements HashingService {
  async hash(data: string | Buffer): Promise<string> {
    return await argon.hash(data);
  }

  async compare(data: string | Buffer, encrypted: string): Promise<boolean> {
    return await argon.verify(<string>data, encrypted);
  }
}
