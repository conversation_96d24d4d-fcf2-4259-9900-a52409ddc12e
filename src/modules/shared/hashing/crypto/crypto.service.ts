import { Injectable } from '@nestjs/common';
import { HashingService } from '../hashing.service';
import { createHmac, randomBytes, timingSafeEqual } from 'crypto';

@Injectable()
export class CryptoHashingService implements HashingService {
  private readonly saltLength = 16;
  private readonly algorithm = 'sha256';

  async hash(password: string): Promise<string> {
    const salt = randomBytes(this.saltLength).toString('hex');
    const hash = createHmac(this.algorithm, salt)
      .update(password)
      .digest('hex');
    return `${salt}:${hash}`;
  }

  async compare(password: string, storedHash: string): Promise<boolean> {
    console.log(password,storedHash);
    
    const [salt, originalHash] = storedHash.split(':');
    const hash = createHmac(this.algorithm, salt)
      .update(password)
      .digest('hex');
    return timingSafeEqual(Buffer.from(hash), Buffer.from(originalHash));
  }
}
