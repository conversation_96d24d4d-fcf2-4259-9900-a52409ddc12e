import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../shared/db/prisma/prisma.service';
import { 
  CreateDocumentApprovalDto, 
  UpdateDocumentApprovalDto, 
  DocumentApprovalQueryDto,
  ApproveDocumentDto,
  RejectDocumentDto,
  DocumentApprovalStatsDto
} from './dto/document-approval.dto';
import { DocumentApprovalStatusEnum, Prisma } from '@prisma/client';

@Injectable()
export class DocumentApprovalsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createApprovalDto: CreateDocumentApprovalDto) {
    const { documentId, approverId, requesterId, ...approvalData } = createApprovalDto;

    // Vérifier que le document existe
    const document = await this.prisma.document.findUnique({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    // Vérifier que l'approbateur existe
    const approver = await this.prisma.employeeData.findUnique({
      where: { id: approverId },
    });

    if (!approver) {
      throw new NotFoundException(`Approver with ID ${approverId} not found`);
    }

    // Vérifier que le demandeur existe
    const requester = await this.prisma.employeeData.findUnique({
      where: { id: requesterId },
    });

    if (!requester) {
      throw new NotFoundException(`Requester with ID ${requesterId} not found`);
    }

    // Vérifier qu'il n'y a pas déjà une demande d'approbation en cours
    const existingApproval = await this.prisma.documentApproval.findFirst({
      where: {
        documentId,
        status: DocumentApprovalStatusEnum.PENDING,
      },
    });

    if (existingApproval) {
      throw new BadRequestException('Document already has a pending approval request');
    }

    return this.prisma.documentApproval.create({
      data: {
        ...approvalData,
        document: { connect: { id: documentId } },
        approver: { connect: { id: approverId } },
        requester: { connect: { id: requesterId } },
      },
      include: this.getApprovalInclude(),
    });
  }

  async findAll(query: DocumentApprovalQueryDto) {
    const { page = 1, limit = 10, ...filters } = query;
    const skip = (page - 1) * limit;

    const where: Prisma.DocumentApprovalWhereInput = {};

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.approverId) {
      where.approverId = filters.approverId;
    }

    if (filters.requesterId) {
      where.requesterId = filters.requesterId;
    }

    if (filters.documentId) {
      where.documentId = filters.documentId;
    }

    const [approvals, total] = await Promise.all([
      this.prisma.documentApproval.findMany({
        where,
        skip,
        take: limit,
        include: this.getApprovalInclude(),
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.documentApproval.count({ where }),
    ]);

    return {
      data: approvals,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string) {
    const approval = await this.prisma.documentApproval.findUnique({
      where: { id },
      include: this.getApprovalInclude(),
    });

    if (!approval) {
      throw new NotFoundException(`Document approval with ID ${id} not found`);
    }

    return approval;
  }

  async update(id: string, updateApprovalDto: UpdateDocumentApprovalDto) {
    const approval = await this.findOne(id);

    if (approval.status !== DocumentApprovalStatusEnum.PENDING) {
      throw new BadRequestException('Can only update pending approval requests');
    }

    const updateData: any = { ...updateApprovalDto };

    if (updateApprovalDto.status === DocumentApprovalStatusEnum.APPROVED) {
      updateData.approvedAt = new Date();
    } else if (updateApprovalDto.status === DocumentApprovalStatusEnum.REJECTED) {
      updateData.rejectedAt = new Date();
    }

    return this.prisma.documentApproval.update({
      where: { id },
      data: updateData,
      include: this.getApprovalInclude(),
    });
  }

  async approve(id: string, approveDto: ApproveDocumentDto) {
    return this.update(id, {
      status: DocumentApprovalStatusEnum.APPROVED,
      comments: approveDto.comments,
    });
  }

  async reject(id: string, rejectDto: RejectDocumentDto) {
    return this.update(id, {
      status: DocumentApprovalStatusEnum.REJECTED,
      comments: rejectDto.comments,
    });
  }

  async cancel(id: string) {
    const approval = await this.findOne(id);

    if (approval.status !== DocumentApprovalStatusEnum.PENDING) {
      throw new BadRequestException('Can only cancel pending approval requests');
    }

    return this.prisma.documentApproval.update({
      where: { id },
      data: { status: DocumentApprovalStatusEnum.CANCELLED },
      include: this.getApprovalInclude(),
    });
  }

  async getApprovalStats(approverId?: string): Promise<DocumentApprovalStatsDto> {
    const where: Prisma.DocumentApprovalWhereInput = approverId ? { approverId } : {};

    const [total, pending, approved, rejected, cancelled] = await Promise.all([
      this.prisma.documentApproval.count({ where }),
      this.prisma.documentApproval.count({ 
        where: { ...where, status: DocumentApprovalStatusEnum.PENDING } 
      }),
      this.prisma.documentApproval.count({ 
        where: { ...where, status: DocumentApprovalStatusEnum.APPROVED } 
      }),
      this.prisma.documentApproval.count({ 
        where: { ...where, status: DocumentApprovalStatusEnum.REJECTED } 
      }),
      this.prisma.documentApproval.count({ 
        where: { ...where, status: DocumentApprovalStatusEnum.CANCELLED } 
      }),
    ]);

    return {
      total,
      pending,
      approved,
      rejected,
      cancelled,
    };
  }

  async getPendingApprovals(approverId: string) {
    return this.prisma.documentApproval.findMany({
      where: {
        approverId,
        status: DocumentApprovalStatusEnum.PENDING,
      },
      include: this.getApprovalInclude(),
      orderBy: { createdAt: 'asc' },
    });
  }

  async getApprovalHistory(documentId: string) {
    return this.prisma.documentApproval.findMany({
      where: { documentId },
      include: this.getApprovalInclude(),
      orderBy: { createdAt: 'desc' },
    });
  }

  async bulkApprove(approvalIds: string[], comments?: string) {
    const results = [];

    for (const id of approvalIds) {
      try {
        const approval = await this.approve(id, { comments });
        results.push({ success: true, approval });
      } catch (error) {
        results.push({ 
          success: false, 
          error: error.message,
          approvalId: id 
        });
      }
    }

    return {
      total: approvalIds.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results,
    };
  }

  private getApprovalInclude(): Prisma.DocumentApprovalInclude {
    return {
      document: {
        select: {
          id: true,
          name: true,
          type: true,
          url: true,
          uploadedAt: true,
        },
      },
      approver: {
        include: {
          user: {
            select: {
              id: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          department: {
            select: {
              id: true,
              name: true,
            },
          },
          position: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      },
      requester: {
        include: {
          user: {
            select: {
              id: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          department: {
            select: {
              id: true,
              name: true,
            },
          },
          position: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      },
    };
  }
}
