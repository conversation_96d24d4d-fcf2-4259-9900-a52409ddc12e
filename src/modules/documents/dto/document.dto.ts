import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUUID,
  IsArray,
  IsNumber,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateDocumentDto {
  @ApiProperty({ description: 'Nom du document' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Type de document' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'URL du document' })
  @IsString()
  url: string;

  @ApiPropertyOptional({ description: 'Description du document' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'ID de l\'utilisateur' })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiPropertyOptional({ description: 'ID de l\'entreprise' })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiPropertyOptional({ description: 'ID de l\'employé' })
  @IsOptional()
  @IsUUID()
  employeeId?: string;

  @ApiPropertyOptional({ description: 'ID du contrat associé' })
  @IsOptional()
  @IsUUID()
  contractId?: string;

  @ApiPropertyOptional({ description: 'ID du template utilisé' })
  @IsOptional()
  @IsUUID()
  documentTemplateId?: string;
}

export class UpdateDocumentDto extends PartialType(CreateDocumentDto) {}

export class DocumentQueryDto {
  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Type de document' })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({ description: 'ID de l\'entreprise' })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiPropertyOptional({ description: 'ID de l\'employé' })
  @IsOptional()
  @IsUUID()
  employeeId?: string;

  @ApiPropertyOptional({ description: 'ID du contrat' })
  @IsOptional()
  @IsUUID()
  contractId?: string;

  @ApiPropertyOptional({ description: 'Recherche par nom' })
  @IsOptional()
  @IsString()
  search?: string;
}

export class DocumentResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  type: string;

  @ApiProperty()
  url: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiProperty()
  uploadedAt: Date;

  @ApiPropertyOptional()
  userId?: string;

  @ApiPropertyOptional()
  companyId?: string;

  @ApiPropertyOptional()
  employeeId?: string;

  @ApiPropertyOptional()
  contractId?: string;

  @ApiPropertyOptional()
  documentTemplateId?: string;
}

export class DocumentStatsDto {
  @ApiProperty()
  total: number;

  @ApiProperty()
  byType: Record<string, number>;

  @ApiProperty()
  recentUploads: number;

  @ApiProperty()
  pendingApprovals: number;
}

export class BulkUploadDocumentDto {
  @ApiProperty({ description: 'Liste des documents à uploader', type: [CreateDocumentDto] })
  @IsArray()
  documents: CreateDocumentDto[];
}
