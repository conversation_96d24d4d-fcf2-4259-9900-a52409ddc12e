import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUUID,
  IsEnum,
  IsNumber,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DocumentApprovalStatusEnum } from '@prisma/client';

export class CreateDocumentApprovalDto {
  @ApiProperty({ description: 'ID du document à approuver' })
  @IsUUID()
  documentId: string;

  @ApiProperty({ description: 'ID de l\'approbateur' })
  @IsUUID()
  approverId: string;

  @ApiProperty({ description: 'ID du demandeur' })
  @IsUUID()
  requesterId: string;

  @ApiPropertyOptional({ description: 'Commentaires sur la demande' })
  @IsOptional()
  @IsString()
  comments?: string;
}

export class UpdateDocumentApprovalDto {
  @ApiProperty({ 
    enum: DocumentApprovalStatusEnum,
    description: 'Nouveau statut de l\'approbation'
  })
  @IsEnum(DocumentApprovalStatusEnum)
  status: DocumentApprovalStatusEnum;

  @ApiPropertyOptional({ description: 'Commentaires sur la décision' })
  @IsOptional()
  @IsString()
  comments?: string;
}

export class DocumentApprovalQueryDto {
  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({ enum: DocumentApprovalStatusEnum })
  @IsOptional()
  @IsEnum(DocumentApprovalStatusEnum)
  status?: DocumentApprovalStatusEnum;

  @ApiPropertyOptional({ description: 'ID de l\'approbateur' })
  @IsOptional()
  @IsUUID()
  approverId?: string;

  @ApiPropertyOptional({ description: 'ID du demandeur' })
  @IsOptional()
  @IsUUID()
  requesterId?: string;

  @ApiPropertyOptional({ description: 'ID du document' })
  @IsOptional()
  @IsUUID()
  documentId?: string;
}

export class DocumentApprovalResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty({ enum: DocumentApprovalStatusEnum })
  status: DocumentApprovalStatusEnum;

  @ApiPropertyOptional()
  comments?: string;

  @ApiPropertyOptional()
  approvedAt?: Date;

  @ApiPropertyOptional()
  rejectedAt?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  documentId: string;

  @ApiProperty()
  approverId: string;

  @ApiProperty()
  requesterId: string;
}

export class ApproveDocumentDto {
  @ApiPropertyOptional({ description: 'Commentaires sur l\'approbation' })
  @IsOptional()
  @IsString()
  comments?: string;
}

export class RejectDocumentDto {
  @ApiProperty({ description: 'Raison du rejet' })
  @IsString()
  comments: string;
}

export class DocumentApprovalStatsDto {
  @ApiProperty()
  total: number;

  @ApiProperty()
  pending: number;

  @ApiProperty()
  approved: number;

  @ApiProperty()
  rejected: number;

  @ApiProperty()
  cancelled: number;
}
