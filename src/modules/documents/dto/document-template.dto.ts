import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsUUID,
} from 'class-validator';

export class CreateDocumentTemplateDto {
  @ApiProperty({ description: 'Nom du template' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Description du template' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Contenu du template' })
  @IsString()
  content: string;

  @ApiProperty({ description: 'Catégorie du template (HR, Legal, Finance, etc.)' })
  @IsString()
  category: string;

  @ApiProperty({ description: 'ID de l\'entreprise' })
  @IsUUID()
  companyId: string;

  @ApiPropertyOptional({ description: 'Template actif', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateDocumentTemplateDto extends PartialType(CreateDocumentTemplateDto) {}

export class DocumentTemplateResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiProperty()
  content: string;

  @ApiProperty()
  category: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  companyId: string;
}

export class DocumentTemplateQueryDto {
  @ApiPropertyOptional({ description: 'Catégorie du template' })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ description: 'Filtrer par templates actifs' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: 'ID de l\'entreprise' })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiPropertyOptional({ description: 'Recherche par nom' })
  @IsOptional()
  @IsString()
  search?: string;
}

export class GenerateDocumentFromTemplateDto {
  @ApiProperty({ description: 'ID du template' })
  @IsUUID()
  templateId: string;

  @ApiProperty({ description: 'Variables pour remplacer les placeholders' })
  variables: Record<string, any>;

  @ApiPropertyOptional({ description: 'Nom du document généré' })
  @IsOptional()
  @IsString()
  documentName?: string;
}
