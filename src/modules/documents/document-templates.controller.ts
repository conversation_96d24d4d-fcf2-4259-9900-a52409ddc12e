import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { DocumentTemplatesService } from './document-templates.service';
import { DocumentApprovalsService } from './document-approvals.service';
import {
  CreateDocumentTemplateDto,
  UpdateDocumentTemplateDto,
  DocumentTemplateResponseDto,
  DocumentTemplateQueryDto,
  GenerateDocumentFromTemplateDto,
} from './dto/document-template.dto';
import {
  CreateDocumentApprovalDto,
  UpdateDocumentApprovalDto,
  DocumentApprovalQueryDto,
  DocumentApprovalResponseDto,
  ApproveDocumentDto,
  RejectDocumentDto,
  DocumentApprovalStatsDto,
} from './dto/document-approval.dto';
import { JwtGuard } from '../shared/guards/jtw.guard';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Roles } from '../shared/decorators/roles.decorator';
import { RoleEnum } from '@prisma/client';

@ApiTags('Document Templates & Approvals')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('api/v1/documents')
export class DocumentTemplatesController {
  constructor(
    private readonly templatesService: DocumentTemplatesService,
    private readonly approvalsService: DocumentApprovalsService,
  ) {}

  // ===== TEMPLATES =====

  @Post('templates')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Créer un nouveau template de document' })
  @ApiResponse({
    status: 201,
    description: 'Template créé avec succès',
    type: DocumentTemplateResponseDto,
  })
  createTemplate(@Body() createTemplateDto: CreateDocumentTemplateDto) {
    return this.templatesService.create(createTemplateDto);
  }

  @Get('templates')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer tous les templates de documents' })
  @ApiResponse({
    status: 200,
    description: 'Templates récupérés avec succès',
    type: [DocumentTemplateResponseDto],
  })
  findAllTemplates(@Query() query: DocumentTemplateQueryDto) {
    return this.templatesService.findAll(query);
  }

  @Get('templates/categories')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer toutes les catégories de templates' })
  @ApiResponse({
    status: 200,
    description: 'Catégories récupérées avec succès',
  })
  @ApiQuery({ name: 'companyId', required: false })
  getTemplateCategories(@Query('companyId') companyId?: string) {
    return this.templatesService.getTemplateCategories(companyId);
  }

  @Get('templates/category/:category')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer templates par catégorie' })
  @ApiResponse({
    status: 200,
    description: 'Templates de la catégorie récupérés',
  })
  @ApiQuery({ name: 'companyId', required: false })
  getTemplatesByCategory(
    @Param('category') category: string,
    @Query('companyId') companyId?: string,
  ) {
    return this.templatesService.getTemplatesByCategory(category, companyId);
  }

  @Post('templates/:id/generate')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Générer un document à partir d\'un template' })
  @ApiResponse({
    status: 200,
    description: 'Document généré avec succès',
  })
  generateFromTemplate(
    @Param('id') templateId: string,
    @Body() generateDto: GenerateDocumentFromTemplateDto,
  ) {
    return this.templatesService.generateDocument(templateId, generateDto.variables);
  }

  @Post('templates/:id/duplicate')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Dupliquer un template' })
  @ApiResponse({
    status: 201,
    description: 'Template dupliqué avec succès',
  })
  duplicateTemplate(
    @Param('id') id: string,
    @Body('newName') newName: string,
  ) {
    return this.templatesService.duplicateTemplate(id, newName);
  }

  @Get('templates/:id/stats')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Statistiques d\'utilisation d\'un template' })
  @ApiResponse({
    status: 200,
    description: 'Statistiques récupérées avec succès',
  })
  getTemplateStats(@Param('id') id: string) {
    return this.templatesService.getTemplateUsageStats(id);
  }

  @Get('templates/:id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer un template par ID' })
  @ApiResponse({
    status: 200,
    description: 'Template récupéré avec succès',
    type: DocumentTemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Template non trouvé' })
  findOneTemplate(@Param('id') id: string) {
    return this.templatesService.findOne(id);
  }

  @Patch('templates/:id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Modifier un template' })
  @ApiResponse({
    status: 200,
    description: 'Template modifié avec succès',
    type: DocumentTemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Template non trouvé' })
  updateTemplate(
    @Param('id') id: string,
    @Body() updateTemplateDto: UpdateDocumentTemplateDto,
  ) {
    return this.templatesService.update(id, updateTemplateDto);
  }

  @Delete('templates/:id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Supprimer un template' })
  @ApiResponse({ status: 200, description: 'Template supprimé avec succès' })
  @ApiResponse({ status: 404, description: 'Template non trouvé' })
  removeTemplate(@Param('id') id: string) {
    return this.templatesService.remove(id);
  }

  // ===== APPROVALS =====

  @Post('approvals')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Créer une demande d\'approbation' })
  @ApiResponse({
    status: 201,
    description: 'Demande d\'approbation créée avec succès',
    type: DocumentApprovalResponseDto,
  })
  createApproval(@Body() createApprovalDto: CreateDocumentApprovalDto) {
    return this.approvalsService.create(createApprovalDto);
  }

  @Get('approvals')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer toutes les demandes d\'approbation' })
  @ApiResponse({
    status: 200,
    description: 'Demandes d\'approbation récupérées avec succès',
  })
  findAllApprovals(@Query() query: DocumentApprovalQueryDto) {
    return this.approvalsService.findAll(query);
  }

  @Get('approvals/stats')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Statistiques des approbations' })
  @ApiResponse({
    status: 200,
    description: 'Statistiques récupérées avec succès',
    type: DocumentApprovalStatsDto,
  })
  @ApiQuery({ name: 'approverId', required: false })
  getApprovalStats(@Query('approverId') approverId?: string) {
    return this.approvalsService.getApprovalStats(approverId);
  }

  @Get('approvals/pending/:approverId')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Approbations en attente pour un approbateur' })
  @ApiResponse({
    status: 200,
    description: 'Approbations en attente récupérées',
  })
  getPendingApprovals(@Param('approverId') approverId: string) {
    return this.approvalsService.getPendingApprovals(approverId);
  }

  @Get('approvals/history/:documentId')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Historique des approbations d\'un document' })
  @ApiResponse({
    status: 200,
    description: 'Historique récupéré avec succès',
  })
  getApprovalHistory(@Param('documentId') documentId: string) {
    return this.approvalsService.getApprovalHistory(documentId);
  }

  @Post('approvals/:id/approve')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Approuver un document' })
  @ApiResponse({
    status: 200,
    description: 'Document approuvé avec succès',
  })
  approveDocument(
    @Param('id') id: string,
    @Body() approveDto: ApproveDocumentDto,
  ) {
    return this.approvalsService.approve(id, approveDto);
  }

  @Post('approvals/:id/reject')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Rejeter un document' })
  @ApiResponse({
    status: 200,
    description: 'Document rejeté avec succès',
  })
  rejectDocument(
    @Param('id') id: string,
    @Body() rejectDto: RejectDocumentDto,
  ) {
    return this.approvalsService.reject(id, rejectDto);
  }

  @Post('approvals/:id/cancel')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Annuler une demande d\'approbation' })
  @ApiResponse({
    status: 200,
    description: 'Demande annulée avec succès',
  })
  cancelApproval(@Param('id') id: string) {
    return this.approvalsService.cancel(id);
  }

  @Post('approvals/bulk-approve')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Approuver plusieurs documents en lot' })
  @ApiResponse({
    status: 200,
    description: 'Approbations en lot effectuées',
  })
  bulkApprove(
    @Body('approvalIds') approvalIds: string[],
    @Body('comments') comments?: string,
  ) {
    return this.approvalsService.bulkApprove(approvalIds, comments);
  }

  @Get('approvals/:id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer une approbation par ID' })
  @ApiResponse({
    status: 200,
    description: 'Approbation récupérée avec succès',
    type: DocumentApprovalResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Approbation non trouvée' })
  findOneApproval(@Param('id') id: string) {
    return this.approvalsService.findOne(id);
  }
}
