import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../shared/db/prisma/prisma.service';
import { 
  CreateDocumentTemplateDto, 
  UpdateDocumentTemplateDto, 
  DocumentTemplateQueryDto,
  GenerateDocumentFromTemplateDto
} from './dto/document-template.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class DocumentTemplatesService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createTemplateDto: CreateDocumentTemplateDto) {
    const { companyId, ...templateData } = createTemplateDto;

    // Vérifier que l'entreprise existe
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    return this.prisma.documentTemplate.create({
      data: {
        ...templateData,
        company: { connect: { id: companyId } },
      },
    });
  }

  async findAll(query: DocumentTemplateQueryDto = {}) {
    const { search, ...filters } = query;
    const where: Prisma.DocumentTemplateWhereInput = {};

    if (filters.category) {
      where.category = filters.category;
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters.companyId) {
      where.companyId = filters.companyId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { category: { contains: search, mode: 'insensitive' } },
      ];
    }

    return this.prisma.documentTemplate.findMany({
      where,
      include: {
        company: {
          select: {
            id: true,
            companyName: true,
          },
        },
        documents: {
          select: {
            id: true,
            name: true,
            uploadedAt: true,
          },
          take: 5,
          orderBy: { uploadedAt: 'desc' },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: string) {
    const template = await this.prisma.documentTemplate.findUnique({
      where: { id },
      include: {
        company: {
          select: {
            id: true,
            companyName: true,
          },
        },
        documents: {
          select: {
            id: true,
            name: true,
            uploadedAt: true,
          },
          orderBy: { uploadedAt: 'desc' },
        },
      },
    });

    if (!template) {
      throw new NotFoundException(`Document template with ID ${id} not found`);
    }

    return template;
  }

  async update(id: string, updateTemplateDto: UpdateDocumentTemplateDto) {
    await this.findOne(id);

    return this.prisma.documentTemplate.update({
      where: { id },
      data: updateTemplateDto,
    });
  }

  async remove(id: string) {
    const template = await this.findOne(id);

    // Vérifier s'il y a des documents utilisant ce template
    const documentsCount = await this.prisma.document.count({
      where: { documentTemplateId: id },
    });

    if (documentsCount > 0) {
      // Plutôt que de supprimer, désactiver le template
      return this.prisma.documentTemplate.update({
        where: { id },
        data: { isActive: false },
      });
    }

    return this.prisma.documentTemplate.delete({
      where: { id },
    });
  }

  async generateDocument(templateId: string, variables: Record<string, any>) {
    const template = await this.findOne(templateId);
    
    let content = template.content;
    
    // Remplacer les placeholders par les variables
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      content = content.replace(new RegExp(placeholder, 'g'), String(value));
    });

    return {
      content,
      templateId: template.id,
      templateName: template.name,
      category: template.category,
      generatedAt: new Date(),
    };
  }

  async getTemplatesByCategory(category: string, companyId?: string) {
    const where: Prisma.DocumentTemplateWhereInput = {
      category,
      isActive: true,
    };

    if (companyId) {
      where.companyId = companyId;
    }

    return this.prisma.documentTemplate.findMany({
      where,
      orderBy: { name: 'asc' },
    });
  }

  async getTemplateCategories(companyId?: string) {
    const where: Prisma.DocumentTemplateWhereInput = {
      isActive: true,
    };

    if (companyId) {
      where.companyId = companyId;
    }

    const templates = await this.prisma.documentTemplate.findMany({
      where,
      select: { category: true },
      distinct: ['category'],
    });

    return templates.map(t => t.category).sort();
  }

  async duplicateTemplate(id: string, newName: string) {
    const template = await this.findOne(id);

    const { id: _, createdAt, updatedAt, ...templateData } = template;

    return this.prisma.documentTemplate.create({
      data: {
        ...templateData,
        name: newName,
      },
    });
  }

  async getTemplateUsageStats(id: string) {
    const template = await this.findOne(id);

    const [totalDocuments, recentDocuments] = await Promise.all([
      this.prisma.document.count({
        where: { documentTemplateId: id },
      }),
      this.prisma.document.count({
        where: {
          documentTemplateId: id,
          uploadedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 jours
          },
        },
      }),
    ]);

    return {
      templateId: id,
      templateName: template.name,
      totalDocuments,
      recentDocuments,
      lastUsed: await this.getLastUsedDate(id),
    };
  }

  private async getLastUsedDate(templateId: string) {
    const lastDocument = await this.prisma.document.findFirst({
      where: { documentTemplateId: templateId },
      orderBy: { uploadedAt: 'desc' },
      select: { uploadedAt: true },
    });

    return lastDocument?.uploadedAt || null;
  }
}
