import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiQuery,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { DocumentsService } from './documents.service';
import {
  CreateDocumentDto,
  UpdateDocumentDto,
  DocumentQueryDto,
  DocumentResponseDto,
  DocumentStatsDto,
  BulkUploadDocumentDto,
} from './dto/document.dto';
import { JwtGuard } from '../shared/guards/jtw.guard';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Roles } from '../shared/decorators/roles.decorator';
import { RoleEnum } from '@prisma/client';

@ApiTags('Documents')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('api/v1/documents')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post()
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Créer un nouveau document' })
  @ApiResponse({
    status: 201,
    description: 'Document créé avec succès',
    type: DocumentResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  create(@Body() createDocumentDto: CreateDocumentDto) {
    return this.documentsService.create(createDocumentDto);
  }

  @Post('upload')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload un fichier document' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 201,
    description: 'Fichier uploadé avec succès',
  })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() metadata: any,
  ) {
    // TODO: Implémenter la logique d'upload de fichier
    // Ceci nécessiterait un service de stockage (AWS S3, local, etc.)
    
    const documentDto: CreateDocumentDto = {
      name: file.originalname,
      type: file.mimetype,
      url: `uploads/${file.filename}`, // URL temporaire
      description: metadata.description,
      companyId: metadata.companyId,
      employeeId: metadata.employeeId,
    };

    return this.documentsService.create(documentDto);
  }

  @Post('bulk-upload')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Upload multiple documents' })
  @ApiResponse({
    status: 201,
    description: 'Documents uploadés avec succès',
  })
  bulkUpload(@Body() bulkUploadDto: BulkUploadDocumentDto) {
    return this.documentsService.bulkUpload(bulkUploadDto);
  }

  @Get()
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer tous les documents' })
  @ApiResponse({
    status: 200,
    description: 'Liste des documents récupérée avec succès',
  })
  findAll(@Query() query: DocumentQueryDto) {
    return this.documentsService.findAll(query);
  }

  @Get('stats')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Statistiques des documents' })
  @ApiResponse({
    status: 200,
    description: 'Statistiques récupérées avec succès',
    type: DocumentStatsDto,
  })
  @ApiQuery({ name: 'companyId', required: false })
  getStats(@Query('companyId') companyId?: string) {
    return this.documentsService.getDocumentStats(companyId);
  }

  @Get('search')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Rechercher des documents' })
  @ApiResponse({
    status: 200,
    description: 'Résultats de recherche',
  })
  @ApiQuery({ name: 'q', description: 'Terme de recherche' })
  @ApiQuery({ name: 'companyId', required: false })
  search(
    @Query('q') searchTerm: string,
    @Query('companyId') companyId?: string,
  ) {
    return this.documentsService.searchDocuments(searchTerm, companyId);
  }

  @Get('by-type/:type')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer documents par type' })
  @ApiResponse({
    status: 200,
    description: 'Documents du type spécifié',
  })
  @ApiQuery({ name: 'companyId', required: false })
  getByType(
    @Param('type') type: string,
    @Query('companyId') companyId?: string,
  ) {
    return this.documentsService.getDocumentsByType(type, companyId);
  }

  @Get(':id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer un document par ID' })
  @ApiResponse({
    status: 200,
    description: 'Document récupéré avec succès',
    type: DocumentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Document non trouvé' })
  findOne(@Param('id') id: string) {
    return this.documentsService.findOne(id);
  }

  @Patch(':id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Modifier un document' })
  @ApiResponse({
    status: 200,
    description: 'Document modifié avec succès',
    type: DocumentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Document non trouvé' })
  update(@Param('id') id: string, @Body() updateDocumentDto: UpdateDocumentDto) {
    return this.documentsService.update(id, updateDocumentDto);
  }

  @Delete(':id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Supprimer un document' })
  @ApiResponse({ status: 200, description: 'Document supprimé avec succès' })
  @ApiResponse({ status: 404, description: 'Document non trouvé' })
  remove(@Param('id') id: string) {
    return this.documentsService.remove(id);
  }
}
