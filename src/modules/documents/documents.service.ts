import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../shared/db/prisma/prisma.service';
import { 
  CreateDocumentDto, 
  UpdateDocumentDto, 
  DocumentQueryDto,
  DocumentStatsDto,
  BulkUploadDocumentDto
} from './dto/document.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class DocumentsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createDocumentDto: CreateDocumentDto) {
    const { userId, companyId, employeeId, contractId, documentTemplateId, ...documentData } = createDocumentDto;

    // Validation des relations
    if (userId) {
      const user = await this.prisma.user.findUnique({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }
    }

    if (companyId) {
      const company = await this.prisma.company.findUnique({ where: { id: companyId } });
      if (!company) {
        throw new NotFoundException(`Company with ID ${companyId} not found`);
      }
    }

    if (employeeId) {
      const employee = await this.prisma.employeeData.findUnique({ where: { id: employeeId } });
      if (!employee) {
        throw new NotFoundException(`Employee with ID ${employeeId} not found`);
      }
    }

    return this.prisma.document.create({
      data: {
        ...documentData,
        user: userId ? { connect: { id: userId } } : undefined,
        company: companyId ? { connect: { id: companyId } } : undefined,
        employee: employeeId ? { connect: { id: employeeId } } : undefined,
        contract: contractId ? { connect: { id: contractId } } : undefined,
        documentTemplate: documentTemplateId ? { connect: { id: documentTemplateId } } : undefined,
      },
      include: this.getDocumentInclude(),
    });
  }

  async findAll(query: DocumentQueryDto) {
    const { page = 1, limit = 10, search, ...filters } = query;
    const skip = (page - 1) * limit;

    const where: Prisma.DocumentWhereInput = {};

    if (filters.type) {
      where.type = filters.type;
    }

    if (filters.companyId) {
      where.companyId = filters.companyId;
    }

    if (filters.employeeId) {
      where.employeeId = filters.employeeId;
    }

    if (filters.contractId) {
      where.contractId = filters.contractId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [documents, total] = await Promise.all([
      this.prisma.document.findMany({
        where,
        skip,
        take: limit,
        include: this.getDocumentInclude(),
        orderBy: { uploadedAt: 'desc' },
      }),
      this.prisma.document.count({ where }),
    ]);

    return {
      data: documents,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string) {
    const document = await this.prisma.document.findUnique({
      where: { id },
      include: this.getDocumentInclude(),
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    return document;
  }

  async update(id: string, updateDocumentDto: UpdateDocumentDto) {
    await this.findOne(id);

    return this.prisma.document.update({
      where: { id },
      data: updateDocumentDto,
      include: this.getDocumentInclude(),
    });
  }

  async remove(id: string) {
    await this.findOne(id);

    return this.prisma.document.delete({
      where: { id },
    });
  }

  async bulkUpload(bulkUploadDto: BulkUploadDocumentDto) {
    const results = [];
    
    for (const documentDto of bulkUploadDto.documents) {
      try {
        const document = await this.create(documentDto);
        results.push({ success: true, document });
      } catch (error) {
        results.push({ 
          success: false, 
          error: error.message,
          documentData: documentDto 
        });
      }
    }

    return {
      total: bulkUploadDto.documents.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results,
    };
  }

  async getDocumentStats(companyId?: string): Promise<DocumentStatsDto> {
    const where: Prisma.DocumentWhereInput = companyId ? { companyId } : {};

    const [total, documents, pendingApprovals] = await Promise.all([
      this.prisma.document.count({ where }),
      this.prisma.document.findMany({
        where,
        select: { type: true, uploadedAt: true },
      }),
      this.prisma.documentApproval.count({
        where: {
          status: 'PENDING',
          document: companyId ? { companyId } : undefined,
        },
      }),
    ]);

    // Compter par type
    const byType = documents.reduce((acc, doc) => {
      acc[doc.type] = (acc[doc.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Compter les uploads récents (derniers 7 jours)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentUploads = documents.filter(doc => doc.uploadedAt >= sevenDaysAgo).length;

    return {
      total,
      byType,
      recentUploads,
      pendingApprovals,
    };
  }

  async getDocumentsByType(type: string, companyId?: string) {
    const where: Prisma.DocumentWhereInput = { type };
    
    if (companyId) {
      where.companyId = companyId;
    }

    return this.prisma.document.findMany({
      where,
      include: this.getDocumentInclude(),
      orderBy: { uploadedAt: 'desc' },
    });
  }

  async searchDocuments(searchTerm: string, companyId?: string) {
    const where: Prisma.DocumentWhereInput = {
      OR: [
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { description: { contains: searchTerm, mode: 'insensitive' } },
        { type: { contains: searchTerm, mode: 'insensitive' } },
      ],
    };

    if (companyId) {
      where.companyId = companyId;
    }

    return this.prisma.document.findMany({
      where,
      include: this.getDocumentInclude(),
      orderBy: { uploadedAt: 'desc' },
    });
  }

  private getDocumentInclude(): Prisma.DocumentInclude {
    return {
      user: {
        select: {
          id: true,
          email: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      },
      company: {
        select: {
          id: true,
          companyName: true,
        },
      },
      employee: {
        include: {
          user: {
            select: {
              id: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
      },
      contract: {
        select: {
          id: true,
          contractNumber: true,
          title: true,
        },
      },
      documentTemplate: {
        select: {
          id: true,
          name: true,
          category: true,
        },
      },
      approvals: {
        include: {
          approver: {
            include: {
              user: {
                select: {
                  profile: {
                    select: {
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          },
          requester: {
            include: {
              user: {
                select: {
                  profile: {
                    select: {
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    };
  }
}
