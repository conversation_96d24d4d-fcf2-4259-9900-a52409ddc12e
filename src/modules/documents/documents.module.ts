import { Module } from '@nestjs/common';
import { DocumentsService } from './documents.service';
import { DocumentTemplatesService } from './document-templates.service';
import { DocumentApprovalsService } from './document-approvals.service';
import { DocumentsController } from './documents.controller';
import { DocumentTemplatesController } from './document-templates.controller';

@Module({
  controllers: [DocumentsController, DocumentTemplatesController],
  providers: [DocumentsService, DocumentTemplatesService, DocumentApprovalsService],
  exports: [DocumentsService, DocumentTemplatesService, DocumentApprovalsService],
})
export class DocumentsModule {}
