import {
  Body,
  Controller,
  Post,
  HttpCode,
  HttpStatus,
  Put,
  Get,
  UseGuards,
  Param,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';

import {
  Email,
  Otp,
  ResetPasswordDto,
} from 'src/modules/shared/dto/resetPassword.dto';
import {
  EmailPasswordAuthService,
  JwtPayload,
} from './email-password-auth/email-password-auth.service';
import { Public } from 'src/modules/shared/decorators/public.decorator';
import { SignInDto, TokenPairDto } from './email-password-auth/dto/signin.dto';
import { SignupDto } from './email-password-auth/dto/signup.dto';

import { CurrentUser } from 'src/modules/shared/decorators/current-user.decorator';
import { UpdateProfileDto } from './email-password-auth/dto/update-profile.dto';
import { RefreshTokensGuard } from 'src/modules/shared/guards/refresh-tokens.guard';
import { Roles } from 'src/modules/shared/decorators/allowed-role.decorator';
import { RolesGuard } from 'src/modules/shared/guards/role.guard';
import { RoleEnum } from '@prisma/client';
import { Request } from 'express';

@ApiTags('Auth')
@Controller('api/v1/auth')
export class AuthController {
  constructor(private readonly authService: EmailPasswordAuthService) {}

  @Public()
  @Post('signin')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'User sign in' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User signed in successfully',
    type: TokenPairDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials',
  })
  async signIn(
    @Body() signInDto: SignInDto,
    @Req() request: Request,
  ): Promise<TokenPairDto> {
    const userAgent = request.headers['user-agent'] || '';
    console.log(request.headers);
    const platform = this.getPlatformFromUserAgent(userAgent);
    return this.authService.signIn(signInDto, platform);
  }

  @Public()
  @Get('me')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'User information' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User signed in successfully',
    type: TokenPairDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials',
  })
  async me(@CurrentUser('id') id: string) {
    return this.authService.me(id);
  }

  @Public()
  @Post('signup')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'User sign up' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User signed up successfully',
    type: TokenPairDto,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'User already exists',
  })
  async signup(@Body() signupDto: SignupDto): Promise<TokenPairDto> {
    return this.authService.signup(signupDto);
  }

  @Public()
  @Post('password-reset/request')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request password reset OTP' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password reset OTP sent',
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User not found' })
  async sendOtpPasswordReset(@Body() email: Email) {
    return this.authService.sendOtpPasswordReset(email.email);
  }

  @Public()
  @Post('password-reset/validate-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Validate password reset OTP' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'OTP validated successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or expired OTP',
  })
  async validateResetOtp(@Body() otp: Otp) {
    return this.authService.validateResetOtp(otp.otp);
  }

  @Public()
  @Post('password-reset/reset')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Reset password' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password reset successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid OTP or passwords do not match',
  })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(
      resetPasswordDto.otp,
      resetPasswordDto,
    );
  }

  @Post('refresh-tokens')
  @UseGuards(RefreshTokensGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Refresh tokens' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tokens refreshed successfully',
    type: TokenPairDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid refresh token',
  })
  async refreshTokens(
    @CurrentUser() currentUser: JwtPayload,
  ): Promise<TokenPairDto> {
    return this.authService.refreshTokens(
      currentUser.email,
      currentUser.refreshToken,
    );
  }

  @Post('verify-account')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify user account' })
  @ApiBody({ type: String, required: true })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Account verified successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or expired verification token',
  })
  async verifyAccount(@CurrentUser('id') userId: string, @Body() otp: Otp) {
    return this.authService.verifyAccount(userId, otp.otp);
  }

  @Put('profile')
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Profile updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  async updateProfile(
    @CurrentUser('id') userId: string,
    @Body() updateProfileDto: UpdateProfileDto,
  ) {
    return this.authService.updateProfile(userId, updateProfileDto);
  }

  @Post('generate-password/:userId')
  @UseGuards(RolesGuard)
  @Roles(
    RoleEnum.ADMIN_HR,
    RoleEnum.ADMIN_RECRUITMENT,
    RoleEnum.SUPER_ADMIN,
    RoleEnum.OWNER,
  )
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Generate and send a new password to user',
    description:
      'Generates a random strong password for the specified user and sends it by email. Requires ADMIN or OWNER role.',
  })
  @ApiParam({
    name: 'userId',
    description: 'ID of the user to generate password for',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password generated and sent successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async generateAndSendPassword(@Param('userId') userId: string) {
    return this.authService.generateAndSendPassword(userId);
  }

  // Helper method to determine platform from user agent
  private getPlatformFromUserAgent(userAgent: string): 'web' | 'mobile' {
    // Utilisation d'une regex pour détecter les appareils mobiles et toutes les versions de okhttp
    return /Android|iPhone|iPad|Mobile|okhttp\//i.test(userAgent)
      ? 'mobile'
      : 'web';
  }
}
