import { ApiProperty } from '@nestjs/swagger';
import { RoleEnum } from '@prisma/client';
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class AddressDto {
  @ApiProperty({ description: 'Street address of the individual' })
  @IsNotEmpty()
  street: string;

  @ApiProperty({ description: 'City of the individual' })
  @IsNotEmpty()
  city: string;

  @ApiProperty({ description: 'Postal code of the individual' })
  @IsNotEmpty()
  postalCode: string;

  @ApiProperty({ description: 'Country of the individual' })
  @IsNotEmpty()
  country: string;
}

export class SignupDto {
  @ApiProperty({ description: 'First name of the individual' })
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'Last name of the individual' })
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    example: 'samuel<PERSON><PERSON><PERSON>@example.com',
    description: 'Email of the individual',
  })
  @IsNotEmpty()
  @IsEmail()
  @IsString()
  email: string;

  @ApiProperty({ description: 'Password of the individual' })
  @IsNotEmpty()
  password: string;

  @ApiProperty({ description: 'Confirmation password of the individual' })
  @IsNotEmpty()
  confirmPassword: string;

  @ApiProperty({ description: 'Birth date of the individual' })
  @IsNotEmpty()
  @IsOptional()
  birthDate?: Date;

  @ApiProperty({ type: AddressDto, description: 'Address of the individual' })
  @IsNotEmpty()
  @IsOptional()
  address?: AddressDto;

  @ApiProperty({ description: 'Role of the individual' })
  @IsNotEmpty()
  @IsEnum(RoleEnum)
  @IsOptional()
  role?: RoleEnum;
}
