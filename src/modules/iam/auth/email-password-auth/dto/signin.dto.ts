import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class SignInDto {
  @IsNotEmpty()
  @IsEmail()
  @IsString()
  @ApiProperty({
    example: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@example.com',
    description: 'The email address of the user',
  })
  @IsOptional()
  email?: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({ example: 'P@ssw0rd', description: 'The password of the user' })
  password: string;
}

export class TokenPairDto {
  @ApiProperty()
  accessToken: string;

  @ApiProperty()
  refreshToken: string;
}
