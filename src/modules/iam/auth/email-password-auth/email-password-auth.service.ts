import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService, ConfigType } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { randomUUID } from 'crypto';
import { addMinutes } from 'date-fns';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
import { MailService } from 'src/modules/shared/notifications/mailer/mailer.service';
import { TokenPair } from 'src/interfaces/response-with-token';
import { ActiveUserData } from 'src/interfaces/active-user-data.interface';
import { SignInDto } from './dto/signin.dto';
import { EmailType } from 'src/modules/shared/@types/emailType';
import jwtConfig from 'src/modules/shared/common/jwt-config';
import { ResetPasswordDto } from 'src/modules/shared/dto/resetPassword.dto';
import { SignupDto } from './dto/signup.dto';
import { RoleEnum } from '@prisma/client';
import {
  IAM_ERRORS,
  IAM_SERVICE_MESSAGES,
  MAIL_SERVICE_MESSAGES,
} from 'src/common/constantes/errors.contantes';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { CryptoHashingService } from 'src/modules/shared/hashing/crypto/crypto.service';

@Injectable()
export class EmailPasswordAuthService {
  constructor(
    private readonly config: ConfigService,
    private readonly jwtService: JwtService,
    private readonly mailService: MailService,
    private readonly prismaService: PrismaService,
    @Inject(jwtConfig.KEY)
    private readonly jwtConfiguration: ConfigType<typeof jwtConfig>,
    private readonly hashingService: CryptoHashingService,
  ) {}

  async signIn(
    signInDto: SignInDto,
    platform: 'web' | 'mobile' = 'web',
  ): Promise<TokenPair> {
    const user = await this.validateUser(signInDto);
    if (!user) {
      throw new UnauthorizedException(IAM_ERRORS.ERR_USER_NOT_FOUND);
    }

    if (platform === 'web' && user.role == RoleEnum.EMPLOYEE) {
      throw new ForbiddenException(
        'Employee  must use the mobile application to sign in',
      );
    }

    const tokens = await this.generateTokens(
      user.id,
      user.email,
      user.role,
      user.employeeData?.companyId,
    );
    return tokens;
  }

  async me(currentUserId: string) {
    const user = await this.prismaService.user.findUnique({
      where: { id: currentUserId },
      include: {
        applications: {
          include: {
            applicationResponses: true,
          },
        },
        employeeData: true,
        profile: {
          include: {
            address: true,
          },
        },
      },
    });
    if (!user) {
      throw new NotFoundException(IAM_ERRORS.ERR_USER_NOT_FOUND);
    }
    delete user.password;
    delete user.passwordReset;
    return user;
  }

  async signup(signupDto: SignupDto): Promise<TokenPair> {
    const existingUser = await this.prismaService.user.findUnique({
      where: { email: signupDto.email },
    });
    if (existingUser) {
      throw new ConflictException(IAM_ERRORS.ERR_EMAIL_IN_USE);
    }
    const { otp } = this.generateOTP();
    const hashedPassword = await this.hashingService.hash(signupDto.password);
    const newUser = await this.prismaService.user.create({
      data: {
        email: signupDto.email,
        password: hashedPassword,
        role: signupDto.role || RoleEnum.OWNER,
        verificationOtp: otp,
        profile: {
          create: {
            firstName: signupDto.firstName,
            lastName: signupDto.lastName,
          },
        },
      },
      include: {
        profile: true,
      },
    });
    await this.mailService.sendMail({
      type: EmailType.WELCOME,
      userEmail: newUser.email,
      userName: newUser.profile.firstName,
      token: otp,
    });

    const tokens = await this.generateTokens(
      newUser.id,
      newUser.email,
      newUser.role,
    );
    return tokens;
  }

  async sendOtpPasswordReset(email: string) {
    const { otp, expiresAt } = this.generateOTP();
    const user = await this.prismaService.user.findUnique({
      where: { email },
      include: {
        profile: true,
      },
    });
    if (!user) {
      throw new NotFoundException(IAM_ERRORS.ERR_USER_NOT_FOUND);
    }
    await this.prismaService.user.update({
      where: { id: user.id },
      data: {
        passwordReset: {
          otp,
          expiresAt: expiresAt.toString(),
        },
      },
    });

    await this.mailService.sendMail({
      type: EmailType.PASSWORD_RESET,
      userEmail: user.email,
      userName: user.profile.firstName,
      token: otp,
    });
    return { message: MAIL_SERVICE_MESSAGES.EMAIL_SEND };
  }

  async validateResetOtp(otp: string) {
    const user = await this.prismaService.user.findFirst({
      where: {
        passwordReset: {
          path: ['otp'],
          equals: otp,
        },
      },
    });

    if (!user) {
      throw new UnauthorizedException(IAM_ERRORS.ERR_OTP_INVALID);
    }

    if (new Date(user.passwordReset['expiresAt']) < new Date()) {
      throw new UnauthorizedException(IAM_ERRORS.ERR_OTP_EXPIRED);
    }
  }

  async resetPassword(otp: string, resetPasswordDto: ResetPasswordDto) {
    const user = await this.prismaService.user.findFirst({
      where: {
        passwordReset: {
          path: ['otp'],
          equals: otp,
        },
      },
    });

    if (!user) {
      throw new NotFoundException(IAM_ERRORS.ERR_USER_NOT_FOUND);
    }

    if (resetPasswordDto.password !== resetPasswordDto.confirmPassword) {
      throw new UnauthorizedException(IAM_ERRORS.ERR_PASS_CONFIRM_NOT_MATCH);
    }

    const hashedPassword = await this.hashingService.hash(
      resetPasswordDto.password,
    );
    await this.prismaService.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordReset: {},
      },
    });
    return { message: IAM_SERVICE_MESSAGES.RESET_PASSWORD_SUCCESS };
  }

  public generateToken() {
    const resetToken = randomUUID();
    const resetTokenExpiresAt = addMinutes(new Date(), 6);
    return { resetToken, resetTokenExpiresAt };
  }

  async validateUser(signInDto: SignInDto) {
    const user = await this.prismaService.user.findUnique({
      where: { email: signInDto.email },
      include: {
        employeeData: {
          select: {
            companyId: true,
          },
        },
      },
    });
    if (!user) {
      throw new NotFoundException(IAM_ERRORS.ERR_USER_NOT_FOUND);
    }
    const passwordMatches = await this.hashingService.compare(
      signInDto.password,
      user.password,
    );

    if (!passwordMatches) {
      throw new ForbiddenException(IAM_ERRORS.ERR_PASS_NOT_MATCH);
    }

    return user;
  }

  async refreshTokens(email: string, refreshToken: string) {
    try {
      await this.jwtService.verifyAsync<
        Pick<ActiveUserData, 'sub'> & { refreshTokenId: string }
      >(refreshToken, {
        secret: this.jwtConfiguration.secret,
      });

      const user = await this.prismaService.user.findUnique({
        where: { email },
        include: {
          employeeData: {
            select: {
              companyId: true,
            },
          },
        },
      });

      return this.generateTokens(
        user.id,
        user.email,
        user.role,
        user.employeeData?.companyId,
      );
    } catch (err) {
      throw new UnauthorizedException(IAM_ERRORS.ERR_ACCESS_TOKEN_INVALID);
    }
  }

  generateOTP() {
    let otp = '';
    for (let i = 0; i < 6; i++) {
      otp += Math.floor(Math.random() * 10);
    }

    const expiresAt = addMinutes(new Date(), 30);

    return { otp, expiresAt };
  }

  async generateTokens(
    userId: string,
    email: string,
    role: RoleEnum,
    companyId?: string,
  ): Promise<TokenPair> {
    const refreshTokenId = randomUUID();

    const jwtPayload: JwtPayload = {
      id: userId,
      email,
      role,
      companyId,
    };

    const jwtPayloadWithRt = {
      ...jwtPayload,
      refreshTokenId,
    };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(jwtPayload, {
        secret: this.config.get<string>('JWT_SECRET'),
        expiresIn: this.config.get<string>('JWT_ACCESS_TOKEN_TTL'),
      }),
      this.jwtService.signAsync(jwtPayloadWithRt, {
        secret: this.config.get<string>('RT_SECRET'),
        expiresIn: this.config.get<string>('JWT_REFRESH_TOKEN_TTL'),
      }),
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  async verifyAccount(userId: string, otp: string) {
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
    });
    if (user.verified) {
      throw new BadRequestException(
        IAM_SERVICE_MESSAGES.ACCOUNT_ALREADY_VERIFIED,
      );
    }
    if (!user || user.verificationOtp !== otp) {
      throw new BadRequestException(IAM_ERRORS.ERR_OTP_INVALID);
    }

    await this.prismaService.user.update({
      where: { id: userId },
      data: { verified: true, verificationOtp: null },
    });
  }

  async updateProfile(userId: string, updateData: UpdateProfileDto) {
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      include: {
        profile: {
          include: {
            address: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException(IAM_ERRORS.ERR_USER_NOT_FOUND);
    }

    const { address, ...others } = updateData;

    const userUpdated = await this.prismaService.user.update({
      where: { id: userId },
      data: {
        profile: {
          update: {
            ...others,
            address: address
              ? {
                  upsert: {
                    create: { ...address },
                    update: { ...address },
                  },
                }
              : undefined,
          },
        },
      },
      include: {
        profile: {
          include: {
            address: true,
          },
        },
      },
    });

    delete userUpdated.password;
    delete userUpdated.passwordReset;

    return userUpdated;
  }

  async generateAndSendPassword(userId: string): Promise<{ message: string }> {
    // 1. Vérifier que l'utilisateur existe
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
      },
    });

    if (!user) {
      throw new NotFoundException(IAM_ERRORS.ERR_USER_NOT_FOUND);
    }

    // 2. Générer un mot de passe aléatoire
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += characters.charAt(
        Math.floor(Math.random() * characters.length),
      );
    }

    // 3. Hasher et sauvegarder le nouveau mot de passe
    const hashedPassword = await this.hashingService.hash(password);
    await this.prismaService.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
      },
    });

    // 4. Envoyer le mot de passe par email
    await this.mailService.sendMail({
      type: EmailType.PASSWORD_GENERATED,
      userEmail: user.email,
      userName: user.profile?.firstName || 'Utilisateur',
      token: password, // On utilise le champ token pour transmettre le mot de passe
    });

    return { message: IAM_SERVICE_MESSAGES.PASSWORD_GENERATED_AND_SENT };
  }
}

export interface JwtPayload {
  id: string;
  email: string;
  role: RoleEnum;
  companyId: string;
  refreshToken?: string;
}
