import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AuthController } from './auth/auth.controller';
import { EmailPasswordAuthService } from './auth/email-password-auth/email-password-auth.service';
import { HashingService } from 'src/modules/shared/hashing/hashing.service';
import { JwtStrategy } from 'src/modules/shared/strategies/jwt.strategy';
import { RefreshTokensStrategy } from 'src/modules/shared/strategies/refresh-token.strategy';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import jwtConfig from 'src/modules/shared/common/jwt-config';
import { CryptoHashingService } from '../shared/hashing/crypto/crypto.service';

@Module({
  imports: [JwtModule.register({}), ConfigModule.forFeature(jwtConfig)],
  controllers: [AuthController],
  providers: [
    EmailPasswordAuthService,
    { provide: HashingService, useValue: CryptoHashingService },
    CryptoHashingService,
    JwtStrategy,
    RefreshTokensStrategy,
  ],
})
export class IamModule {}
