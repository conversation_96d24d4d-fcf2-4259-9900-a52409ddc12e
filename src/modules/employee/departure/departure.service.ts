// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import { CreateDepartureDto, UpdateDepartureDto } from '../dto/departure.dto';

// @Injectable()
// export class DepartureService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createDepartureDto: CreateDepartureDto) {
//     const { departureDate, reason, employeeId } = createDepartureDto;

//     return this.prisma.departure.create({
//       data: {
//         departureDate,
//         reason,
//         employee: { connect: { employeeId } },
//       },
//     });
//   }

//   async findAll(companyId: number) {
//     return this.prisma.departure.findMany({
//       where: {
//         employee: {
//           companyId,
//         },
//       },
//     });
//   }

//   async findOne(id: number) {
//     const departure = await this.prisma.departure.findUnique({
//       where: { departureId: id },
//     });

//     if (!departure) {
//       throw new NotFoundException(`Departure with ID ${id} not found`);
//     }

//     return departure;
//   }

//   async update(id: number, updateDepartureDto: UpdateDepartureDto) {
//     const departure = await this.findOne(id);

//     if (!departure) {
//       throw new NotFoundException(`Departure with ID ${id} not found`);
//     }

//     const { departureDate, reason, employeeId } = updateDepartureDto;

//     return this.prisma.departure.update({
//       where: { departureId: id },
//       data: {
//         departureDate: departureDate ?? departure.departureDate,
//         reason: reason ?? departure.reason,
//         employee: employeeId ? { connect: { employeeId } } : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const departure = await this.findOne(id);

//     if (!departure) {
//       throw new NotFoundException(`Departure with ID ${id} not found`);
//     }

//     return this.prisma.departure.delete({
//       where: { departureId: id },
//     });
//   }
// }
