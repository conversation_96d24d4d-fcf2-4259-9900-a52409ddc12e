// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { DepartureService } from './departure.service';
// import { CreateDepartureDto, UpdateDepartureDto } from '../dto/departure.dto';

// @ApiTags('departures')
// @Controller('api/v1/departures')
// export class DepartureController {
//   constructor(private readonly departureService: DepartureService) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new departure' })
//   @ApiResponse({
//     status: 201,
//     description: 'The departure has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createDepartureDto: CreateDepartureDto) {
//     return this.departureService.create(createDepartureDto);
//   }

//   @Get('/:companyId')
//   @ApiOperation({ summary: 'Get all departures' })
//   @ApiResponse({ status: 200, description: 'Return all departures.' })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll(@Param('companyId') companyId: number) {
//     return this.departureService.findAll(companyId);
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get a departure by ID' })
//   @ApiResponse({ status: 200, description: 'Return the departure.' })
//   @ApiResponse({ status: 404, description: 'Departure not found.' })
//   findOne(@Param('id') id: number) {
//     return this.departureService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update a departure by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The departure has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Departure not found.' })
//   update(
//     @Param('id') id: number,
//     @Body() updateDepartureDto: UpdateDepartureDto,
//   ) {
//     return this.departureService.update(id, updateDepartureDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete a departure by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The departure has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Departure not found.' })
//   remove(@Param('id') id: number) {
//     return this.departureService.remove(id);
//   }
// }
