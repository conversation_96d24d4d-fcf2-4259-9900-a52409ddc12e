// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
// import { TrainingService } from './trainig.service';
// import { CreateTrainingDto, UpdateTrainingDto } from '../dto/training.dto';

// @ApiTags('trainings')
// @Controller('api/v1/company/trainings')
// export class TrainingController {
//   constructor(private readonly trainingService: TrainingService) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new training' })
//   @ApiResponse({
//     status: 201,
//     description: 'The training has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createTrainingDto: CreateTrainingDto) {
//     return this.trainingService.create(createTrainingDto);
//   }

//   @Get(':employeeId')
//   @ApiOperation({ summary: 'Get all trainings' })
//   @ApiResponse({ status: 200, description: 'Return all trainings.' })
//   findAll(@Param('employeeId') employeeId: string) {
//     return this.trainingService.findAll(+employeeId);
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get a training by ID' })
//   @ApiResponse({ status: 200, description: 'Return the training.' })
//   @ApiResponse({ status: 404, description: 'Training not found.' })
//   @ApiParam({ name: 'id', type: Number, description: 'ID of the training' })
//   findOne(@Param('id') id: number) {
//     return this.trainingService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update a training by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The training has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Training not found.' })
//   @ApiParam({ name: 'id', type: Number, description: 'ID of the training' })
//   update(
//     @Param('id') id: number,
//     @Body() updateTrainingDto: UpdateTrainingDto,
//   ) {
//     return this.trainingService.update(id, updateTrainingDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete a training by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The training has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Training not found.' })
//   @ApiParam({ name: 'id', type: Number, description: 'ID of the training' })
//   remove(@Param('id') id: number) {
//     return this.trainingService.remove(id);
//   }
// }
