// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import { CreateTrainingDto, UpdateTrainingDto } from '../dto/training.dto';

// @Injectable()
// export class TrainingService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createTrainingDto: CreateTrainingDto) {
//     const { employeeId, trainingName, description, startDate, endDate } =
//       createTrainingDto;

//     // Validate dates
//     // if (endDate < startDate) {
//       throw new Error('The end date cannot be before the start date');
//     }

//     return this.prisma.training.create({
//       data: {
//         employee: { connect: { employeeId } },
//         trainingName,
//         description,
//         startDate,
//         endDate,
//       },
//     });
//   }

//   async findAll(employeeId: number) {
//     return this.prisma.training.findMany({ where: { employeeId } });
//   }

//   async findOne(id: number) {
//     const training = await this.prisma.training.findUnique({
//       where: { trainingId: id },
//     });

//     if (!training) {
//       throw new NotFoundException(`Training with ID ${id} not found`);
//     }

//     return training;
//   }

//   async update(id: number, updateTrainingDto: UpdateTrainingDto) {
//     const training = await this.findOne(id);

//     if (!training) {
//       throw new NotFoundException(`Training with ID ${id} not found`);
//     }

//     const { trainingName, description, startDate, endDate } = updateTrainingDto;

//     // Validate dates
//     if (endDate && startDate && endDate < startDate) {
//       throw new Error('The end date cannot be before the start date');
//     }

//     return this.prisma.training.update({
//       where: { trainingId: id },
//       data: {
//         trainingName: trainingName ?? training.trainingName,
//         description: description ?? training.description,
//         startDate: startDate ?? training.startDate,
//         endDate: endDate ?? training.endDate,
//       },
//     });
//   }

//   async remove(id: number) {
//     const training = await this.findOne(id);

//     if (!training) {
//       throw new NotFoundException(`Training with ID ${id} not found`);
//     }

//     return this.prisma.training.delete({
//       where: { trainingId: id },
//     });
//   }
// }
