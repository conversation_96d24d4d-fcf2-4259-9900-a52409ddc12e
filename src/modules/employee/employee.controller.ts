import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Patch,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { EmployeeService } from './employee.service';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';

@ApiTags('employees')
@Controller('api/v1/companies/:companyId/employees')
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  @Get()
  @ApiOperation({ summary: 'Get all employees for a company' })
  @ApiParam({ name: 'companyId', type: String, description: 'Company ID' })
  @ApiResponse({ status: 200, description: 'Return all employees.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Company not found.' })
  findAll(@Param('companyId') companyId: string) {
    return this.employeeService.findAll(companyId);
  }

  @Get('/statistics')
  @ApiOperation({ summary: 'Get employee statistics for a company' })
  @ApiParam({ name: 'companyId', type: String, description: 'Company ID' })
  @ApiQuery({
    name: 'date',
    required: false,
    description: 'Date for statistics (YYYY-MM-DD)',
  })
  @ApiResponse({
    status: 200,
    description:
      'Return statistics including total employees, new hires, resigned, and on leave.',
  })
  @ApiResponse({ status: 404, description: 'Company not found.' })
  getEmployeeStatistics(
    @Param('companyId') companyId: string,
    @Query('date') dateString?: string,
  ) {
    const date = dateString ? new Date(dateString) : undefined;
    return this.employeeService.getEmployeeStatistics(companyId, date);
  }

  @Get('/statistics/monthly')
  @ApiOperation({
    summary: 'Get monthly employee statistics for a company for a given year',
  })
  @ApiParam({ name: 'companyId', type: String, description: 'Company ID' })
  @ApiQuery({
    name: 'year',
    required: false,
    description: 'Year for statistics (YYYY) - defaults to current year',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description:
      'Return monthly statistics including new hires, resigned, and on leave for each month.',
  })
  @ApiResponse({ status: 404, description: 'Company not found.' })
  getMonthlyEmployeeStatistics(
    @Param('companyId') companyId: string,
    @Query('year') year?: number,
  ) {
    const targetYear = year || new Date().getFullYear();
    return this.employeeService.getMonthlyEmployeeStatistics(
      companyId,
      targetYear,
    );
  }

  @Post()
  @ApiOperation({ summary: 'Create a new employee in a company' })
  @ApiParam({ name: 'companyId', type: String, description: 'Company ID' })
  @ApiResponse({
    status: 201,
    description: 'The employee has been successfully created.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Company not found.' })
  create(
    @Param('companyId') companyId: string,
    @Body() createEmployeeDto: CreateEmployeeDto,
  ) {
    return this.employeeService.create(companyId, createEmployeeDto);
  }

  @Get(':employeeId')
  @ApiOperation({ summary: 'Get an employee by ID' })
  @ApiParam({ name: 'companyId', type: String, description: 'Company ID' })
  @ApiParam({ name: 'employeeId', type: String, description: 'Employee ID' })
  @ApiResponse({ status: 200, description: 'Return the employee.' })
  @ApiResponse({ status: 404, description: 'Employee not found.' })
  findOne(
    @Param('companyId') companyId: string,
    @Param('employeeId') employeeId: string,
  ) {
    return this.employeeService.findOne(employeeId);
  }

  @Patch(':employeeId')
  @ApiOperation({ summary: 'Update an employee by ID' })
  @ApiParam({ name: 'companyId', type: String, description: 'Company ID' })
  @ApiParam({ name: 'employeeId', type: String, description: 'Employee ID' })
  @ApiResponse({
    status: 200,
    description: 'The employee has been successfully updated.',
  })
  @ApiResponse({ status: 404, description: 'Employee not found.' })
  update(
    @Param('companyId') companyId: string,
    @Param('employeeId') employeeId: string,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
  ) {
    return this.employeeService.update(
      companyId,
      updateEmployeeDto,
      employeeId,
    );
  }
}
