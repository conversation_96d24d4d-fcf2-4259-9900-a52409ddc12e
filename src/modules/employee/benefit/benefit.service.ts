// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import { CreateBenefitDto, UpdateBenefitDto } from '../dto/benefit.dto';

// @Injectable()
// export class BenefitService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createBenefitDto: CreateBenefitDto) {
//     const { benefitType, description, authorizedAmount, employeeId } =
//       createBenefitDto;

//     return this.prisma.benefit.create({
//       data: {
//         benefitType,
//         description,
//         authorizedAmount,
//         employee: { connect: { employeeId } },
//       },
//     });
//   }

//   async findAll() {
//     return this.prisma.benefit.findMany();
//   }

//   async findOne(id: number) {
//     const benefit = await this.prisma.benefit.findUnique({
//       where: { benefitId: id },
//     });

//     if (!benefit) {
//       throw new NotFoundException(`Benefit with ID ${id} not found`);
//     }

//     return benefit;
//   }

//   async update(id: number, updateBenefitDto: UpdateBenefitDto) {
//     const benefit = await this.findOne(id);

//     if (!benefit) {
//       throw new NotFoundException(`Benefit with ID ${id} not found`);
//     }

//     const { benefitType, description, authorizedAmount, employeeId } =
//       updateBenefitDto;

//     return this.prisma.benefit.update({
//       where: { benefitId: id },
//       data: {
//         benefitType: benefitType ?? benefit.benefitType,
//         description: description ?? benefit.description,
//         authorizedAmount: authorizedAmount ?? benefit.authorizedAmount,
//         employee: employeeId ? { connect: { employeeId } } : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const benefit = await this.findOne(id);

//     if (!benefit) {
//       throw new NotFoundException(`Benefit with ID ${id} not found`);
//     }

//     return this.prisma.benefit.delete({
//       where: { benefitId: id },
//     });
//   }
// }
