// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { BenefitService } from './benefit.service';
// import { CreateBenefitDto, UpdateBenefitDto } from '../dto/benefit.dto';

// @ApiTags('benefits')
// @Controller('api/v1/company/benefits')
// export class BenefitController {
//   constructor(private readonly benefitService: BenefitService) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new benefit' })
//   @ApiResponse({
//     status: 201,
//     description: 'The benefit has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createBenefitDto: CreateBenefitDto) {
//     return this.benefitService.create(createBenefitDto);
//   }

//   @Get()
//   @ApiOperation({ summary: 'Get all benefits' })
//   @ApiResponse({ status: 200, description: 'Return all benefits.' })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll() {
//     return this.benefitService.findAll();
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get a benefit by ID' })
//   @ApiResponse({ status: 200, description: 'Return the benefit.' })
//   @ApiResponse({ status: 404, description: 'Benefit not found.' })
//   findOne(@Param('id') id: number) {
//     return this.benefitService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update a benefit by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The benefit has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Benefit not found.' })
//   update(@Param('id') id: number, @Body() updateBenefitDto: UpdateBenefitDto) {
//     return this.benefitService.update(id, updateBenefitDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete a benefit by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The benefit has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Benefit not found.' })
//   remove(@Param('id') id: number) {
//     return this.benefitService.remove(id);
//   }
// }
