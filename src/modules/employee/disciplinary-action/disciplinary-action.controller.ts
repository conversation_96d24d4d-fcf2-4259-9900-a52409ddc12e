// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { DisciplinaryActionService } from './disciplinary-action.service';
// import {
//   CreateDisciplinaryActionDto,
//   UpdateDisciplinaryActionDto,
// } from '../dto/disciplinary-action.dto';

// @ApiTags('disciplinary-actions')
// @Controller('api/v1/company/disciplinary-actions')
// export class DisciplinaryActionController {
//   constructor(
//     private readonly disciplinaryActionService: DisciplinaryActionService,
//   ) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new disciplinary action' })
//   @ApiResponse({
//     status: 201,
//     description: 'The disciplinary action has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createDisciplinaryActionDto: CreateDisciplinaryActionDto) {
//     return this.disciplinaryActionService.create(createDisciplinaryActionDto);
//   }

//   @Get()
//   @ApiOperation({ summary: 'Get all disciplinary actions' })
//   @ApiResponse({ status: 200, description: 'Return all disciplinary actions.' })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll() {
//     return this.disciplinaryActionService.findAll();
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get a disciplinary action by ID' })
//   @ApiResponse({ status: 200, description: 'Return the disciplinary action.' })
//   @ApiResponse({ status: 404, description: 'Disciplinary action not found.' })
//   findOne(@Param('id') id: number) {
//     return this.disciplinaryActionService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update a disciplinary action by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The disciplinary action has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Disciplinary action not found.' })
//   update(
//     @Param('id') id: number,
//     @Body() updateDisciplinaryActionDto: UpdateDisciplinaryActionDto,
//   ) {
//     return this.disciplinaryActionService.update(
//       id,
//       updateDisciplinaryActionDto,
//     );
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete a disciplinary action by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The disciplinary action has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Disciplinary action not found.' })
//   remove(@Param('id') id: number) {
//     return this.disciplinaryActionService.remove(id);
//   }
// }
