// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import {
//   CreateDisciplinaryActionDto,
//   UpdateDisciplinaryActionDto,
// } from '../dto/disciplinary-action.dto';

// @Injectable()
// export class DisciplinaryActionService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createDisciplinaryActionDto: CreateDisciplinaryActionDto) {
//     const { employeeId, ...others } = createDisciplinaryActionDto;

//     return this.prisma.disciplinaryAction.create({
//       data: {
//         ...others,
//         employee: { connect: { employeeId } },
//       },
//     });
//   }

//   async findAll() {
//     return this.prisma.disciplinaryAction.findMany();
//   }

//   async findOne(id: number) {
//     const disciplinaryAction = await this.prisma.disciplinaryAction.findUnique({
//       where: { actionId: id },
//     });

//     if (!disciplinaryAction) {
//       throw new NotFoundException(
//         `Disciplinary action with ID ${id} not found`,
//       );
//     }

//     return disciplinaryAction;
//   }

//   async update(
//     id: number,
//     updateDisciplinaryActionDto: UpdateDisciplinaryActionDto,
//   ) {
//     const disciplinaryAction = await this.findOne(id);

//     if (!disciplinaryAction) {
//       throw new NotFoundException(
//         `Disciplinary action with ID ${id} not found`,
//       );
//     }

//     const { description, actionDate, employeeId } = updateDisciplinaryActionDto;

//     return this.prisma.disciplinaryAction.update({
//       where: { actionId: id },
//       data: {
//         description: description ?? disciplinaryAction.description,
//         actionDate: actionDate ?? disciplinaryAction.actionDate,
//         employee: employeeId ? { connect: { employeeId } } : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const disciplinaryAction = await this.findOne(id);

//     if (!disciplinaryAction) {
//       throw new NotFoundException(
//         `Disciplinary action with ID ${id} not found`,
//       );
//     }

//     return this.prisma.disciplinaryAction.delete({
//       where: { actionId: id },
//     });
//   }
// }
