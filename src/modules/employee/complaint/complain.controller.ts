// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { ComplaintService } from './complaint.service';
// import { CreateComplaintDto, UpdateComplaintDto } from '../dto/complaint.dto';

// @ApiTags('complaints')
// @Controller('api/v1/company/complaints')
// export class ComplaintController {
//   constructor(private readonly complaintService: ComplaintService) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new complaint' })
//   @ApiResponse({
//     status: 201,
//     description: 'The complaint has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createComplaintDto: CreateComplaintDto) {
//     return this.complaintService.create(createComplaintDto);
//   }

//   @Get()
//   @ApiOperation({ summary: 'Get all complaints' })
//   @ApiResponse({ status: 200, description: 'Return all complaints.' })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll() {
//     return this.complaintService.findAll();
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get a complaint by ID' })
//   @ApiResponse({ status: 200, description: 'Return the complaint.' })
//   @ApiResponse({ status: 404, description: 'Complaint not found.' })
//   findOne(@Param('id') id: number) {
//     return this.complaintService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update a complaint by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The complaint has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Complaint not found.' })
//   update(
//     @Param('id') id: number,
//     @Body() updateComplaintDto: UpdateComplaintDto,
//   ) {
//     return this.complaintService.update(id, updateComplaintDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete a complaint by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The complaint has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Complaint not found.' })
//   remove(@Param('id') id: number) {
//     return this.complaintService.remove(id);
//   }
// }
