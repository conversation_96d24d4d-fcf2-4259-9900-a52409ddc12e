// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import { CreateComplaintDto, UpdateComplaintDto } from '../dto/complaint.dto';

// @Injectable()
// export class ComplaintService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createComplaintDto: CreateComplaintDto) {
//     const { employeeId, ...others } = createComplaintDto;

//     return this.prisma.complaint.create({
//       data: {
//         ...others,
//         employee: { connect: { employeeId } },
//       },
//     });
//   }

//   async findAll() {
//     return this.prisma.complaint.findMany();
//   }

//   async findOne(id: number) {
//     const complaint = await this.prisma.complaint.findUnique({
//       where: { complaintId: id },
//     });

//     if (!complaint) {
//       throw new NotFoundException(`Complaint with ID ${id} not found`);
//     }

//     return complaint;
//   }

//   async update(id: number, updateComplaintDto: UpdateComplaintDto) {
//     const complaint = await this.findOne(id);

//     if (!complaint) {
//       throw new NotFoundException(`Complaint with ID ${id} not found`);
//     }

//     const { description, complaintDate, employeeId } = updateComplaintDto;

//     return this.prisma.complaint.update({
//       where: { complaintId: id },
//       data: {
//         description: description ?? complaint.description,
//         complaintDate: complaintDate ?? complaint.complaintDate,
//         employee: employeeId ? { connect: { employeeId } } : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const complaint = await this.findOne(id);

//     if (!complaint) {
//       throw new NotFoundException(`Complaint with ID ${id} not found`);
//     }

//     return this.prisma.complaint.delete({
//       where: { complaintId: id },
//     });
//   }
// }
