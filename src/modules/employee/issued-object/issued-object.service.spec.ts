import { Test, TestingModule } from '@nestjs/testing';
import { IssuedObjectService } from './issued-object.service';

describe('IssuedObjectService', () => {
  let service: IssuedObjectService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [IssuedObjectService],
    }).compile();

    service = module.get<IssuedObjectService>(IssuedObjectService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
