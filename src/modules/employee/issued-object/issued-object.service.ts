// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import {
//   CreateIssuedObjectDto,
//   UpdateIssuedObjectDto,
// } from '../dto/issued-object.dto';

// @Injectable()
// export class IssuedObjectService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createIssuedObjectDto: CreateIssuedObjectDto) {
//     const { objectName, description, issuedDate, returnDate, employeeId } =
//       createIssuedObjectDto;

//     return this.prisma.issuedObject.create({
//       data: {
//         objectName,
//         description,
//         issuedDate,
//         returnDate,
//         employee: { connect: { employeeId } },
//       },
//     });
//   }

//   async findAll() {
//     return this.prisma.issuedObject.findMany();
//   }

//   async findOne(id: number) {
//     const issuedObject = await this.prisma.issuedObject.findUnique({
//       where: { objectId: id },
//     });

//     if (!issuedObject) {
//       throw new NotFoundException(`IssuedObject with ID ${id} not found`);
//     }

//     return issuedObject;
//   }

//   async update(id: number, updateIssuedObjectDto: UpdateIssuedObjectDto) {
//     const issuedObject = await this.findOne(id);

//     if (!issuedObject) {
//       throw new NotFoundException(`IssuedObject with ID ${id} not found`);
//     }

//     const { objectName, description, issuedDate, returnDate, employeeId } =
//       updateIssuedObjectDto;

//     return this.prisma.issuedObject.update({
//       where: { objectId: id },
//       data: {
//         objectName: objectName ?? issuedObject.objectName,
//         description: description ?? issuedObject.description,
//         issuedDate: issuedDate ?? issuedObject.issuedDate,
//         returnDate: returnDate ?? issuedObject.returnDate,
//         employee: employeeId ? { connect: { employeeId } } : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const issuedObject = await this.findOne(id);

//     if (!issuedObject) {
//       throw new NotFoundException(`IssuedObject with ID ${id} not found`);
//     }

//     return this.prisma.issuedObject.delete({
//       where: { objectId: id },
//     });
//   }
// }
