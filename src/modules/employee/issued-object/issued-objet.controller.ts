// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { IssuedObjectService } from './issued-object.service';
// import {
//   CreateIssuedObjectDto,
//   UpdateIssuedObjectDto,
// } from '../dto/issued-object.dto';

// @ApiTags('issued-objects')
// @Controller('api/v1/company/issued-objects')
// export class IssuedObjectController {
//   constructor(private readonly issuedObjectService: IssuedObjectService) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new issued object' })
//   @ApiResponse({
//     status: 201,
//     description: 'The issued object has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createIssuedObjectDto: CreateIssuedObjectDto) {
//     return this.issuedObjectService.create(createIssuedObjectDto);
//   }

//   @Get()
//   @ApiOperation({ summary: 'Get all issued objects' })
//   @ApiResponse({ status: 200, description: 'Return all issued objects.' })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll() {
//     return this.issuedObjectService.findAll();
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get an issued object by ID' })
//   @ApiResponse({ status: 200, description: 'Return the issued object.' })
//   @ApiResponse({ status: 404, description: 'Issued object not found.' })
//   findOne(@Param('id') id: number) {
//     return this.issuedObjectService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update an issued object by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The issued object has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Issued object not found.' })
//   update(
//     @Param('id') id: number,
//     @Body() updateIssuedObjectDto: UpdateIssuedObjectDto,
//   ) {
//     return this.issuedObjectService.update(id, updateIssuedObjectDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete an issued object by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The issued object has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Issued object not found.' })
//   remove(@Param('id') id: number) {
//     return this.issuedObjectService.remove(id);
//   }
// }
