import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';

@Injectable()
export class EmployeeService {
  constructor(private readonly prisma: PrismaService) {}

  async create(companyId: string, createEmployeeDto: CreateEmployeeDto) {
    const {
      address,
      hireDate,
      positionId,
      departmentId,
      firstName,
      lastName,
      birthDate,
      email,
      role,
    } = createEmployeeDto;

    // Vérifier que l'email n'est pas déjà utilisé
    const user = await this.prisma.user.findUnique({ where: { email } });
    if (user) {
      throw new BadRequestException('This email is already taken');
    }

    // Vérifier que le département existe
    const department = await this.prisma.department.findUnique({
      where: { id: departmentId },
    });
    if (!department) {
      throw new NotFoundException(
        `Department with ID ${departmentId} not found`,
      );
    }

    // Vérifier que le poste existe et appartient au département
    const position = await this.prisma.position.findUnique({
      where: { id: positionId },
    });
    if (!position || position.departmentId !== departmentId) {
      throw new NotFoundException(
        `Position with ID ${positionId} not found in department ${departmentId}`,
      );
    }

    return this.prisma.user.create({
      data: {
        email,
        role: role || 'EMPLOYEE',
        employeeData: {
          create: {
            hireDate,
            position: { connect: { id: positionId } },
            department: { connect: { id: departmentId } },
            company: {
              connect: {
                id: companyId ? companyId : createEmployeeDto.companyId,
              },
            },
          },
        },
        profile: {
          create: {
            firstName,
            lastName,
            birthDate,
            address: address ? { create: address } : undefined,
          },
        },
      },
      include: {
        employeeData: {
          include: {
            department: true,
            position: true,
          },
        },
      },
    });
  }

  async findAll(companyId: string) {
    return await this.prisma.employeeData.findMany({
      where: {
        companyId,
      },
      include: {
        department: true,
        position: true,
        salaries: true,
        timesheets: true,
        trainings: true,
        payslips: true,
        leaves: true,
        user: {
          include: {
            profile: {
              include: {
                address: true,
              },
            },
          },
        },
      },
    });
  }

  async findOne(id: string) {
    const employee = await this.prisma.employeeData.findUnique({
      where: { id },
      include: {
        department: true,
        position: true,
        trainings: true,
        salaries: true,
        company: true,
        leaves: true,
        timesheets: true,
        payslips: true,
        performanceEvaluations: true,
        user: {
          include: {
            profile: {
              include: {
                address: true,
              },
            },
          },
        },
      },
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${id} not found`);
    }

    return employee;
  }

  async update(
    currentCompanyId: string,
    updateEmployeeDto: UpdateEmployeeDto,
    employeeId: string,
  ) {
    const { hireDate, positionId, departmentId, companyId, ...others } =
      updateEmployeeDto;

    const existingEmployee = await this.prisma.employeeData.findUnique({
      where: { id: employeeId },
    });

    if (!existingEmployee) {
      throw new NotFoundException(`Employee with ID ${employeeId} not found`);
    }

    // Vérifier le département si fourni
    if (departmentId) {
      const department = await this.prisma.department.findUnique({
        where: { id: departmentId },
      });
      if (!department) {
        throw new NotFoundException(
          `Department with ID ${departmentId} not found`,
        );
      }
    }

    // Vérifier le poste si fourni
    if (positionId) {
      const position = await this.prisma.position.findUnique({
        where: { id: positionId },
      });
      if (!position) {
        throw new NotFoundException(`Position with ID ${positionId} not found`);
      }
      if (departmentId && position.departmentId !== departmentId) {
        throw new BadRequestException(
          `Position ${positionId} does not belong to department ${departmentId}`,
        );
      }
    }

    return this.prisma.employeeData.update({
      where: { id: employeeId },
      data: {
        ...others,
        hireDate: hireDate ?? existingEmployee.hireDate,
        position: positionId
          ? { connect: { id: positionId } }
          : { connect: { id: existingEmployee.positionId } },
        department: departmentId
          ? { connect: { id: departmentId } }
          : { connect: { id: existingEmployee.departmentId } },
        company: companyId
          ? { connect: { id: companyId } }
          : { connect: { id: existingEmployee.companyId } },
      },
      include: {
        department: true,
        position: true,
      },
    });
  }

  async getEmployeeStatistics(
    companyId: string,
    date?: Date,
  ): Promise<{
    totalEmployees: number;
    newlyHired: number;
    resigned: number;
    onLeave: number;
  }> {
    const targetDate = date || new Date();
    const firstDayOfMonth = new Date(
      targetDate.getFullYear(),
      targetDate.getMonth(),
      1,
    );
    const lastDayOfMonth = new Date(
      targetDate.getFullYear(),
      targetDate.getMonth() + 1,
      0,
    );

    const [totalEmployees, newlyHired, resigned, onLeave] = await Promise.all([
      this.prisma.employeeData.count({
        where: {
          companyId,
          NOT: {
            departures: {
              some: {
                departureDate: { lt: firstDayOfMonth },
              },
            },
          },
        },
      }),
      this.prisma.employeeData.count({
        where: {
          companyId,
          hireDate: {
            gte: firstDayOfMonth,
            lte: lastDayOfMonth,
          },
        },
      }),
      this.prisma.departure.count({
        where: {
          employee: { companyId },
          departureDate: {
            gte: firstDayOfMonth,
            lte: lastDayOfMonth,
          },
        },
      }),
      this.prisma.leave.count({
        where: {
          employee: { companyId },
          startDate: { lte: lastDayOfMonth },
          endDate: { gte: firstDayOfMonth },
        },
      }),
    ]);

    return { totalEmployees, newlyHired, resigned, onLeave };
  }

  async getMonthlyEmployeeStatistics(
    companyId: string,
    year: number,
  ): Promise<{
    monthlyStats: {
      month: number;
      newlyHired: number;
      resigned: number;
      onLeave: number;
    }[];
  }> {
    const monthlyStats = [];

    for (let month = 0; month < 12; month++) {
      const firstDayOfMonth = new Date(year, month, 1);
      const lastDayOfMonth = new Date(year, month + 1, 0);

      const [newlyHired, resigned, onLeave] = await Promise.all([
        this.prisma.employeeData.count({
          where: {
            companyId,
            hireDate: {
              gte: firstDayOfMonth,
              lte: lastDayOfMonth,
            },
          },
        }),
        this.prisma.departure.count({
          where: {
            employee: { companyId },
            departureDate: {
              gte: firstDayOfMonth,
              lte: lastDayOfMonth,
            },
          },
        }),
        this.prisma.leave.count({
          where: {
            employee: { companyId },
            startDate: { lte: lastDayOfMonth },
            endDate: { gte: firstDayOfMonth },
          },
        }),
      ]);

      monthlyStats.push({
        month,
        newlyHired,
        resigned,
        onLeave,
      });
    }

    return { monthlyStats };
  }
}
