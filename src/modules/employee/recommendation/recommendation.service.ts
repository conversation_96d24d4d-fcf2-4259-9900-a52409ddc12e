// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import {
//   CreateRecommendationDto,
//   UpdateRecommendationDto,
// } from '../dto/recommendation.dto';

// @Injectable()
// export class RecommendationService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createRecommendationDto: CreateRecommendationDto) {
//     const {
//       description,
//       recommendationDate,
//       senderEmployeeId,
//       receiverEmployeeId,
//     } = createRecommendationDto;

//     return this.prisma.recommendation.create({
//       data: {
//         description,
//         recommendationDate,
//         senderEmployee: { connect: { employeeId: senderEmployeeId } },
//         receiverEmployee: { connect: { employeeId: receiverEmployeeId } },
//       },
//     });
//   }

//   async findAll() {
//     return this.prisma.recommendation.findMany();
//   }

//   async findOne(id: number) {
//     const recommendation = await this.prisma.recommendation.findUnique({
//       where: { recommendationId: id },
//     });

//     if (!recommendation) {
//       throw new NotFoundException(`Recommendation with ID ${id} not found`);
//     }

//     return recommendation;
//   }

//   async update(id: number, updateRecommendationDto: UpdateRecommendationDto) {
//     const recommendation = await this.findOne(id);

//     if (!recommendation) {
//       throw new NotFoundException(`Recommendation with ID ${id} not found`);
//     }

//     const {
//       description,
//       recommendationDate,
//       senderEmployeeId,
//       receiverEmployeeId,
//     } = updateRecommendationDto;

//     return this.prisma.recommendation.update({
//       where: { recommendationId: id },
//       data: {
//         description: description ?? recommendation.description,
//         recommendationDate:
//           recommendationDate ?? recommendation.recommendationDate,
//         senderEmployee: senderEmployeeId
//           ? { connect: { employeeId: senderEmployeeId } }
//           : undefined,
//         receiverEmployee: receiverEmployeeId
//           ? { connect: { employeeId: receiverEmployeeId } }
//           : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const recommendation = await this.findOne(id);

//     if (!recommendation) {
//       throw new NotFoundException(`Recommendation with ID ${id} not found`);
//     }

//     return this.prisma.recommendation.delete({
//       where: { recommendationId: id },
//     });
//   }
// }
