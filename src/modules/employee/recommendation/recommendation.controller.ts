// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { RecommendationService } from './recommendation.service';
// import {
//   CreateRecommendationDto,
//   UpdateRecommendationDto,
// } from '../dto/recommendation.dto';

// @ApiTags('recommendations')
// @Controller('api/v1/company/recommendations')
// export class RecommendationController {
//   constructor(private readonly recommendationService: RecommendationService) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new recommendation' })
//   @ApiResponse({
//     status: 201,
//     description: 'The recommendation has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createRecommendationDto: CreateRecommendationDto) {
//     return this.recommendationService.create(createRecommendationDto);
//   }

//   @Get()
//   @ApiOperation({ summary: 'Get all recommendations' })
//   @ApiResponse({ status: 200, description: 'Return all recommendations.' })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll() {
//     return this.recommendationService.findAll();
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get a recommendation by ID' })
//   @ApiResponse({ status: 200, description: 'Return the recommendation.' })
//   @ApiResponse({ status: 404, description: 'Recommendation not found.' })
//   findOne(@Param('id') id: number) {
//     return this.recommendationService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update a recommendation by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The recommendation has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Recommendation not found.' })
//   update(
//     @Param('id') id: number,
//     @Body() updateRecommendationDto: UpdateRecommendationDto,
//   ) {
//     return this.recommendationService.update(id, updateRecommendationDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete a recommendation by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The recommendation has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Recommendation not found.' })
//   remove(@Param('id') id: number) {
//     return this.recommendationService.remove(id);
//   }
// }
