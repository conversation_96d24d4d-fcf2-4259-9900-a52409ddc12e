// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import {
//   CreatePerformanceEvaluationDto,
//   UpdatePerformanceEvaluationDto,
// } from '../dto/performance-evaluation.dto';

// @Injectable()
// export class PerformanceEvaluationService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createPerformanceEvaluationDto: CreatePerformanceEvaluationDto) {
//     const {
//       periodStart,
//       periodEnd,
//       goals,
//       selfEvaluation,
//       managerEvaluation,
//       employeeId,
//     } = createPerformanceEvaluationDto;

//     return this.prisma.performanceEvaluation.create({
//       data: {
//         periodStart,
//         periodEnd,
//         goals,
//         selfEvaluation,
//         managerEvaluation,
//         employee: { connect: { employeeId } },
//       },
//     });
//   }

//   async findAll() {
//     return this.prisma.performanceEvaluation.findMany();
//   }

//   async findOne(id: number) {
//     const performanceEvaluation =
//       await this.prisma.performanceEvaluation.findUnique({
//         where: { evaluationId: id },
//       });

//     if (!performanceEvaluation) {
//       throw new NotFoundException(
//         `PerformanceEvaluation with ID ${id} not found`,
//       );
//     }

//     return performanceEvaluation;
//   }

//   async update(
//     id: number,
//     updatePerformanceEvaluationDto: UpdatePerformanceEvaluationDto,
//   ) {
//     const performanceEvaluation = await this.findOne(id);

//     if (!performanceEvaluation) {
//       throw new NotFoundException(
//         `PerformanceEvaluation with ID ${id} not found`,
//       );
//     }

//     const {
//       periodStart,
//       periodEnd,
//       goals,
//       selfEvaluation,
//       managerEvaluation,
//       employeeId,
//     } = updatePerformanceEvaluationDto;

//     return this.prisma.performanceEvaluation.update({
//       where: { evaluationId: id },
//       data: {
//         periodStart: periodStart ?? performanceEvaluation.periodStart,
//         periodEnd: periodEnd ?? performanceEvaluation.periodEnd,
//         goals: goals ?? performanceEvaluation.goals,
//         selfEvaluation: selfEvaluation ?? performanceEvaluation.selfEvaluation,
//         managerEvaluation:
//           managerEvaluation ?? performanceEvaluation.managerEvaluation,
//         employee: employeeId ? { connect: { employeeId } } : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const performanceEvaluation = await this.findOne(id);

//     if (!performanceEvaluation) {
//       throw new NotFoundException(
//         `PerformanceEvaluation with ID ${id} not found`,
//       );
//     }

//     return this.prisma.performanceEvaluation.delete({
//       where: { evaluationId: id },
//     });
//   }
// }
