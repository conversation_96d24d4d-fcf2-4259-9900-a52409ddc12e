// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { PerformanceEvaluationService } from './performance-evaluation.service';
// import {
//   CreatePerformanceEvaluationDto,
//   UpdatePerformanceEvaluationDto,
// } from '../dto/performance-evaluation.dto';
// //
// @ApiTags('performance-evaluations')
// @Controller('api/v1/company/performance-evaluations')
// export class PerformanceEvaluationController {
//   constructor(
//     private readonly performanceEvaluationService: PerformanceEvaluationService,
//   ) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new performance evaluation' })
//   @ApiResponse({
//     status: 201,
//     description: 'The performance evaluation has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(
//     @Body() createPerformanceEvaluationDto: CreatePerformanceEvaluationDto,
//   ) {
//     return this.performanceEvaluationService.create(
//       createPerformanceEvaluationDto,
//     );
//   }

//   @Get()
//   @ApiOperation({ summary: 'Get all performance evaluations' })
//   @ApiResponse({
//     status: 200,
//     description: 'Return all performance evaluations.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll() {
//     return this.performanceEvaluationService.findAll();
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get a performance evaluation by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'Return the performance evaluation.',
//   })
//   @ApiResponse({
//     status: 404,
//     description: 'Performance evaluation not found.',
//   })
//   findOne(@Param('id') id: number) {
//     return this.performanceEvaluationService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update a performance evaluation by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The performance evaluation has been successfully updated.',
//   })
//   @ApiResponse({
//     status: 404,
//     description: 'Performance evaluation not found.',
//   })
//   update(
//     @Param('id') id: number,
//     @Body() updatePerformanceEvaluationDto: UpdatePerformanceEvaluationDto,
//   ) {
//     return this.performanceEvaluationService.update(
//       id,
//       updatePerformanceEvaluationDto,
//     );
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete a performance evaluation by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The performance evaluation has been successfully deleted.',
//   })
//   @ApiResponse({
//     status: 404,
//     description: 'Performance evaluation not found.',
//   })
//   remove(@Param('id') id: number) {
//     return this.performanceEvaluationService.remove(id);
//   }
// }
