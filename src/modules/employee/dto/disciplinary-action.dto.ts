import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsDate, IsInt } from 'class-validator';

export class CreateDisciplinaryActionDto {
  @ApiProperty({
    description: 'The description of the disciplinary action',
    example: 'Violation of company policy regarding attendance',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The action taken',
    example: 'Violation of company policy regarding attendance',
  })
  @IsString()
  @IsNotEmpty()
  actionTaken: string;

  @ApiProperty({
    description: 'The date of the disciplinary action',
    example: '2023-06-01T00:00:00.000Z',
  })
  @IsDate()
  @IsNotEmpty()
  actionDate: Date;

  @ApiProperty({
    description:
      'The ID of the employee associated with the disciplinary action',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  employeeId: number;
}
export class UpdateDisciplinaryActionDto extends PartialType(
  CreateDisciplinaryActionDto,
) {}
