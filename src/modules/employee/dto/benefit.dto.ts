import { ApiProperty, PartialType } from '@nestjs/swagger';
import { BenefitTypeEnum } from '@prisma/client';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  IsNumber,
  Min,
  IsInt,
} from 'class-validator';

export class CreateBenefitDto {
  @ApiProperty({
    description: 'The type of the benefit',
    example: 'health',
    enum: BenefitTypeEnum,
  })
  @IsEnum(BenefitTypeEnum)
  @IsNotEmpty()
  benefitType: BenefitTypeEnum;

  @ApiProperty({
    description: 'The description of the benefit',
    example: 'Health insurance covering dental and vision',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The authorized amount for the benefit',
    example: 1000.0,
  })
  @IsNumber()
  @Min(0)
  authorizedAmount: number;

  @ApiProperty({
    description: 'The ID of the employee associated with the benefit',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  employeeId: number;
}
export class UpdateBenefitDto extends PartialType(CreateBenefitDto) {}
