import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsDateString, IsInt } from 'class-validator';

export class CreateDepartureDto {
  @ApiProperty({
    description: 'The date of the departure',
    example: '2023-06-01T12:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  departureDate: string;

  @ApiProperty({
    description: 'The reason for the departure',
    example: 'Retirement',
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiProperty({
    description: 'The ID of the employee associated with the departure',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  employeeId: number;
}
export class UpdateDepartureDto extends PartialType(CreateDepartureDto) {}
