import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsInt, IsDateString } from 'class-validator';

export class CreateRecommendationDto {
  @ApiProperty({
    description: 'Description of the recommendation',
    example: 'Highly skilled in project management.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Date of the recommendation',
    example: '2023-06-01T12:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  recommendationDate: string;

  @ApiProperty({
    description: 'ID of the employee sending the recommendation',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  senderEmployeeId: number;

  @ApiProperty({
    description: 'ID of the employee receiving the recommendation',
    example: 2,
  })
  @IsInt()
  @IsNotEmpty()
  receiverEmployeeId: number;
}
export class UpdateRecommendationDto extends PartialType(
  CreateRecommendationDto,
) {}
