import { ApiProperty, PartialType } from '@nestjs/swagger';
import { LeaveStatusEnum, LeaveTypeEnum } from '@prisma/client';
import {
  IsEnum,
  IsNotEmpty,
  IsDateString,
  IsUUID,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateLeaveDto {
  @ApiProperty({
    description: 'Type of leave',
    enum: LeaveTypeEnum,
    example: LeaveTypeEnum.ANNUAL,
  })
  @IsEnum(LeaveTypeEnum)
  @IsNotEmpty()
  leaveType: LeaveTypeEnum;

  @ApiProperty({
    description: 'Raison de la demande de congé',
    example: 'Maladie',
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  @IsString()
  raison?: string;

  @ApiProperty({
    description: 'Start date of leave',
    example: '2023-06-01T09:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  @ApiProperty({
    description: 'End date of leave',
    example: '2023-06-05T17:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  endDate: string;

  @ApiProperty({
    description: 'Status of leave',
    enum: LeaveStatusEnum,
    example: LeaveStatusEnum.REJECTED,
  })
  @IsEnum(LeaveStatusEnum)
  @IsNotEmpty()
  status: LeaveStatusEnum;

  @ApiProperty({
    description: 'The ID of the employee requesting the leave',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  employeeId: string;
}
export class UpdateLeaveDto extends PartialType(CreateLeaveDto) {}
