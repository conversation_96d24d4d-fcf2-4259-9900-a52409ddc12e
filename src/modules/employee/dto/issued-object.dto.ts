import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsDateString, IsInt } from 'class-validator';

export class CreateIssuedObjectDto {
  @ApiProperty({
    description: 'The name of the issued object',
    example: '<PERSON>ptop',
  })
  @IsString()
  @IsNotEmpty()
  objectName: string;

  @ApiProperty({
    description: 'Description of the issued object',
    example: 'Company-provided laptop for work purposes.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The date when the object was issued',
    example: '2023-06-01T09:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  issuedDate: string;

  @ApiProperty({
    description: 'The date when the object is expected to be returned',
    example: '2024-06-01T17:00:00Z',
  })
  @IsDateString()
  returnDate: string;

  @ApiProperty({
    description: 'The ID of the employee to whom the object is issued',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  employeeId: number;
}
export class UpdateIssuedObjectDto extends PartialType(CreateIssuedObjectDto) {}
