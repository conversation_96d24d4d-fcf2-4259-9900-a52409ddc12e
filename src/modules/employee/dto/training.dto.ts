import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsDate, IsInt } from 'class-validator';

export class CreateTrainingDto {
  @ApiProperty({
    description: 'The name of the training',
    example: 'Advanced React Training',
  })
  @IsString()
  @IsNotEmpty()
  trainingName: string;

  @ApiProperty({
    description: 'The description of the training',
    example: 'An in-depth training session covering advanced React concepts.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The start date of the training',
    example: '2023-06-01T00:00:00.000Z',
  })
  @IsDate()
  @IsNotEmpty()
  startDate: Date;

  @ApiProperty({
    description: 'The end date of the training',
    example: '2023-06-05T00:00:00.000Z',
  })
  @IsDate()
  @IsNotEmpty()
  endDate: Date;

  @ApiProperty({
    description: 'The ID of the employee associated with the training',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  employeeId: number;
}
export class UpdateTrainingDto extends PartialType(CreateTrainingDto) {}
