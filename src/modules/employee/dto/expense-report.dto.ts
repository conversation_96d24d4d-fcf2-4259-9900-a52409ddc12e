import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsDateString, IsNumber } from 'class-validator';

export class CreateExpenseReportDto {
  @ApiProperty({
    example: 'This is an expense report description.',
    description: 'Description of the expense report',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({ example: 150.75, description: 'Amount of the expense report' })
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @ApiProperty({
    example: '2024-06-30T15:30:00Z',
    description: 'Date of the expense report',
  })
  @IsNotEmpty()
  @IsDateString()
  reportDate: Date;

  @ApiProperty({
    example: 1,
    description: 'ID of the employee submitting the expense report',
  })
  @IsNotEmpty()
  @IsNumber()
  employeeId: number;
}

export class UpdateExpenseReportDto extends PartialType(
  CreateExpenseReportDto,
) {}
