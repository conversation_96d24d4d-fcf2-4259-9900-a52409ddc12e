import { ApiProperty, PartialType } from '@nestjs/swagger';
import { ProbationStatusEnum } from '@prisma/client';
import { IsDateString, IsNotEmpty, IsInt } from 'class-validator';

export class CreateProbationPeriodDto {
  @ApiProperty({
    description: 'Start date of the probation period',
    example: '2023-01-01T00:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  @ApiProperty({
    description: 'End date of the probation period',
    example: '2023-03-31T23:59:59Z',
  })
  @IsDateString()
  @IsNotEmpty()
  endDate: string;

  @ApiProperty({
    description: 'Status of the probation period',
    enum: ['pending', 'completed'],
    example: 'completed',
  })
  @IsNotEmpty()
  status: ProbationStatusEnum;

  @ApiProperty({
    description: 'The ID of the employee under probation',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  employeeId: number;
}
export class UpdateProbationPeriodDto extends PartialType(
  CreateProbationPeriodDto,
) {}
