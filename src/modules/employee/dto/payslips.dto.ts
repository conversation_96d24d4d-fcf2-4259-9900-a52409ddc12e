import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PayslipResponseDto {
  @ApiProperty({ description: 'ID du bulletin de paie' })
  id: string;

  @ApiProperty({ description: 'Mois' })
  month: number;

  @ApiProperty({ description: 'Année' })
  year: number;

  @ApiProperty({ description: 'Salaire de base' })
  baseSalary: number;

  @ApiProperty({ description: 'Allocation logement' })
  housingAllowance: number;

  @ApiProperty({ description: 'Allocation transport' })
  transportAllowance: number;

  @ApiProperty({ description: 'Allocation repas' })
  mealAllowance: number;

  @ApiProperty({ description: 'Allocation familiale' })
  familyAllowance: number;

  @ApiProperty({ description: 'Bonus' })
  bonus: number;

  @ApiProperty({ description: 'Heures supplémentaires' })
  overtimeHours: number;

  @ApiProperty({ description: 'Paiement heures supplémentaires' })
  overtimePay: number;

  @ApiProperty({ description: 'Déductions fiscales' })
  taxDeductions: number;

  @ApiProperty({ description: 'Cotisations sociales' })
  socialSecurity: number;

  @ApiProperty({ description: 'Autres déductions' })
  otherDeductions: number;

  @ApiProperty({ description: 'Salaire brut' })
  grossSalary: number;

  @ApiProperty({ description: 'Salaire net' })
  netSalary: number;

  @ApiProperty({ description: 'Statut du bulletin' })
  status: string;

  @ApiPropertyOptional({ description: 'Date de paiement' })
  paymentDate?: Date;

  @ApiProperty({ description: 'Date de création' })
  createdAt: Date;

  @ApiProperty({ description: 'Date de mise à jour' })
  updatedAt: Date;
}
