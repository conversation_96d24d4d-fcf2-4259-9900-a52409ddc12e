import {
  IsUUID,
  IsPositive,
  IsNumber,
  IsDateString,
  IsOptional,
  Min,
  IsInt,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateSalaryDto {
  @ApiProperty({ description: "ID de l'employé", format: 'uuid' })
  @IsUUID()
  employeeId: string;

  @ApiProperty({ description: 'Salaire de base', minimum: 0 })
  @IsNumber()
  @Min(0)
  baseSalary: number;

  @ApiPropertyOptional({ description: 'Allocation logement', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  housingAllowance?: number;

  @ApiPropertyOptional({ description: 'Allocation transport', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  transportAllowance?: number;

  @ApiPropertyOptional({ description: 'Bonus', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  bonus?: number;

  @ApiPropertyOptional({ description: 'Heures supplémentaires', minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  overtimeHours?: number;

  @ApiPropertyOptional({
    description: 'Taux horaire des heures supplémentaires',
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  overtimeRate?: number;

  @ApiProperty({
    description: "Date d'entrée en vigueur (format YYYY-MM-DD)",
    example: '2023-01-01',
  })
  @IsDateString()
  effectiveDate: string;
}

export class UpdateSalaryDto {
  @ApiPropertyOptional({ description: 'Salaire de base', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  baseSalary?: number;

  @ApiPropertyOptional({ description: 'Allocation logement', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  housingAllowance?: number;

  @ApiPropertyOptional({ description: 'Allocation transport', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  transportAllowance?: number;

  @ApiPropertyOptional({ description: 'Bonus', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  bonus?: number;

  @ApiPropertyOptional({ description: 'Heures supplémentaires', minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  overtimeHours?: number;

  @ApiPropertyOptional({
    description: 'Taux horaire des heures supplémentaires',
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  overtimeRate?: number;

  @ApiPropertyOptional({
    description: "Date d'entrée en vigueur (format YYYY-MM-DD)",
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDateString()
  effectiveDate?: string;
}
