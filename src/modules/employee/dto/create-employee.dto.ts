// src/employee/dto/create-employee.dto.ts
import {
  IsString,
  IsInt,
  IsDate,
  IsNotEmpty,
  IsEmail,
  IsEnum,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AddressDto } from 'src/modules/iam/auth/email-password-auth/dto/signup.dto';
import { RoleEnum } from '@prisma/client';

export class CreateEmployeeDto {
  @ApiProperty({ description: 'First name of the employee' })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'Last name of the employee' })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ description: 'Email of the employee' })
  @IsString()
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'Role of the employee',
    enum: RoleEnum,
    default: RoleEnum.EMPLOYEE,
  })
  @IsEnum(RoleEnum)
  @IsOptional()
  role?: RoleEnum;

  @ApiProperty({ description: 'Birth date of the employee' })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  birthDate: Date;

  @ApiProperty({ description: 'Hire date of the employee' })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  hireDate: Date;

  @ApiProperty({
    description: 'ID of the department where the employee works',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  departmentId: string;

  @ApiProperty({
    description: 'ID of the employee position',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  positionId: string;

  @ApiPropertyOptional({
    description: 'Address associated with the employee',
    type: AddressDto,
  })
  @IsOptional()
  address?: AddressDto;

  @ApiPropertyOptional({
    description: 'ID of company (only needed when creating super admin)',
    example: '',
  })
  @IsOptional()
  companyId?: string;
}
