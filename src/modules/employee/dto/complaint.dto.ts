import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsDateString, IsInt } from 'class-validator';

export class CreateComplaintDto {
  @ApiProperty({
    example: '2024-06-27T10:00:00Z',
    description: 'Date of the complaint',
  })
  @IsNotEmpty()
  @IsDateString()
  complaintDate: Date;

  @ApiProperty({
    example: 'This is a complaint description.',
    description: 'Description of the complaint',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    example: '2024-06-28T14:00:00Z',
    description: 'Date of resolution of the complaint',
  })
  @IsDateString()
  resolutionDate: Date;

  @ApiProperty({
    example: 'Resolved with satisfactory details.',
    description: 'Details of the resolution',
  })
  @IsNotEmpty()
  @IsString()
  resolutionDetails: string;

  @ApiProperty({
    example: 1,
    description: 'ID of the employee handling the complaint',
  })
  @IsNotEmpty()
  @IsInt()
  employeeId: number;
}

export class UpdateComplaintDto extends PartialType(CreateComplaintDto) {}
