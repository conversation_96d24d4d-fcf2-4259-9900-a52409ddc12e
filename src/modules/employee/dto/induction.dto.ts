import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsDateString, IsInt } from 'class-validator';

export class CreateInductionDto {
  @ApiProperty({
    description: 'The start date of the induction',
    example: '2023-06-01T09:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  @ApiProperty({
    description: 'The end date of the induction',
    example: '2023-06-05T17:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  endDate: string;

  @ApiProperty({
    description: 'The name of the trainer',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  trainer: string;

  @ApiProperty({
    description: 'The feedback for the induction',
    example: 'Very informative and well-structured.',
  })
  @IsString()
  @IsNotEmpty()
  feedback: string;

  @ApiProperty({
    description: 'The ID of the employee associated with the induction',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  employeeId: number;
}
export class UpdateInductionDto extends PartialType(CreateInductionDto) {}
