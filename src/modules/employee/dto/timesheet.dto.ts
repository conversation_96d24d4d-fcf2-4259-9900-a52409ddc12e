import {
  IsInt,
  IsString,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>y,
  ValidateNested,
} from 'class-validator';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreateWorkDayDto {
  @ApiProperty({ description: 'Date de la journée de travail' })
  @IsString()
  date: Date;

  @ApiProperty({ description: "Heure d'arrivée" })
  @IsString()
  arrivalTime: Date;

  @ApiProperty({ description: 'Heure de départ' })
  @IsString()
  departureTime: Date;
}

export class UpdateWorkDayDto {
  @ApiProperty({ description: 'ID de la journée de travail' })
  @IsString()
  workDayId: string;

  @ApiPropertyOptional({ description: 'Date de la journée de travail' })
  @IsOptional()
  @IsString()
  date?: Date;

  @ApiPropertyOptional({ description: "Heure d'arrivée" })
  @IsOptional()
  @IsString()
  arrivalTime?: Date;

  @ApiPropertyOptional({ description: 'Heure de départ' })
  @IsOptional()
  @IsString()
  departureTime?: Date;
}

export class CreateTimesheetDto {
  @ApiProperty({ description: "ID de l'employé" })
  @IsString()
  employeeId: string;

  @ApiProperty({ description: 'Début de la période' })
  @IsString()
  periodStart: Date;

  @ApiProperty({ description: 'Fin de la période' })
  @IsString()
  periodEnd: Date;
}

export class UpdateTimesheetDto {
  @ApiPropertyOptional({ description: 'Début de la période' })
  @IsOptional()
  @IsString()
  periodStart?: Date;

  @ApiPropertyOptional({ description: 'Fin de la période' })
  @IsOptional()
  @IsString()
  periodEnd?: Date;

  @ApiPropertyOptional({
    description: 'Journées de travail',
    type: [CreateWorkDayDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateWorkDayDto)
  workDays?: CreateWorkDayDto[];
}
