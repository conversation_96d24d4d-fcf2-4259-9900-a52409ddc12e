import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsInt, IsString } from 'class-validator';

export class CreatePerformanceEvaluationDto {
  @ApiProperty({
    description: 'Start date of the evaluation period',
    example: '2023-01-01T00:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  periodStart: string;

  @ApiProperty({
    description: 'End date of the evaluation period',
    example: '2023-03-31T23:59:59Z',
  })
  @IsDateString()
  @IsNotEmpty()
  periodEnd: string;

  @ApiProperty({
    description: 'Goals for the evaluation period',
    example: 'Achieve 95% customer satisfaction rate.',
  })
  @IsString()
  @IsNotEmpty()
  goals: string;

  @ApiProperty({
    description: 'Employee self-evaluation for the period',
    example:
      'I believe I have met all the set goals and exceeded expectations in customer service.',
  })
  @IsString()
  @IsNotEmpty()
  selfEvaluation: string;

  @ApiProperty({
    description: 'Manager evaluation of the employee for the period',
    example:
      'The employee consistently delivered exceptional results and demonstrated leadership skills.',
  })
  @IsString()
  @IsNotEmpty()
  managerEvaluation: string;

  @ApiProperty({
    description: 'The ID of the employee being evaluated',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  employeeId: number;
}
export class UpdatePerformanceEvaluationDto extends PartialType(
  CreatePerformanceEvaluationDto,
) {}
