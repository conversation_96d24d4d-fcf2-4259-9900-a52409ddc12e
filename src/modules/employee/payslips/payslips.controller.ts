// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
// import { PayslipService } from './payslips.service';
// import { CreatePayslipDto, UpdatePayslipDto } from '../dto/payslips.dto';

// @ApiTags('payslips')
// @Controller('api/v1/payslips')
// export class PayslipController {
//   constructor(private readonly payslipService: PayslipService) {}

//   @Post()
//   @ApiOperation({ summary: 'Créer une nouvelle fiche de paie' })
//   @ApiResponse({
//     status: 201,
//     description: 'La fiche de paie a été créée avec succès.',
//   })
//   @ApiResponse({ status: 400, description: "Données d'entrée invalides" })
//   async create(@Body() createPayslipDto: CreatePayslipDto) {
//     return this.payslipService.create(createPayslipDto);
//   }

//   @Get(':employeeId')
//   @ApiOperation({
//     summary: 'Obtenir toutes les fiches de paie pour un employé',
//   })
//   @ApiParam({
//     name: 'employeeId',
//     description: "ID de l'employé pour lequel récupérer les fiches de paie",
//     type: Number,
//   })
//   @ApiResponse({
//     status: 200,
//     description: "Liste des fiches de paie pour l'employé spécifié",
//   })
//   @ApiResponse({
//     status: 404,
//     description: "Aucune fiche de paie trouvée pour l'employé spécifié",
//   })
//   async findAll(@Param('employeeId') employeeId: number) {
//     return this.payslipService.findAll(+employeeId);
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Obtenir une fiche de paie spécifique par ID' })
//   @ApiParam({
//     name: 'id',
//     description: 'ID de la fiche de paie à récupérer',
//     type: Number,
//   })
//   @ApiResponse({
//     status: 200,
//     description: 'La fiche de paie a été récupérée avec succès.',
//   })
//   @ApiResponse({ status: 404, description: 'Fiche de paie non trouvée' })
//   async findOne(@Param('id') id: number) {
//     return this.payslipService.findOne(+id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Mettre à jour une fiche de paie par ID' })
//   @ApiParam({
//     name: 'id',
//     description: 'ID de la fiche de paie à mettre à jour',
//     type: Number,
//   })
//   @ApiResponse({
//     status: 200,
//     description: 'La fiche de paie a été mise à jour avec succès.',
//   })
//   @ApiResponse({ status: 404, description: 'Fiche de paie non trouvée' })
//   async update(
//     @Param('id') id: number,
//     @Body() updatePayslipDto: UpdatePayslipDto,
//   ) {
//     return this.payslipService.update(+id, updatePayslipDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Supprimer une fiche de paie par ID' })
//   @ApiParam({
//     name: 'id',
//     description: 'ID de la fiche de paie à supprimer',
//     type: Number,
//   })
//   @ApiResponse({
//     status: 200,
//     description: 'La fiche de paie a été supprimée avec succès.',
//   })
//   @ApiResponse({ status: 404, description: 'Fiche de paie non trouvée' })
//   async remove(@Param('id') id: number) {
//     return this.payslipService.remove(+id);
//   }
// }
