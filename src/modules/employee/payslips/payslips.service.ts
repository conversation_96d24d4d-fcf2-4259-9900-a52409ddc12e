// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import { SalaryService } from '../salary/salary.service';
// import { TimesheetService } from '../timesheet/timesheet.service';
// import { CreatePayslipDto, UpdatePayslipDto } from '../dto/payslips.dto';

// @Injectable()
// export class PayslipService {
//   constructor(
//     private readonly prisma: PrismaService,
//     private readonly salaryService: SalaryService,
//     private readonly timesheetService: TimesheetService,
//   ) {}

//   async create(createPayslipDto: CreatePayslipDto) {
//     const {
//       employeeId,
//       month,
//       year,
//       grossSalary,
//       taxDeductions,
//       socialSecurity,
//       otherDeductions,
//       netSalary,
//     } = createPayslipDto;

//     // const salary = await this.salaryService.findByEffectiveDate(
//     //   employeeId,
//     //   year,
//     //   month,
//     // );
//     // const timesheets = await this.timesheetService.findAllForMonth(
//     //   employeeId,
//     //   month,
//     //   year,
//     // );

//     // if (!salary) {
//     //   throw new NotFoundException(
//     //     `Salary for employee ID ${employeeId} not found`,
//     //   );
//     // }

//     // const grossSalary = this.calculateGrossSalary(salary, timesheets);
//     // const taxDeductions = this.calculateTaxDeductions(grossSalary);
//     // const socialSecurity = this.calculateSocialSecurity(grossSalary);
//     // const otherDeductions = createPayslipDto.otherDeductions
//     //   ? createPayslipDto.otherDeductions
//     //   : 0;
//     // const netSalary =
//     //   grossSalary - taxDeductions - socialSecurity - otherDeductions;

//     return this.prisma.payslip.create({
//       data: {
//         employee: { connect: { employeeId } },
//         month,
//         year,
//         grossSalary,
//         taxDeductions,
//         socialSecurity,
//         otherDeductions,
//         netSalary,
//       },
//     });
//   }

//   async findAll(employeeId: number) {
//     return this.prisma.payslip.findMany({ where: { employeeId } });
//   }

//   async findOne(id: number) {
//     const payslip = await this.prisma.payslip.findUnique({
//       where: { payslipId: id },
//     });

//     if (!payslip) {
//       throw new NotFoundException(`Payslip with ID ${id} not found`);
//     }

//     return payslip;
//   }

//   async update(id: number, updatePayslipDto: UpdatePayslipDto) {
//     const payslip = await this.findOne(id);

//     if (!payslip) {
//       throw new NotFoundException(`Payslip with ID ${id} not found`);
//     }

//     const {
//       grossSalary,
//       taxDeductions,
//       socialSecurity,
//       otherDeductions,
//       netSalary,
//     } = updatePayslipDto;

//     return this.prisma.payslip.update({
//       where: { payslipId: id },
//       data: {
//         grossSalary: grossSalary ?? payslip.grossSalary,
//         taxDeductions: taxDeductions ?? payslip.taxDeductions,
//         socialSecurity: socialSecurity ?? payslip.socialSecurity,
//         otherDeductions: otherDeductions ?? payslip.otherDeductions,
//         netSalary: netSalary ?? payslip.netSalary,
//       },
//     });
//   }

//   async remove(id: number) {
//     const payslip = await this.findOne(id);

//     if (!payslip) {
//       throw new NotFoundException(`Payslip with ID ${id} not found`);
//     }

//     return this.prisma.payslip.delete({
//       where: { payslipId: id },
//     });
//   }

//   private calculateGrossSalary(salary: any, timesheets: any[]): number {
//     const baseSalary = salary.baseSalary || 0;
//     const housingAllowance = salary.housingAllowance || 0;
//     const transportAllowance = salary.transportAllowance || 0;
//     const bonus = salary.bonus || 0;

//     const overtimeHours = timesheets.reduce(
//       (total, ts) =>
//         total +
//         ts.workDays.reduce((wdTotal, wd) => wdTotal + wd.overtimeHours, 0),
//       0,
//     );
//     const overtimePay = (salary.overtimeRate || 0) * overtimeHours;

//     return (
//       baseSalary + housingAllowance + transportAllowance + bonus + overtimePay
//     );
//   }

//   private calculateTaxDeductions(grossSalary: number): number {
//     return grossSalary * 0.1; // Example: 10% tax
//   }

//   private calculateSocialSecurity(grossSalary: number): number {
//     return grossSalary * 0.05; // Example: 5% social security
//   }

//   private calculateOtherDeductions(): number {
//     return 50; // Example fixed amount
//   }
// }
