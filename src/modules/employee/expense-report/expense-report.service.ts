// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import {
//   CreateExpenseReportDto,
//   UpdateExpenseReportDto,
// } from '../dto/expense-report.dto';

// @Injectable()
// export class ExpenseReportService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createExpenseReportDto: CreateExpenseReportDto) {
//     const { employeeId, ...others } = createExpenseReportDto;

//     return this.prisma.expenseReport.create({
//       data: {
//         ...others,
//         employee: { connect: { employeeId } },
//       },
//     });
//   }

//   async findAll() {
//     return this.prisma.expenseReport.findMany();
//   }

//   async findOne(id: number) {
//     const expenseReport = await this.prisma.expenseReport.findUnique({
//       where: { reportId: id },
//     });

//     if (!expenseReport) {
//       throw new NotFoundException(`Expense report with ID ${id} not found`);
//     }

//     return expenseReport;
//   }

//   async update(id: number, updateExpenseReportDto: UpdateExpenseReportDto) {
//     const expenseReport = await this.findOne(id);

//     if (!expenseReport) {
//       throw new NotFoundException(`Expense report with ID ${id} not found`);
//     }

//     const { description, reportDate, amount, employeeId } =
//       updateExpenseReportDto;

//     return this.prisma.expenseReport.update({
//       where: { reportId: id },
//       data: {
//         description: description ?? expenseReport.description,
//         reportDate: reportDate ?? expenseReport.reportDate,
//         amount: amount ?? expenseReport.amount,
//         employee: employeeId ? { connect: { employeeId } } : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const expenseReport = await this.findOne(id);

//     if (!expenseReport) {
//       throw new NotFoundException(`Expense report with ID ${id} not found`);
//     }

//     return this.prisma.expenseReport.delete({
//       where: { reportId: id },
//     });
//   }
// }
