// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { ExpenseReportService } from './expense-report.service';
// import {
//   CreateExpenseReportDto,
//   UpdateExpenseReportDto,
// } from '../dto/expense-report.dto';

// @ApiTags('expense-reports')
// @Controller('api/v1/company/expense-reports')
// export class ExpenseReportController {
//   constructor(private readonly expenseReportService: ExpenseReportService) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new expense report' })
//   @ApiResponse({
//     status: 201,
//     description: 'The expense report has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createExpenseReportDto: CreateExpenseReportDto) {
//     return this.expenseReportService.create(createExpenseReportDto);
//   }

//   @Get()
//   @ApiOperation({ summary: 'Get all expense reports' })
//   @ApiResponse({ status: 200, description: 'Return all expense reports.' })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll() {
//     return this.expenseReportService.findAll();
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get an expense report by ID' })
//   @ApiResponse({ status: 200, description: 'Return the expense report.' })
//   @ApiResponse({ status: 404, description: 'Expense report not found.' })
//   findOne(@Param('id') id: number) {
//     return this.expenseReportService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update an expense report by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The expense report has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Expense report not found.' })
//   update(
//     @Param('id') id: number,
//     @Body() updateExpenseReportDto: UpdateExpenseReportDto,
//   ) {
//     return this.expenseReportService.update(id, updateExpenseReportDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete an expense report by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The expense report has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Expense report not found.' })
//   remove(@Param('id') id: number) {
//     return this.expenseReportService.remove(id);
//   }
// }
