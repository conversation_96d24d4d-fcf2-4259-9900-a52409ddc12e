import { ApiProperty } from '@nestjs/swagger';
import { Salary } from '@prisma/client';

export class SalaryResponseDto implements Partial<Salary> {
  @ApiProperty({ description: 'Salary record ID', format: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Employee ID', format: 'uuid' })
  employeeId: string;

  @ApiProperty({ description: 'Base salary amount' })
  baseSalary: number;

  @ApiProperty({ description: 'Housing allowance amount' })
  housingAllowance: number;

  @ApiProperty({ description: 'Transport allowance amount' })
  transportAllowance: number;

  @ApiProperty({ description: 'Bonus amount' })
  bonus: number;

  @ApiProperty({ description: 'Overtime hours' })
  overtimeHours: number;

  @ApiProperty({ description: 'Overtime rate per hour' })
  overtimeRate: number;

  @ApiProperty({ description: 'Effective date (YYYY-MM-DD)' })
  effectiveDate: Date;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
