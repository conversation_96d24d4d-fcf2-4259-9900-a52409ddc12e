import { ApiProperty } from '@nestjs/swagger';
import { Payslip } from '@prisma/client';

export class PayslipResponseDto implements Partial<Payslip> {
  @ApiProperty({ description: 'Payslip ID', format: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Employee ID', format: 'uuid' })
  employeeId: string;

  @ApiProperty({ description: 'Month (1-12)' })
  month: number;

  @ApiProperty({ description: 'Year' })
  year: number;

  @ApiProperty({ description: 'Gross salary amount' })
  grossSalary: number;

  @ApiProperty({ description: 'Tax deductions amount' })
  taxDeductions: number;

  @ApiProperty({ description: 'Social security contributions' })
  socialSecurity: number;

  @ApiProperty({ description: 'Other deductions amount' })
  otherDeductions: number;

  @ApiProperty({ description: 'Net salary amount' })
  netSalary: number;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
