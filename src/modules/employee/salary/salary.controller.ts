import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { SalaryService } from './salary.service';
import { CreateSalaryDto, UpdateSalaryDto } from '../dto/salary.dto';
import { SalaryResponseDto } from './dto/salary-response.dto';
import { PayslipResponseDto } from '../dto/payslips.dto';

@ApiTags('Salaries')
@Controller('api/v1/salaries')
export class SalaryController {
  constructor(private readonly salaryService: SalaryService) {}

  // Salary Endpoints
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new salary record',
    description: 'Creates a new salary configuration for an employee',
  })
  @ApiBody({ type: CreateSalaryDto })
  @ApiCreatedResponse({
    description: 'Salary record created successfully',
    type: SalaryResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or duplicate effective date',
  })
  @ApiNotFoundResponse({ description: 'Employee not found' })
  async create(@Body() createSalaryDto: CreateSalaryDto) {
    return this.salaryService.create(createSalaryDto);
  }

  @Get('employee/:employeeId')
  @ApiOperation({
    summary: 'Get all salaries for an employee',
    description: 'Retrieves all salary records for a specific employee',
  })
  @ApiParam({
    name: 'employeeId',
    description: 'UUID of the employee',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponse({
    description: 'List of employee salaries',
    type: [SalaryResponseDto],
  })
  @ApiNotFoundResponse({ description: 'Employee not found' })
  async findAll(@Param('employeeId') employeeId: string) {
    return this.salaryService.findAll(employeeId);
  }

  @Get('company/:companyId')
  @ApiOperation({
    summary: 'Get all salaries for company',
    description: 'Retrieves all salary records for a specific company',
  })
  @ApiParam({
    name: 'companyId',
    description: 'UUID of the company',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponse({
    description: 'List of company salaries',
    type: [SalaryResponseDto],
  })
  @ApiNotFoundResponse({ description: 'Employee not found' })
  async findAllSalariesCompany(@Param('companyId') companyId: string) {
    return this.salaryService.findAllSalariesCompany(companyId);
  }
  @Get('current/:employeeId')
  @ApiOperation({
    summary: 'Get current salary for an employee',
    description: 'Retrieves the most recent salary record for an employee',
  })
  @ApiParam({
    name: 'employeeId',
    description: 'UUID of the employee',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponse({
    description: 'Current salary details',
    type: SalaryResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Employee or salary not found' })
  async getCurrentSalary(@Param('employeeId') employeeId: string) {
    return this.salaryService.getCurrentSalary(employeeId);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a specific salary record',
    description: 'Retrieves details of a specific salary record',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the salary record',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponse({
    description: 'Salary record details',
    type: SalaryResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Salary record not found' })
  async findOne(@Param('id') id: string) {
    return this.salaryService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update a salary record',
    description: 'Updates an existing salary record',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the salary record to update',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({ type: UpdateSalaryDto })
  @ApiOkResponse({
    description: 'Salary record updated successfully',
    type: SalaryResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Salary record not found' })
  @ApiBadRequestResponse({
    description: 'Invalid input data or duplicate effective date',
  })
  async update(
    @Param('id') id: string,
    @Body() updateSalaryDto: UpdateSalaryDto,
  ) {
    return this.salaryService.update(id, updateSalaryDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete a salary record',
    description: 'Deletes a specific salary record',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the salary record to delete',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Salary record deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Salary record not found' })
  async remove(@Param('id') id: string) {
    return this.salaryService.remove(id);
  }

  // Payslip Endpoints
  @Post('payslips/generate')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Generate a payslip',
    description:
      'Generates a payslip for an employee for a specific month/year',
  })
  @ApiQuery({
    name: 'employeeId',
    description: 'UUID of the employee',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'year',
    description: 'Year for the payslip',
    example: 2023,
  })
  @ApiQuery({
    name: 'month',
    description: 'Month for the payslip (1-12)',
    example: 6,
  })
  @ApiCreatedResponse({
    description: 'Payslip generated successfully',
    type: PayslipResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid month/year parameters' })
  @ApiNotFoundResponse({
    description: 'Employee not found or no valid salary for period',
  })
  async generatePayslip(
    @Query('employeeId') employeeId: string,
    @Query('year') year: number,
    @Query('month') month: number,
  ) {
    return this.salaryService.generatePayslip(employeeId, year, month);
  }

  @Get('payslips/:id')
  @ApiOperation({
    summary: 'Get a specific payslip',
    description: 'Retrieves details of a specific payslip',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the payslip',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponse({
    description: 'Payslip details',
    type: PayslipResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Payslip not found' })
  async getPayslip(@Param('id') id: string) {
    return this.salaryService.getPayslip(id);
  }

  @Get('payslips/employee/:employeeId')
  @ApiOperation({
    summary: 'Get all payslips for an employee',
    description: 'Retrieves all payslips for a specific employee',
  })
  @ApiParam({
    name: 'employeeId',
    description: 'UUID of the employee',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponse({
    description: 'List of employee payslips',
    type: [PayslipResponseDto],
  })
  @ApiNotFoundResponse({ description: 'Employee not found' })
  async getEmployeePayslips(@Param('employeeId') employeeId: string) {
    return this.salaryService.getEmployeePayslips(employeeId);
  }

  @Get('payslips/company/:companyId')
  @ApiOperation({
    summary: 'Get all payslips for a company',
    description: 'Retrieves all payslips for a specific company',
  })
  @ApiParam({
    name: 'companyId',
    description: 'UUID of the company',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponse({
    description: 'List of company payslips',
    type: [PayslipResponseDto],
  })
  async getAllPayslips(@Param('companyId') companyId: string) {
    return this.salaryService.getAllPayslips(companyId);
  }

  @Patch('payslips/:id')
  @ApiOperation({
    summary: 'Update a payslip',
    description: 'Updates an existing payslip',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the payslip to update',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({ type: PayslipResponseDto })
  @ApiOkResponse({
    description: 'Payslip updated successfully',
    type: PayslipResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Payslip not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  async updatePayslip(
    @Param('id') id: string,
    @Body() updateData: Partial<PayslipResponseDto>,
  ) {
    return this.salaryService.updatePayslip(id, updateData);
  }

  @Delete('payslips/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete a payslip',
    description: 'Deletes a specific payslip',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the payslip to delete',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Payslip deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Payslip not found' })
  async deletePayslip(@Param('id') id: string) {
    return this.salaryService.deletePayslip(id);
  }

  @Get('payslips/period')
  @ApiOperation({
    summary: 'Get payslip by period',
    description: 'Retrieves a payslip for a specific employee and period',
  })
  @ApiQuery({
    name: 'employeeId',
    description: 'UUID of the employee',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'year',
    description: 'Year of the payslip',
    example: 2023,
  })
  @ApiQuery({
    name: 'month',
    description: 'Month of the payslip (1-12)',
    example: 6,
  })
  @ApiOkResponse({
    description: 'Payslip details',
    type: PayslipResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Payslip not found' })
  async getPayslipByPeriod(
    @Query('employeeId') employeeId: string,
    @Query('year') year: number,
    @Query('month') month: number,
  ) {
    return this.salaryService.getPayslipByPeriod(employeeId, year, month);
  }

  // Utility Endpoints
  @Get('history/:employeeId')
  @ApiOperation({
    summary: 'Get salary history',
    description: 'Retrieves salary history for an employee',
  })
  @ApiParam({
    name: 'employeeId',
    description: 'UUID of the employee',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponse({
    description: 'Salary history',
    type: [SalaryResponseDto],
  })
  @ApiNotFoundResponse({ description: 'Employee not found' })
  async getSalaryHistory(@Param('employeeId') employeeId: string) {
    return this.salaryService.getSalaryHistory(employeeId);
  }
}
