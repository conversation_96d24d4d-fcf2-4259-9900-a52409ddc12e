import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';

@Injectable()
export class PayrollCalculator {
  private readonly WORK_DAYS_PER_MONTH = 22;
  private readonly WORK_HOURS_PER_DAY = 8;

  constructor(private readonly prisma: PrismaService) {}

  async calculatePayslip(input: {
    salary: any;
    timesheet?: any;
    unpaidLeaves?: any[];
    employee: any;
    periodStart: Date;
    periodEnd: Date;
  }) {
    const { salary, timesheet, unpaidLeaves = [], employee } = input;

    // 1. Calcul des heures supplémentaires
    const overtimeHours = timesheet?.totalOvertimeHours || 0;
    const overtimeRate = this.calculateOvertimeRate(salary, employee);
    const overtimePay = overtimeHours * overtimeRate;

    // 2. Calcul des absences non payées
    const unpaidDays = this.calculateUnpaidDays(
      unpaidLeaves,
      input.periodStart,
      input.periodEnd,
    );
    const dailyRate = salary.baseSalary / this.WORK_DAYS_PER_MONTH;
    const unpaidDeduction = unpaidDays * dailyRate;

    // 3. Calcul du salaire brut
    const grossSalary =
      salary.baseSalary +
      (salary.housingAllowance || 0) +
      (salary.transportAllowance || 0) +
      overtimePay -
      unpaidDeduction;

    // 4. Calcul des déductions
    const deductions = this.calculateDeductions(grossSalary, employee);

    return {
      grossSalary,
      taxDeductions: deductions.taxDeductions,
      socialSecurity: deductions.socialSecurity,
      otherDeductions: deductions.otherDeductions,
      netSalary:
        grossSalary -
        (deductions.taxDeductions +
          deductions.socialSecurity +
          deductions.otherDeductions),
    };
  }

  private calculateOvertimeRate(salary: any, employee: any): number {
    const config = employee.company.payrollConfiguration;
    return (
      salary.overtimeRate ||
      (salary.baseSalary /
        (this.WORK_DAYS_PER_MONTH * this.WORK_HOURS_PER_DAY)) *
        (config?.overtimeMultiplier || 1.5)
    );
  }

  private calculateUnpaidDays(
    leaves: any[],
    periodStart: Date,
    periodEnd: Date,
  ): number {
    return leaves.reduce((days, leave) => {
      const start = new Date(
        Math.max(leave.startDate.getTime(), periodStart.getTime()),
      );
      const end = new Date(
        Math.min(leave.endDate.getTime(), periodEnd.getTime()),
      );
      return (
        days + (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24) + 1
      );
    }, 0);
  }

  private calculateDeductions(grossSalary: number, employee: any) {
    const { taxSettings } = employee.company;

    // 1. Impôt sur le revenu
    const taxableIncome = Math.max(
      0,
      grossSalary - (taxSettings.incomeTaxThreshold || 0),
    );
    const taxDeductions = taxableIncome * (taxSettings.incomeTaxRate / 100);

    // 2. Sécurité sociale
    const socialSecurityBase = Math.min(
      grossSalary,
      taxSettings.socialSecurityThreshold || Infinity,
    );
    const socialSecurity =
      socialSecurityBase * (taxSettings.socialSecurityRate / 100);

    // 3. Autres déductions
    const otherDeductions = [
      taxSettings.healthInsuranceRate,
      taxSettings.pensionContributionRate,
      taxSettings.unEmploymentInsuranceRate,
    ].reduce((sum, rate) => sum + (rate ? grossSalary * (rate / 100) : 0), 0);

    return {
      taxDeductions,
      socialSecurity,
      otherDeductions,
    };
  }
}
