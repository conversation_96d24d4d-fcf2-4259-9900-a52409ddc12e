import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
import { CreateSalaryDto, UpdateSalaryDto } from '../dto/salary.dto';
import {
  Salary,
  Payslip,
  EmployeeData,
  Company,
  LeaveTypeEnum,
} from '@prisma/client';
import { PayrollCalculator } from './utils/payroll-calculator';

@Injectable()
export class SalaryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly payrollCalculator: PayrollCalculator,
  ) {}

  private async validateEmployee(employeeId: string): Promise<{
    employee: EmployeeData & {
      company: Company & {
        taxSettings: any;
        payrollConfiguration: any;
      };
    };
  }> {
    const employee = await this.prisma.employeeData.findUnique({
      where: { id: employeeId },
      include: {
        company: {
          include: {
            taxSettings: true,
            payrollConfiguration: true,
          },
        },
      },
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${employeeId} not found`);
    }

    return { employee };
  }

  // Méthodes pour Salary
  async create(createSalaryDto: CreateSalaryDto): Promise<Salary> {
    const { employee } = await this.validateEmployee(
      createSalaryDto.employeeId,
    );

    const existingSalary = await this.prisma.salary.findFirst({
      where: {
        employeeId: createSalaryDto.employeeId,
        effectiveDate: new Date(createSalaryDto.effectiveDate),
      },
    });

    if (existingSalary) {
      throw new Error(
        `A salary already exists for this employee with the same effective date`,
      );
    }

    return this.prisma.salary.create({
      data: {
        employeeId: createSalaryDto.employeeId,
        baseSalary: createSalaryDto.baseSalary,
        housingAllowance: createSalaryDto.housingAllowance || 0,
        transportAllowance: createSalaryDto.transportAllowance || 0,
        bonus: createSalaryDto.bonus || 0,
        overtimeHours: createSalaryDto.overtimeHours || 0,
        overtimeRate: createSalaryDto.overtimeRate || 0,
        effectiveDate: new Date(createSalaryDto.effectiveDate),
      },
    });
  }

  async findAll(employeeId: string): Promise<Salary[]> {
    await this.validateEmployee(employeeId);
    return this.prisma.salary.findMany({
      where: { employeeId },
      orderBy: { effectiveDate: 'desc' },
    });
  }

  async findAllSalariesCompany(companyId: string): Promise<Salary[]> {
    return this.prisma.salary.findMany({
      where: {
        employee: {
          companyId,
        },
      },
      orderBy: { effectiveDate: 'desc' },
    });
  }
  async findOne(id: string): Promise<Salary> {
    const salary = await this.prisma.salary.findUnique({
      where: { id },
    });

    if (!salary) {
      throw new NotFoundException(`Salary with ID ${id} not found`);
    }

    return salary;
  }

  async update(id: string, updateSalaryDto: UpdateSalaryDto): Promise<Salary> {
    const existingSalary = await this.findOne(id);
    const { employee } = await this.validateEmployee(existingSalary.employeeId);

    if (updateSalaryDto.effectiveDate) {
      const newEffectiveDate = new Date(updateSalaryDto.effectiveDate);
      if (
        newEffectiveDate.toISOString() !==
        existingSalary.effectiveDate.toISOString()
      ) {
        const conflictingSalary = await this.prisma.salary.findFirst({
          where: {
            employeeId: existingSalary.employeeId,
            effectiveDate: newEffectiveDate,
            NOT: { id },
          },
        });

        if (conflictingSalary) {
          throw new Error(
            `Another salary already exists for this employee with the same effective date`,
          );
        }
      }
    }

    return this.prisma.salary.update({
      where: { id },
      data: {
        baseSalary: updateSalaryDto.baseSalary ?? existingSalary.baseSalary,
        housingAllowance:
          updateSalaryDto.housingAllowance ?? existingSalary.housingAllowance,
        transportAllowance:
          updateSalaryDto.transportAllowance ??
          existingSalary.transportAllowance,
        bonus: updateSalaryDto.bonus ?? existingSalary.bonus,
        overtimeHours:
          updateSalaryDto.overtimeHours ?? existingSalary.overtimeHours,
        overtimeRate:
          updateSalaryDto.overtimeRate ?? existingSalary.overtimeRate,
        effectiveDate: updateSalaryDto.effectiveDate
          ? new Date(updateSalaryDto.effectiveDate)
          : existingSalary.effectiveDate,
      },
    });
  }

  async remove(id: string): Promise<Salary> {
    const salary = await this.findOne(id);
    return this.prisma.salary.delete({
      where: { id },
    });
  }

  async findByEffectiveDate(
    employeeId: string,
    date: Date,
  ): Promise<Salary | null> {
    await this.validateEmployee(employeeId);
    return this.prisma.salary.findFirst({
      where: {
        employeeId,
        effectiveDate: {
          lte: date,
        },
      },
      orderBy: {
        effectiveDate: 'desc',
      },
    });
  }

  // Méthodes pour Payslip (Fiches de paie)
  async generatePayslip(
    employeeId: string,
    year: number,
    month: number,
  ): Promise<Payslip> {
    const { employee } = await this.validateEmployee(employeeId);
    const periodStart = new Date(year, month - 1, 1);
    const periodEnd = new Date(year, month, 0);

    const salary = await this.findByEffectiveDate(employeeId, periodEnd);
    console.log(periodEnd);

    if (!salary) {
      throw new NotFoundException(
        `No valid salary found for employee ${employeeId} for ${month}/${year}`,
      );
    }

    const timesheet = await this.prisma.timesheet.findFirst({
      where: {
        employeeId,
        periodStart: { gte: periodStart },
        periodEnd: { lte: periodEnd },
      },
      include: { workDays: true },
    });

    const unpaidLeaves = await this.prisma.leave.findMany({
      where: {
        employeeId,
        leaveType: LeaveTypeEnum.UNPAID,
        status: 'APPROVED',
        startDate: { lte: periodEnd },
        endDate: { gte: periodStart },
      },
    });

    const payslipData = await this.payrollCalculator.calculatePayslip({
      salary,
      timesheet,
      unpaidLeaves,
      employee,
      periodStart,
      periodEnd,
    });

    return this.prisma.payslip.create({
      data: {
        employeeId,
        month: Number(month),
        year: Number(year),
        grossSalary: payslipData.grossSalary,
        taxDeductions: payslipData.taxDeductions,
        socialSecurity: payslipData.socialSecurity,
        otherDeductions: payslipData.otherDeductions,
        netSalary: payslipData.netSalary,
      },
    });
  }

  async getPayslip(id: string): Promise<Payslip> {
    const payslip = await this.prisma.payslip.findUnique({
      where: { id },
      include: {
        employee: {
          include: {
            user: {
              include: {
                profile: true,
              },
            },
          },
        },
      },
    });

    if (!payslip) {
      throw new NotFoundException(`Payslip with ID ${id} not found`);
    }

    return payslip;
  }

  async getEmployeePayslips(employeeId: string): Promise<Payslip[]> {
    await this.validateEmployee(employeeId);
    return this.prisma.payslip.findMany({
      where: { employeeId },
      orderBy: [{ year: 'desc' }, { month: 'desc' }],
    });
  }

  async getAllPayslips(companyId: string): Promise<Payslip[]> {
    return this.prisma.payslip.findMany({
      where: {
        employee: {
          companyId,
        },
      },
      include: {
        employee: {
          include: {
            user: {
              include: {
                profile: true,
              },
            },
          },
        },
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }],
    });
  }

  async updatePayslip(
    id: string,
    updateData: Partial<Payslip>,
  ): Promise<Payslip> {
    const existingPayslip = await this.getPayslip(id);
    return this.prisma.payslip.update({
      where: { id },
      data: {
        grossSalary: updateData.grossSalary ?? existingPayslip.grossSalary,
        taxDeductions:
          updateData.taxDeductions ?? existingPayslip.taxDeductions,
        socialSecurity:
          updateData.socialSecurity ?? existingPayslip.socialSecurity,
        otherDeductions:
          updateData.otherDeductions ?? existingPayslip.otherDeductions,
        netSalary: updateData.netSalary ?? existingPayslip.netSalary,
      },
    });
  }

  async deletePayslip(id: string): Promise<Payslip> {
    const payslip = await this.getPayslip(id);
    return this.prisma.payslip.delete({
      where: { id },
    });
  }

  async getPayslipByPeriod(
    employeeId: string,
    year: number,
    month: number,
  ): Promise<Payslip | null> {
    return this.prisma.payslip.findFirst({
      where: {
        employeeId,
        year,
        month,
      },
    });
  }

  // Méthodes utilitaires
  async getSalaryHistory(employeeId: string): Promise<Salary[]> {
    await this.validateEmployee(employeeId);
    return this.prisma.salary.findMany({
      where: { employeeId },
      orderBy: { effectiveDate: 'desc' },
    });
  }

  async getCurrentSalary(employeeId: string): Promise<Salary | null> {
    await this.validateEmployee(employeeId);
    return this.prisma.salary.findFirst({
      where: { employeeId },
      orderBy: { effectiveDate: 'desc' },
    });
  }
}
