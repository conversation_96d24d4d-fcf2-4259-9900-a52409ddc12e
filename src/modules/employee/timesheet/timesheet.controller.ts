import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Patch,
  Delete,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { TimesheetService } from './timesheet.service';
import {
  CreateTimesheetDto,
  CreateWorkDayDto,
  UpdateTimesheetDto,
  UpdateWorkDayDto,
} from '../dto/timesheet.dto';

@ApiTags('timesheets')
@Controller('api/v1/timesheets')
export class TimesheetController {
  constructor(private readonly timesheetService: TimesheetService) {}

  @Post()
  @ApiOperation({ summary: 'Créer une nouvelle feuille de temps' })
  @ApiBody({ type: CreateTimesheetDto })
  @ApiResponse({
    status: 201,
    description: 'La feuille de temps a été créée avec succès',
  })
  @ApiResponse({ status: 400, description: 'Requête invalide' })
  async create(@Body() createTimesheetDto: CreateTimesheetDto) {
    return this.timesheetService.create(createTimesheetDto);
  }

  @Get(':companyId')
  @ApiOperation({
    summary: 'Obtenir toutes les feuilles de temps pour un employé',
  })
  @ApiParam({
    name: 'employeeId',
    type: Number,
    description: "ID de l'employé",
  })
  @ApiResponse({
    status: 200,
    description: "Liste des feuilles de temps de l'employé",
  })
  @ApiResponse({ status: 404, description: 'Employé non trouvé' })
  async findAll(
    @Param('companyId') companyId: string,
    @Query('employeeId') employeeId: string,
  ) {
    return this.timesheetService.findAll(companyId, employeeId);
  }

  @Get('timesheet/:id')
  @ApiOperation({ summary: 'Obtenir une feuille de temps spécifique' })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'ID de la feuille de temps',
  })
  @ApiResponse({ status: 200, description: 'Détails de la feuille de temps' })
  @ApiResponse({ status: 404, description: 'Feuille de temps non trouvée' })
  async findOne(@Param('id') id: string) {
    return this.timesheetService.findOne(id);
  }

  @Patch('timesheet/:id')
  @ApiOperation({ summary: 'Mettre à jour une feuille de temps' })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'ID de la feuille de temps',
  })
  @ApiBody({ type: UpdateTimesheetDto })
  @ApiResponse({
    status: 200,
    description: 'Feuille de temps mise à jour avec succès',
  })
  @ApiResponse({ status: 404, description: 'Feuille de temps non trouvée' })
  @ApiResponse({ status: 400, description: 'Requête invalide' })
  async update(
    @Param('id') id: string,
    @Body() updateTimesheetDto: UpdateTimesheetDto,
  ) {
    return this.timesheetService.update(id, updateTimesheetDto);
  }

  @Delete('timesheet/:id')
  @ApiOperation({ summary: 'Supprimer une feuille de temps' })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'ID de la feuille de temps',
  })
  @ApiResponse({
    status: 200,
    description: 'Feuille de temps supprimée avec succès',
  })
  @ApiResponse({ status: 404, description: 'Feuille de temps non trouvée' })
  async remove(@Param('id') id: string) {
    return this.timesheetService.remove(id);
  }

  @Post('timesheet/:id/workdays')
  @ApiOperation({
    summary: 'Ajouter des jours de travail à une feuille de temps',
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'ID de la feuille de temps',
  })
  @ApiBody({ type: [CreateWorkDayDto] })
  @ApiResponse({
    status: 201,
    description: 'Jours de travail ajoutés avec succès',
  })
  @ApiResponse({ status: 400, description: 'Requête invalide' })
  async addWorkDays(
    @Param('id') id: string,
    @Body() workDays: CreateWorkDayDto[],
  ) {
    return this.timesheetService.createWorkDays(id, workDays);
  }

  @Patch('workday/:id')
  @ApiOperation({ summary: 'Mettre à jour un jour de travail' })
  @ApiParam({ name: 'id', type: Number, description: 'ID du jour de travail' })
  @ApiBody({ type: UpdateWorkDayDto })
  @ApiResponse({
    status: 200,
    description: 'Jour de travail mis à jour avec succès',
  })
  @ApiResponse({ status: 404, description: 'Jour de travail non trouvé' })
  @ApiResponse({ status: 400, description: 'Requête invalide' })
  async updateWorkDay(@Body() updateWorkDayDto: UpdateWorkDayDto) {
    return this.timesheetService.updateWorkDays([updateWorkDayDto]);
  }
}
