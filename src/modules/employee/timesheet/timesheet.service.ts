import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
import {
  CreateTimesheetDto,
  UpdateTimesheetDto,
  CreateWorkDayDto,
  UpdateWorkDayDto,
} from '../dto/timesheet.dto';

@Injectable()
export class TimesheetService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createTimesheetDto: CreateTimesheetDto) {
    const { employeeId, periodStart, periodEnd } = createTimesheetDto;

    // Vérifier s'il existe déjà un timesheet pour cet employé sur cette période
    const existingTimesheet = await this.prisma.timesheet.findFirst({
      where: {
        employeeId,
        OR: [
          // Cas 1: La nouvelle période commence pendant une période existante
          {
            periodStart: { lte: periodStart },
            periodEnd: { gte: periodStart },
          },
          // Cas 2: La nouvelle période se termine pendant une période existante
          {
            periodStart: { lte: periodEnd },
            periodEnd: { gte: periodEnd },
          },
          // Cas 3: La nouvelle période englobe complètement une période existante
          {
            periodStart: { gte: periodStart },
            periodEnd: { lte: periodEnd },
          },
        ],
      },
    });

    if (existingTimesheet) {
      throw new ConflictException(
        `Un timesheet existe déjà pour cet employé sur la période du ${existingTimesheet.periodStart.toLocaleDateString()} au ${existingTimesheet.periodEnd.toLocaleDateString()}`,
      );
    }

    const timesheet = await this.prisma.timesheet.create({
      data: {
        employee: { connect: { id: employeeId } },
        periodStart,
        periodEnd,
        totalOvertimeHours: 0,
        totalRegularHours: 0,
        totalUndertimeHours: 0,
      },
    });

    return timesheet;
  }

  async createWorkDays(timesheetId: string, workDays: CreateWorkDayDto[]) {
    for (const workDay of workDays) {
      const { date, arrivalTime, departureTime } = workDay;
      const { regularHours, overtimeHours, undertimeHours } =
        this.calculateHours(arrivalTime, departureTime);

      await this.prisma.workDay.create({
        data: {
          date,
          arrivalTime,
          departureTime,
          regularHours,
          overtimeHours,
          undertimeHours,
          timesheet: { connect: { id: timesheetId } },
        },
      });
    }
  }

  async findAll(companyId: string, employeeId?: string) {
    const timesheets = await this.prisma.timesheet.findMany({
      where: {
        employee: {
          companyId,
          ...(employeeId ? { id: employeeId } : {}),
        },
      },
      include: { workDays: true },
    });

    return timesheets.map((timesheet) => {
      // Filtrer seulement les journées complètes (avec departureTime)
      const completedWorkDays = timesheet.workDays.filter(
        (day) => day.departureTime !== null && day.departureTime !== undefined,
      );

      return {
        ...timesheet,
        workDays: completedWorkDays, // Optionnel: retourner seulement les journées complètes
        totalRegularHours: completedWorkDays.reduce(
          (sum, day) => sum + day.regularHours,
          0,
        ),
        totalOvertimeHours: completedWorkDays.reduce(
          (sum, day) => sum + day.overtimeHours,
          0,
        ),
        totalUndertimeHours: completedWorkDays.reduce(
          (sum, day) => sum + day.undertimeHours,
          0,
        ),
      };
    });
  }

  async findOne(id: string) {
    const timesheet = await this.prisma.timesheet.findUnique({
      where: { id },
      include: { workDays: true },
    });

    if (!timesheet) {
      throw new NotFoundException(`Timesheet with ID ${id} not found`);
    }

    // Filtrer seulement les journées complètes (avec departureTime)
    const completedWorkDays = timesheet.workDays.filter(
      (day) => day.departureTime !== null && day.departureTime !== undefined,
    );

    // Calcul des totaux seulement sur les journées complètes
    const totalRegularHours = completedWorkDays.reduce(
      (sum, day) => sum + day.regularHours,
      0,
    );
    const totalOvertimeHours = completedWorkDays.reduce(
      (sum, day) => sum + day.overtimeHours,
      0,
    );
    const totalUndertimeHours = completedWorkDays.reduce(
      (sum, day) => sum + day.undertimeHours,
      0,
    );

    return {
      ...timesheet,
      workDays: completedWorkDays, // Optionnel: retourner seulement les journées complètes
      totalRegularHours,
      totalOvertimeHours,
      totalUndertimeHours,
      // Informations supplémentaires utiles
      totalCompletedDays: completedWorkDays.length,
      totalWorkDays: timesheet.workDays.length,
      incompleteDays: timesheet.workDays.length - completedWorkDays.length,
    };
  }

  async update(id: string, updateTimesheetDto: UpdateTimesheetDto) {
    const timesheet = await this.findOne(id);

    if (!timesheet) {
      throw new NotFoundException(`Timesheet with ID ${id} not found`);
    }

    const { periodStart, periodEnd, workDays } = updateTimesheetDto;

    const updatedTimesheet = await this.prisma.timesheet.update({
      where: { id },
      data: {
        periodStart: periodStart ?? timesheet.periodStart,
        periodEnd: periodEnd ?? timesheet.periodEnd,
      },
    });

    if (workDays) {
      await this.createWorkDays(timesheet.id, workDays);
    }

    return await this.findOne(updatedTimesheet.id);
  }

  async updateWorkDays(workDays: UpdateWorkDayDto[]) {
    for (const workDay of workDays) {
      const { workDayId, date, arrivalTime, departureTime } = workDay;
      const existingWorkDay = await this.prisma.workDay.findUnique({
        where: { id: workDayId },
      });

      if (!existingWorkDay) {
        throw new NotFoundException(`WorkDay with ID ${workDayId} not found`);
      }

      const { regularHours, overtimeHours, undertimeHours } =
        this.calculateHours(
          arrivalTime ?? existingWorkDay.arrivalTime,
          departureTime ?? existingWorkDay.departureTime,
        );

      await this.prisma.workDay.update({
        where: { id: workDayId },
        data: {
          date: date ?? existingWorkDay.date,
          arrivalTime: arrivalTime ?? existingWorkDay.arrivalTime,
          departureTime: departureTime ?? existingWorkDay.departureTime,
          regularHours,
          overtimeHours,
          undertimeHours,
        },
      });
    }
  }

  async remove(id: string) {
    const timesheet = await this.findOne(id);

    if (!timesheet) {
      throw new NotFoundException(`Timesheet with ID ${id} not found`);
    }

    await this.prisma.workDay.deleteMany({ where: { timesheetId: id } });

    return this.prisma.timesheet.delete({
      where: { id },
    });
  }

  calculateHours(arrivalTime: Date, departureTime: Date) {
    const totalHoursWorked =
      (new Date(departureTime).getTime() - new Date(arrivalTime).getTime()) /
      (1000 * 60 * 60); // Convert milliseconds to hours

    const standardHoursPerDay = 8; // This can be adjusted as needed

    // Arrondir le total des heures travaillées
    const roundedTotalHoursWorked = Math.round(totalHoursWorked * 10) / 10; // Arrondi à une décimale

    const regularHours = Math.min(roundedTotalHoursWorked, standardHoursPerDay);
    const overtimeHours = Math.max(
      roundedTotalHoursWorked - standardHoursPerDay,
      0,
    );
    const undertimeHours = Math.max(
      standardHoursPerDay - roundedTotalHoursWorked,
      0,
    );

    return {
      regularHours,
      overtimeHours,
      undertimeHours,
    };
  }

  async findAllForMonth(employeeId: string, month: number, year: number) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);

    return this.prisma.timesheet.findMany({
      where: {
        employeeId,
        periodStart: {
          gte: startDate,
        },
        periodEnd: {
          lte: endDate,
        },
      },
      include: {
        workDays: true,
      },
    });
  }
}
