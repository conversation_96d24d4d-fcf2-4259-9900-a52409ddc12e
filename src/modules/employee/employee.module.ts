import { <PERSON>du<PERSON> } from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { Employee<PERSON>ontroller } from './employee.controller';
import { TimesheetController } from './timesheet/timesheet.controller';
import { TimesheetService } from './timesheet/timesheet.service';
import { <PERSON><PERSON><PERSON><PERSON>roller } from './salary/salary.controller';
import { SalaryService } from './salary/salary.service';
import { PayrollCalculator } from './salary/utils/payroll-calculator';
import { LeaveController } from './leave/leave.controller';
import { LeaveService } from './leave/leave.service';

@Module({
  controllers: [
    EmployeeController,
    TimesheetController,
    SalaryController,
    LeaveController,
  ],
  providers: [
    EmployeeService,
    TimesheetService,
    PayrollCalculator,
    SalaryService,
    LeaveService,
  ],
})
export class EmployeeModule {}
