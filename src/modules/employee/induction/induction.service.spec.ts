import { Test, TestingModule } from '@nestjs/testing';
import { InductionService } from './induction.service';

describe('InductionService', () => {
  let service: InductionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [InductionService],
    }).compile();

    service = module.get<InductionService>(InductionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
