// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import { CreateInductionDto, UpdateInductionDto } from '../dto/induction.dto';

// @Injectable()
// export class InductionService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createInductionDto: CreateInductionDto) {
//     const { startDate, endDate, trainer, feedback, employeeId } =
//       createInductionDto;

//     return this.prisma.induction.create({
//       data: {
//         startDate,
//         endDate,
//         trainer,
//         feedback,
//         employee: { connect: { employeeId } },
//       },
//     });
//   }

//   async findAll() {
//     return this.prisma.induction.findMany();
//   }

//   async findOne(id: number) {
//     const induction = await this.prisma.induction.findUnique({
//       where: { inductionId: id },
//     });

//     if (!induction) {
//       throw new NotFoundException(`Induction with ID ${id} not found`);
//     }

//     return induction;
//   }

//   async update(id: number, updateInductionDto: UpdateInductionDto) {
//     const induction = await this.findOne(id);

//     if (!induction) {
//       throw new NotFoundException(`Induction with ID ${id} not found`);
//     }

//     const { startDate, endDate, trainer, feedback, employeeId } =
//       updateInductionDto;

//     return this.prisma.induction.update({
//       where: { inductionId: id },
//       data: {
//         startDate: startDate ?? induction.startDate,
//         endDate: endDate ?? induction.endDate,
//         trainer: trainer ?? induction.trainer,
//         feedback: feedback ?? induction.feedback,
//         employee: employeeId ? { connect: { employeeId } } : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const induction = await this.findOne(id);

//     if (!induction) {
//       throw new NotFoundException(`Induction with ID ${id} not found`);
//     }

//     return this.prisma.induction.delete({
//       where: { inductionId: id },
//     });
//   }
// }
