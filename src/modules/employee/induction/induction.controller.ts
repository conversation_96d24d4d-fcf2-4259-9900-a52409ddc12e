// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { InductionService } from './induction.service';
// import { CreateInductionDto, UpdateInductionDto } from '../dto/induction.dto';

// @ApiTags('inductions')
// @Controller('api/v1/company/inductions')
// export class InductionController {
//   constructor(private readonly inductionService: InductionService) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new induction' })
//   @ApiResponse({
//     status: 201,
//     description: 'The induction has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createInductionDto: CreateInductionDto) {
//     return this.inductionService.create(createInductionDto);
//   }

//   @Get()
//   @ApiOperation({ summary: 'Get all inductions' })
//   @ApiResponse({ status: 200, description: 'Return all inductions.' })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll() {
//     return this.inductionService.findAll();
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get an induction by ID' })
//   @ApiResponse({ status: 200, description: 'Return the induction.' })
//   @ApiResponse({ status: 404, description: 'Induction not found.' })
//   findOne(@Param('id') id: number) {
//     return this.inductionService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update an induction by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The induction has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Induction not found.' })
//   update(
//     @Param('id') id: number,
//     @Body() updateInductionDto: UpdateInductionDto,
//   ) {
//     return this.inductionService.update(id, updateInductionDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete an induction by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The induction has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Induction not found.' })
//   remove(@Param('id') id: number) {
//     return this.inductionService.remove(id);
//   }
// }
