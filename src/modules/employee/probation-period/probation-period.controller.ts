// import {
//   Controller,
//   Post,
//   Body,
//   Get,
//   Param,
//   Patch,
//   Delete,
// } from '@nestjs/common';
// import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { ProbationPeriodService } from './probation-period.service';
// import {
//   CreateProbationPeriodDto,
//   UpdateProbationPeriodDto,
// } from '../dto/probation-period.dto';

// @ApiTags('probation-periods')
// @Controller('api/v1/company/probation-periods')
// export class ProbationPeriodController {
//   constructor(
//     private readonly probationPeriodService: ProbationPeriodService,
//   ) {}

//   @Post()
//   @ApiOperation({ summary: 'Create a new probation period' })
//   @ApiResponse({
//     status: 201,
//     description: 'The probation period has been successfully created.',
//   })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   create(@Body() createProbationPeriodDto: CreateProbationPeriodDto) {
//     return this.probationPeriodService.create(createProbationPeriodDto);
//   }

//   @Get()
//   @ApiOperation({ summary: 'Get all probation periods' })
//   @ApiResponse({ status: 200, description: 'Return all probation periods.' })
//   @ApiResponse({ status: 400, description: 'Bad Request.' })
//   findAll() {
//     return this.probationPeriodService.findAll();
//   }

//   @Get(':id')
//   @ApiOperation({ summary: 'Get a probation period by ID' })
//   @ApiResponse({ status: 200, description: 'Return the probation period.' })
//   @ApiResponse({ status: 404, description: 'Probation period not found.' })
//   findOne(@Param('id') id: number) {
//     return this.probationPeriodService.findOne(id);
//   }

//   @Patch(':id')
//   @ApiOperation({ summary: 'Update a probation period by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The probation period has been successfully updated.',
//   })
//   @ApiResponse({ status: 404, description: 'Probation period not found.' })
//   update(
//     @Param('id') id: number,
//     @Body() updateProbationPeriodDto: UpdateProbationPeriodDto,
//   ) {
//     return this.probationPeriodService.update(id, updateProbationPeriodDto);
//   }

//   @Delete(':id')
//   @ApiOperation({ summary: 'Delete a probation period by ID' })
//   @ApiResponse({
//     status: 200,
//     description: 'The probation period has been successfully deleted.',
//   })
//   @ApiResponse({ status: 404, description: 'Probation period not found.' })
//   remove(@Param('id') id: number) {
//     return this.probationPeriodService.remove(id);
//   }
// }
