// import { Injectable, NotFoundException } from '@nestjs/common';
// import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
// import {
//   CreateProbationPeriodDto,
//   UpdateProbationPeriodDto,
// } from '../dto/probation-period.dto';

// @Injectable()
// export class ProbationPeriodService {
//   constructor(private readonly prisma: PrismaService) {}

//   async create(createProbationPeriodDto: CreateProbationPeriodDto) {
//     const { startDate, endDate, status, employeeId } = createProbationPeriodDto;

//     return this.prisma.probationPeriod.create({
//       data: {
//         startDate,
//         endDate,
//         status,
//         employee: { connect: { employeeId } },
//       },
//     });
//   }

//   async findAll() {
//     return this.prisma.probationPeriod.findMany();
//   }

//   async findOne(id: number) {
//     const probationPeriod = await this.prisma.probationPeriod.findUnique({
//       where: { probationPeriodId: id },
//     });

//     if (!probationPeriod) {
//       throw new NotFoundException(`ProbationPeriod with ID ${id} not found`);
//     }

//     return probationPeriod;
//   }

//   async update(id: number, updateProbationPeriodDto: UpdateProbationPeriodDto) {
//     const probationPeriod = await this.findOne(id);

//     if (!probationPeriod) {
//       throw new NotFoundException(`ProbationPeriod with ID ${id} not found`);
//     }

//     const { startDate, endDate, status, employeeId } = updateProbationPeriodDto;

//     return this.prisma.probationPeriod.update({
//       where: { probationPeriodId: id },
//       data: {
//         startDate: startDate ?? probationPeriod.startDate,
//         endDate: endDate ?? probationPeriod.endDate,
//         status: status ?? probationPeriod.status,
//         employee: employeeId ? { connect: { employeeId } } : undefined,
//       },
//     });
//   }

//   async remove(id: number) {
//     const probationPeriod = await this.findOne(id);

//     if (!probationPeriod) {
//       throw new NotFoundException(`ProbationPeriod with ID ${id} not found`);
//     }

//     return this.prisma.probationPeriod.delete({
//       where: { probationPeriodId: id },
//     });
//   }
// }
