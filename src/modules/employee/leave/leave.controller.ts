import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { LeaveService } from './leave.service';
import { CreateLeaveDto, UpdateLeaveDto } from '../dto/leave.dto';

@ApiTags('leaves')
@Controller('api/v1/companies/:companyId/leaves')
export class LeaveController {
  constructor(private readonly leaveService: LeaveService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new leave request' })
  @ApiResponse({
    status: 201,
    description: 'The leave request has been successfully created.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  create(@Body() createLeaveDto: CreateLeaveDto) {
    return this.leaveService.create(createLeaveDto);
  }

  @Get('')
  @ApiOperation({ summary: 'Get all leave requests' })
  @ApiResponse({ status: 200, description: 'Return all leave requests.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  findAll(@Param('companyId') companyId: string) {
    return this.leaveService.findAll(companyId);
  }

  @Get('employee/:employeeId')
  @ApiOperation({ summary: 'Get all leave requests for un employee' })
  @ApiResponse({ status: 200, description: 'Return all leave requests.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  findAllEmployeeLeaves(@Param('employeeId') employeeId: string) {
    return this.leaveService.findAllEmployeeLeaves(employeeId);
  }
  @Get(':id')
  @ApiOperation({ summary: 'Get a leave request by ID' })
  @ApiResponse({ status: 200, description: 'Return the leave request.' })
  @ApiResponse({ status: 404, description: 'Leave request not found.' })
  findOne(@Param('id') id: string) {
    return this.leaveService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a leave request by ID' })
  @ApiResponse({
    status: 200,
    description: 'The leave request has been successfully updated.',
  })
  @ApiResponse({ status: 404, description: 'Leave request not found.' })
  update(@Param('id') id: string, @Body() updateLeaveDto: UpdateLeaveDto) {
    return this.leaveService.update(id, updateLeaveDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a leave request by ID' })
  @ApiResponse({
    status: 200,
    description: 'The leave request has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'Leave request not found.' })
  remove(@Param('id') id: string) {
    return this.leaveService.remove(id);
  }
}
