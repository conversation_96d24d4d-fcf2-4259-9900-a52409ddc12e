import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';
import { CreateLeaveDto, UpdateLeaveDto } from '../dto/leave.dto';

@Injectable()
export class LeaveService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createLeaveDto: CreateLeaveDto) {
    const { leaveType, startDate, endDate, status, employeeId } =
      createLeaveDto;

    return this.prisma.leave.create({
      data: {
        leaveType,
        startDate,
        endDate,
        status,
        employee: { connect: { id: employeeId } },
      },
    });
  }

  async findAll(companyId: string) {
    return this.prisma.leave.findMany({
      where: {
        employee: {
          companyId,
        },
      },
      include: {
        employee: true,
      },
    });
  }

  async findAllEmployeeLeaves(employeeId: string) {
    return this.prisma.leave.findMany({
      where: {
        employee: {
          id: employeeId,
        },
      },
      include: {
        employee: {
          include: {
            user: {
              include: {
                profile: true,
              },
            },
          },
        },
      },
    });
  }

  async findOne(id: string) {
    const leave = await this.prisma.leave.findUnique({
      where: { id },
    });

    if (!leave) {
      throw new NotFoundException(`Leave with ID ${id} not found`);
    }

    return leave;
  }

  async update(id: string, updateLeaveDto: UpdateLeaveDto) {
    const leave = await this.findOne(id);

    if (!leave) {
      throw new NotFoundException(`Leave with ID ${id} not found`);
    }

    const { leaveType, startDate, endDate, status, employeeId } =
      updateLeaveDto;

    return this.prisma.leave.update({
      where: { id },
      data: {
        leaveType: leaveType ?? leave.leaveType,
        startDate: startDate ?? leave.startDate,
        endDate: endDate ?? leave.endDate,
        status: status ?? leave.status,
        employee: employeeId ? { connect: { id: employeeId } } : undefined,
      },
    });
  }

  async remove(id: string) {
    const leave = await this.findOne(id);

    if (!leave) {
      throw new NotFoundException(`Leave with ID ${id} not found`);
    }

    return this.prisma.leave.delete({
      where: { id },
    });
  }
}
