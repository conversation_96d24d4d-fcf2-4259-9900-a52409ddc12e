import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUUID,
  IsDateString,
  IsArray,
  IsNumber,
} from 'class-validator';

export class AnalyticsQueryDto {
  @ApiPropertyOptional({ description: 'Date de début' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: 'Date de fin' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: 'ID de l\'entreprise' })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiPropertyOptional({ description: 'ID du département' })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiPropertyOptional({ description: 'Période d\'agrégation', default: 'month' })
  @IsOptional()
  @IsString()
  period?: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

export class EmployeeAnalyticsDto {
  @ApiProperty()
  totalEmployees: number;

  @ApiProperty()
  activeEmployees: number;

  @ApiProperty()
  newHires: number;

  @ApiProperty()
  departures: number;

  @ApiProperty()
  turnoverRate: number;

  @ApiProperty()
  averageAge: number;

  @ApiProperty()
  genderDistribution: {
    male: number;
    female: number;
    other: number;
  };

  @ApiProperty()
  departmentDistribution: Array<{
    departmentName: string;
    count: number;
    percentage: number;
  }>;

  @ApiProperty()
  positionDistribution: Array<{
    positionTitle: string;
    count: number;
    percentage: number;
  }>;

  @ApiProperty()
  hiringTrends: Array<{
    period: string;
    hires: number;
    departures: number;
  }>;
}

export class PayrollAnalyticsDto {
  @ApiProperty()
  totalPayroll: number;

  @ApiProperty()
  averageSalary: number;

  @ApiProperty()
  medianSalary: number;

  @ApiProperty()
  highestSalary: number;

  @ApiProperty()
  lowestSalary: number;

  @ApiProperty()
  salaryDistribution: Array<{
    range: string;
    count: number;
    percentage: number;
  }>;

  @ApiProperty()
  departmentPayroll: Array<{
    departmentName: string;
    totalPayroll: number;
    averageSalary: number;
    employeeCount: number;
  }>;

  @ApiProperty()
  payrollTrends: Array<{
    period: string;
    totalPayroll: number;
    averageSalary: number;
  }>;

  @ApiProperty()
  benefitsCosts: {
    healthInsurance: number;
    retirement: number;
    other: number;
    total: number;
  };
}

export class LeaveAnalyticsDto {
  @ApiProperty()
  totalLeaveRequests: number;

  @ApiProperty()
  approvedLeaves: number;

  @ApiProperty()
  pendingLeaves: number;

  @ApiProperty()
  rejectedLeaves: number;

  @ApiProperty()
  averageLeaveDays: number;

  @ApiProperty()
  leaveTypeDistribution: Array<{
    leaveType: string;
    count: number;
    totalDays: number;
  }>;

  @ApiProperty()
  departmentLeaveStats: Array<{
    departmentName: string;
    totalRequests: number;
    averageDays: number;
    approvalRate: number;
  }>;

  @ApiProperty()
  monthlyLeavePattern: Array<{
    month: string;
    requests: number;
    totalDays: number;
  }>;
}

export class PerformanceAnalyticsDto {
  @ApiProperty()
  averagePerformanceScore: number;

  @ApiProperty()
  performanceDistribution: Array<{
    scoreRange: string;
    count: number;
    percentage: number;
  }>;

  @ApiProperty()
  departmentPerformance: Array<{
    departmentName: string;
    averageScore: number;
    employeeCount: number;
  }>;

  @ApiProperty()
  topPerformers: Array<{
    employeeName: string;
    department: string;
    score: number;
  }>;

  @ApiProperty()
  improvementNeeded: Array<{
    employeeName: string;
    department: string;
    score: number;
  }>;
}

export class RecruitmentAnalyticsDto {
  @ApiProperty()
  totalJobOffers: number;

  @ApiProperty()
  activeJobOffers: number;

  @ApiProperty()
  totalApplications: number;

  @ApiProperty()
  averageApplicationsPerJob: number;

  @ApiProperty()
  averageTimeToHire: number; // en jours

  @ApiProperty()
  applicationStatusDistribution: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;

  @ApiProperty()
  sourceEffectiveness: Array<{
    source: string;
    applications: number;
    hires: number;
    conversionRate: number;
  }>;

  @ApiProperty()
  departmentRecruitment: Array<{
    departmentName: string;
    openPositions: number;
    applications: number;
    hires: number;
  }>;
}

export class ComprehensiveAnalyticsDto {
  @ApiProperty()
  employee: EmployeeAnalyticsDto;

  @ApiProperty()
  payroll: PayrollAnalyticsDto;

  @ApiProperty()
  leave: LeaveAnalyticsDto;

  @ApiProperty()
  performance: PerformanceAnalyticsDto;

  @ApiProperty()
  recruitment: RecruitmentAnalyticsDto;

  @ApiProperty()
  generatedAt: Date;

  @ApiProperty()
  period: {
    startDate: Date;
    endDate: Date;
  };
}
