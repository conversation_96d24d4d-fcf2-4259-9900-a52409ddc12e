import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsUUID,
  IsObject,
  IsNumber,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ReportTypeEnum, ReportScheduleEnum } from '@prisma/client';

export class CreateReportDto {
  @ApiProperty({ description: 'Nom du rapport' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Description du rapport' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    enum: ReportTypeEnum,
    description: 'Type de rapport'
  })
  @IsEnum(ReportTypeEnum)
  reportType: ReportTypeEnum;

  @ApiPropertyOptional({ description: 'Paramètres du rapport' })
  @IsOptional()
  @IsObject()
  parameters?: Record<string, any>;

  @ApiPropertyOptional({ 
    enum: ReportScheduleEnum,
    description: 'Planification automatique du rapport'
  })
  @IsOptional()
  @IsEnum(ReportScheduleEnum)
  schedule?: ReportScheduleEnum;

  @ApiProperty({ description: 'ID de l\'entreprise' })
  @IsUUID()
  companyId: string;

  @ApiProperty({ description: 'ID du créateur' })
  @IsUUID()
  createdById: string;

  @ApiPropertyOptional({ description: 'Rapport actif', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateReportDto extends PartialType(CreateReportDto) {}

export class ReportQueryDto {
  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({ enum: ReportTypeEnum })
  @IsOptional()
  @IsEnum(ReportTypeEnum)
  reportType?: ReportTypeEnum;

  @ApiPropertyOptional({ enum: ReportScheduleEnum })
  @IsOptional()
  @IsEnum(ReportScheduleEnum)
  schedule?: ReportScheduleEnum;

  @ApiPropertyOptional({ description: 'ID de l\'entreprise' })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiPropertyOptional({ description: 'ID du créateur' })
  @IsOptional()
  @IsUUID()
  createdById?: string;

  @ApiPropertyOptional({ description: 'Filtrer par rapports actifs' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: 'Recherche par nom' })
  @IsOptional()
  @IsString()
  search?: string;
}

export class ExecuteReportDto {
  @ApiPropertyOptional({ description: 'Paramètres d\'exécution du rapport' })
  @IsOptional()
  @IsObject()
  parameters?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Format de sortie', default: 'JSON' })
  @IsOptional()
  @IsString()
  format?: 'JSON' | 'CSV' | 'PDF' | 'XLSX';
}

export class ReportResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiProperty({ enum: ReportTypeEnum })
  reportType: ReportTypeEnum;

  @ApiProperty()
  parameters: Record<string, any>;

  @ApiPropertyOptional({ enum: ReportScheduleEnum })
  schedule?: ReportScheduleEnum;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  companyId: string;

  @ApiProperty()
  createdById: string;
}

export class ReportExecutionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  startedAt: Date;

  @ApiPropertyOptional()
  completedAt?: Date;

  @ApiPropertyOptional()
  fileUrl?: string;

  @ApiPropertyOptional()
  errorMessage?: string;

  @ApiProperty()
  parameters: Record<string, any>;

  @ApiProperty()
  reportId: string;

  @ApiProperty()
  executedById: string;
}

export class ReportStatsDto {
  @ApiProperty()
  total: number;

  @ApiProperty()
  active: number;

  @ApiProperty()
  scheduled: number;

  @ApiProperty()
  byType: Record<string, number>;

  @ApiProperty()
  recentExecutions: number;

  @ApiProperty()
  failedExecutions: number;
}

export class DashboardDataDto {
  @ApiProperty()
  totalEmployees: number;

  @ApiProperty()
  activeContracts: number;

  @ApiProperty()
  pendingLeaves: number;

  @ApiProperty()
  pendingApprovals: number;

  @ApiProperty()
  monthlyPayroll: number;

  @ApiProperty()
  recentHires: number;

  @ApiProperty()
  expiringContracts: number;

  @ApiProperty()
  departmentStats: Array<{
    departmentName: string;
    employeeCount: number;
    averageSalary: number;
  }>;

  @ApiProperty()
  monthlyTrends: Array<{
    month: string;
    hires: number;
    departures: number;
    payroll: number;
  }>;
}
