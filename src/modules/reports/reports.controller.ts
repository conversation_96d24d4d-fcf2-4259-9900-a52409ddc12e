import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { ReportsService } from './reports.service';
import { AnalyticsService } from './analytics.service';
import {
  CreateReportDto,
  UpdateReportDto,
  ReportQueryDto,
  ExecuteReportDto,
  ReportResponseDto,
  ReportStatsDto,
  DashboardDataDto,
} from './dto/report.dto';
import {
  AnalyticsQueryDto,
  EmployeeAnalyticsDto,
  PayrollAnalyticsDto,
  LeaveAnalyticsDto,
  PerformanceAnalyticsDto,
  RecruitmentAnalyticsDto,
  ComprehensiveAnalyticsDto,
} from './dto/analytics.dto';
import { JwtGuard } from '../shared/guards/jtw.guard';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Roles } from '../shared/decorators/roles.decorator';
import { RoleEnum } from '@prisma/client';

@ApiTags('Reports & Analytics')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('api/v1/reports')
export class ReportsController {
  constructor(
    private readonly reportsService: ReportsService,
    private readonly analyticsService: AnalyticsService,
  ) {}

  // ===== REPORTS =====

  @Post()
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Créer un nouveau rapport' })
  @ApiResponse({
    status: 201,
    description: 'Rapport créé avec succès',
    type: ReportResponseDto,
  })
  createReport(@Body() createReportDto: CreateReportDto) {
    return this.reportsService.create(createReportDto);
  }

  @Get()
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer tous les rapports' })
  @ApiResponse({
    status: 200,
    description: 'Liste des rapports récupérée avec succès',
  })
  findAllReports(@Query() query: ReportQueryDto) {
    return this.reportsService.findAll(query);
  }

  @Get('stats')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Statistiques des rapports' })
  @ApiResponse({
    status: 200,
    description: 'Statistiques récupérées avec succès',
    type: ReportStatsDto,
  })
  @ApiQuery({ name: 'companyId', required: false })
  getReportStats(@Query('companyId') companyId?: string) {
    return this.reportsService.getReportStats(companyId);
  }

  @Get('dashboard/:companyId')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Données du tableau de bord' })
  @ApiResponse({
    status: 200,
    description: 'Données du tableau de bord récupérées avec succès',
    type: DashboardDataDto,
  })
  getDashboardData(@Param('companyId') companyId: string) {
    return this.reportsService.getDashboardData(companyId);
  }

  @Post(':id/execute')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Exécuter un rapport' })
  @ApiResponse({
    status: 200,
    description: 'Rapport exécuté avec succès',
  })
  executeReport(
    @Param('id') id: string,
    @Body() executeDto: ExecuteReportDto,
    @Body('executedById') executedById: string,
  ) {
    return this.reportsService.executeReport(id, executeDto, executedById);
  }

  @Get(':id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer un rapport par ID' })
  @ApiResponse({
    status: 200,
    description: 'Rapport récupéré avec succès',
    type: ReportResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Rapport non trouvé' })
  findOneReport(@Param('id') id: string) {
    return this.reportsService.findOne(id);
  }

  @Patch(':id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Modifier un rapport' })
  @ApiResponse({
    status: 200,
    description: 'Rapport modifié avec succès',
    type: ReportResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Rapport non trouvé' })
  updateReport(@Param('id') id: string, @Body() updateReportDto: UpdateReportDto) {
    return this.reportsService.update(id, updateReportDto);
  }

  @Delete(':id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Supprimer un rapport' })
  @ApiResponse({ status: 200, description: 'Rapport supprimé avec succès' })
  @ApiResponse({ status: 404, description: 'Rapport non trouvé' })
  removeReport(@Param('id') id: string) {
    return this.reportsService.remove(id);
  }

  // ===== ANALYTICS =====

  @Get('analytics/employees')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Analytics des employés' })
  @ApiResponse({
    status: 200,
    description: 'Analytics des employés récupérées avec succès',
    type: EmployeeAnalyticsDto,
  })
  getEmployeeAnalytics(@Query() query: AnalyticsQueryDto) {
    return this.analyticsService.getEmployeeAnalytics(query);
  }

  @Get('analytics/payroll')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Analytics de la masse salariale' })
  @ApiResponse({
    status: 200,
    description: 'Analytics de la masse salariale récupérées avec succès',
    type: PayrollAnalyticsDto,
  })
  getPayrollAnalytics(@Query() query: AnalyticsQueryDto) {
    return this.analyticsService.getPayrollAnalytics(query);
  }

  @Get('analytics/leaves')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Analytics des congés' })
  @ApiResponse({
    status: 200,
    description: 'Analytics des congés récupérées avec succès',
    type: LeaveAnalyticsDto,
  })
  getLeaveAnalytics(@Query() query: AnalyticsQueryDto) {
    return this.analyticsService.getLeaveAnalytics(query);
  }

  @Get('analytics/performance')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Analytics de performance' })
  @ApiResponse({
    status: 200,
    description: 'Analytics de performance récupérées avec succès',
    type: PerformanceAnalyticsDto,
  })
  getPerformanceAnalytics(@Query() query: AnalyticsQueryDto) {
    return this.analyticsService.getPerformanceAnalytics(query);
  }

  @Get('analytics/recruitment')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Analytics du recrutement' })
  @ApiResponse({
    status: 200,
    description: 'Analytics du recrutement récupérées avec succès',
    type: RecruitmentAnalyticsDto,
  })
  getRecruitmentAnalytics(@Query() query: AnalyticsQueryDto) {
    return this.analyticsService.getRecruitmentAnalytics(query);
  }

  @Get('analytics/comprehensive')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Analytics complètes' })
  @ApiResponse({
    status: 200,
    description: 'Analytics complètes récupérées avec succès',
    type: ComprehensiveAnalyticsDto,
  })
  getComprehensiveAnalytics(@Query() query: AnalyticsQueryDto) {
    return this.analyticsService.getComprehensiveAnalytics(query);
  }

  // ===== HR REPORTS (Rapports RH prédéfinis) =====

  @Get('hr/employee-list')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Rapport liste des employés' })
  @ApiResponse({
    status: 200,
    description: 'Rapport généré avec succès',
  })
  @ApiQuery({ name: 'companyId', required: true })
  @ApiQuery({ name: 'departmentId', required: false })
  @ApiQuery({ name: 'format', required: false, enum: ['JSON', 'CSV', 'PDF', 'XLSX'] })
  getEmployeeListReport(
    @Query('companyId') companyId: string,
    @Query('departmentId') departmentId?: string,
    @Query('format') format: string = 'JSON',
  ) {
    const executeDto: ExecuteReportDto = {
      parameters: { companyId, departmentId },
      format: format as any,
    };
    
    // Créer un rapport temporaire et l'exécuter
    return this.reportsService.executeReport('temp-employee-list', executeDto, 'system');
  }

  @Get('hr/payroll-summary')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Rapport résumé de paie' })
  @ApiResponse({
    status: 200,
    description: 'Rapport généré avec succès',
  })
  @ApiQuery({ name: 'companyId', required: true })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'format', required: false, enum: ['JSON', 'CSV', 'PDF', 'XLSX'] })
  getPayrollSummaryReport(
    @Query('companyId') companyId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('format') format: string = 'JSON',
  ) {
    const executeDto: ExecuteReportDto = {
      parameters: { companyId, startDate, endDate },
      format: format as any,
    };
    
    return this.reportsService.executeReport('temp-payroll-summary', executeDto, 'system');
  }

  @Get('hr/leave-report')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Rapport des congés' })
  @ApiResponse({
    status: 200,
    description: 'Rapport généré avec succès',
  })
  @ApiQuery({ name: 'companyId', required: true })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'format', required: false, enum: ['JSON', 'CSV', 'PDF', 'XLSX'] })
  getLeaveReport(
    @Query('companyId') companyId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('status') status?: string,
    @Query('format') format: string = 'JSON',
  ) {
    const executeDto: ExecuteReportDto = {
      parameters: { companyId, startDate, endDate, status },
      format: format as any,
    };
    
    return this.reportsService.executeReport('temp-leave-report', executeDto, 'system');
  }

  @Get('hr/attendance-report')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Rapport de présence' })
  @ApiResponse({
    status: 200,
    description: 'Rapport généré avec succès',
  })
  @ApiQuery({ name: 'companyId', required: true })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'employeeId', required: false })
  @ApiQuery({ name: 'format', required: false, enum: ['JSON', 'CSV', 'PDF', 'XLSX'] })
  getAttendanceReport(
    @Query('companyId') companyId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('employeeId') employeeId?: string,
    @Query('format') format: string = 'JSON',
  ) {
    const executeDto: ExecuteReportDto = {
      parameters: { companyId, startDate, endDate, employeeId },
      format: format as any,
    };
    
    return this.reportsService.executeReport('temp-attendance-report', executeDto, 'system');
  }
}
