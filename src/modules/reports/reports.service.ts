import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../shared/db/prisma/prisma.service';
import { 
  CreateReportDto, 
  UpdateReportDto, 
  ReportQueryDto,
  ExecuteReportDto,
  ReportStatsDto,
  DashboardDataDto
} from './dto/report.dto';
import { ReportTypeEnum, ReportExecutionStatusEnum, Prisma } from '@prisma/client';

@Injectable()
export class ReportsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createReportDto: CreateReportDto) {
    const { companyId, createdById, ...reportData } = createReportDto;

    // Vérifier que l'entreprise existe
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    // Vérifier que le créateur existe
    const creator = await this.prisma.employeeData.findUnique({
      where: { id: createdById },
    });

    if (!creator) {
      throw new NotFoundException(`Creator with ID ${createdById} not found`);
    }

    return this.prisma.report.create({
      data: {
        ...reportData,
        company: { connect: { id: companyId } },
        createdBy: { connect: { id: createdById } },
      },
      include: this.getReportInclude(),
    });
  }

  async findAll(query: ReportQueryDto) {
    const { page = 1, limit = 10, search, ...filters } = query;
    const skip = (page - 1) * limit;

    const where: Prisma.ReportWhereInput = {};

    if (filters.reportType) {
      where.reportType = filters.reportType;
    }

    if (filters.schedule) {
      where.schedule = filters.schedule;
    }

    if (filters.companyId) {
      where.companyId = filters.companyId;
    }

    if (filters.createdById) {
      where.createdById = filters.createdById;
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        where,
        skip,
        take: limit,
        include: this.getReportInclude(),
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.report.count({ where }),
    ]);

    return {
      data: reports,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string) {
    const report = await this.prisma.report.findUnique({
      where: { id },
      include: this.getReportInclude(),
    });

    if (!report) {
      throw new NotFoundException(`Report with ID ${id} not found`);
    }

    return report;
  }

  async update(id: string, updateReportDto: UpdateReportDto) {
    await this.findOne(id);

    return this.prisma.report.update({
      where: { id },
      data: updateReportDto,
      include: this.getReportInclude(),
    });
  }

  async remove(id: string) {
    await this.findOne(id);

    return this.prisma.report.delete({
      where: { id },
    });
  }

  async executeReport(id: string, executeDto: ExecuteReportDto, executedById: string) {
    const report = await this.findOne(id);

    if (!report.isActive) {
      throw new BadRequestException('Cannot execute inactive report');
    }

    // Créer une exécution de rapport
    const execution = await this.prisma.reportExecution.create({
      data: {
        status: ReportExecutionStatusEnum.RUNNING,
        parameters: executeDto.parameters || {},
        report: { connect: { id } },
        executedBy: { connect: { id: executedById } },
      },
    });

    try {
      // Exécuter le rapport selon son type
      const data = await this.generateReportData(report, executeDto.parameters || {});
      
      // Simuler la génération d'un fichier (en réalité, vous utiliseriez un service de génération de fichiers)
      const fileUrl = await this.generateReportFile(data, executeDto.format || 'JSON');

      // Marquer l'exécution comme terminée
      return await this.prisma.reportExecution.update({
        where: { id: execution.id },
        data: {
          status: ReportExecutionStatusEnum.COMPLETED,
          completedAt: new Date(),
          fileUrl,
        },
        include: {
          report: true,
          executedBy: {
            include: {
              user: {
                select: {
                  profile: {
                    select: {
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          },
        },
      });
    } catch (error) {
      // Marquer l'exécution comme échouée
      await this.prisma.reportExecution.update({
        where: { id: execution.id },
        data: {
          status: ReportExecutionStatusEnum.FAILED,
          completedAt: new Date(),
          errorMessage: error.message,
        },
      });

      throw error;
    }
  }

  async getReportStats(companyId?: string): Promise<ReportStatsDto> {
    const where: Prisma.ReportWhereInput = companyId ? { companyId } : {};

    const [total, active, scheduled, reports, recentExecutions, failedExecutions] = await Promise.all([
      this.prisma.report.count({ where }),
      this.prisma.report.count({ 
        where: { ...where, isActive: true } 
      }),
      this.prisma.report.count({ 
        where: { ...where, schedule: { not: null } } 
      }),
      this.prisma.report.findMany({
        where,
        select: { reportType: true },
      }),
      this.prisma.reportExecution.count({
        where: {
          startedAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 jours
          },
          report: companyId ? { companyId } : undefined,
        },
      }),
      this.prisma.reportExecution.count({
        where: {
          status: ReportExecutionStatusEnum.FAILED,
          report: companyId ? { companyId } : undefined,
        },
      }),
    ]);

    // Compter par type
    const byType = reports.reduce((acc, report) => {
      acc[report.reportType] = (acc[report.reportType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      active,
      scheduled,
      byType,
      recentExecutions,
      failedExecutions,
    };
  }

  async getDashboardData(companyId: string): Promise<DashboardDataDto> {
    const [
      totalEmployees,
      activeContracts,
      pendingLeaves,
      pendingApprovals,
      monthlyPayroll,
      recentHires,
      expiringContracts,
      departmentStats,
    ] = await Promise.all([
      this.prisma.employeeData.count({
        where: { companyId, isActive: true },
      }),
      this.prisma.contract.count({
        where: { companyId, status: 'ACTIVE' },
      }),
      this.prisma.leave.count({
        where: { 
          employee: { companyId },
          status: 'PENDING',
        },
      }),
      this.prisma.documentApproval.count({
        where: {
          status: 'PENDING',
          document: { companyId },
        },
      }),
      this.calculateMonthlyPayroll(companyId),
      this.getRecentHires(companyId),
      this.getExpiringContracts(companyId),
      this.getDepartmentStats(companyId),
    ]);

    const monthlyTrends = await this.getMonthlyTrends(companyId);

    return {
      totalEmployees,
      activeContracts,
      pendingLeaves,
      pendingApprovals,
      monthlyPayroll,
      recentHires,
      expiringContracts,
      departmentStats,
      monthlyTrends,
    };
  }

  private async generateReportData(report: any, parameters: Record<string, any>) {
    switch (report.reportType) {
      case ReportTypeEnum.EMPLOYEE_LIST:
        return this.generateEmployeeListReport(report.companyId, parameters);
      case ReportTypeEnum.PAYROLL_SUMMARY:
        return this.generatePayrollSummaryReport(report.companyId, parameters);
      case ReportTypeEnum.LEAVE_REPORT:
        return this.generateLeaveReport(report.companyId, parameters);
      case ReportTypeEnum.ATTENDANCE_REPORT:
        return this.generateAttendanceReport(report.companyId, parameters);
      case ReportTypeEnum.PERFORMANCE_REPORT:
        return this.generatePerformanceReport(report.companyId, parameters);
      case ReportTypeEnum.RECRUITMENT_REPORT:
        return this.generateRecruitmentReport(report.companyId, parameters);
      default:
        throw new BadRequestException(`Unsupported report type: ${report.reportType}`);
    }
  }

  private async generateReportFile(data: any, format: string): Promise<string> {
    // Simuler la génération d'un fichier
    // En réalité, vous utiliseriez des bibliothèques comme xlsx, pdf-lib, etc.
    const timestamp = Date.now();
    const filename = `report_${timestamp}.${format.toLowerCase()}`;
    
    // Simuler l'upload vers un service de stockage
    return `/reports/${filename}`;
  }

  private async generateEmployeeListReport(companyId: string, parameters: any) {
    return this.prisma.employeeData.findMany({
      where: { companyId },
      include: {
        user: {
          select: {
            email: true,
            profile: true,
          },
        },
        department: true,
        position: true,
      },
    });
  }

  private async generatePayrollSummaryReport(companyId: string, parameters: any) {
    return this.prisma.salary.findMany({
      where: { 
        employee: { companyId },
        ...(parameters.startDate && { createdAt: { gte: new Date(parameters.startDate) } }),
        ...(parameters.endDate && { createdAt: { lte: new Date(parameters.endDate) } }),
      },
      include: {
        employee: {
          include: {
            user: {
              select: {
                profile: true,
              },
            },
            department: true,
          },
        },
      },
    });
  }

  private async generateLeaveReport(companyId: string, parameters: any) {
    return this.prisma.leave.findMany({
      where: { 
        employee: { companyId },
        ...(parameters.startDate && { startDate: { gte: new Date(parameters.startDate) } }),
        ...(parameters.endDate && { endDate: { lte: new Date(parameters.endDate) } }),
      },
      include: {
        employee: {
          include: {
            user: {
              select: {
                profile: true,
              },
            },
            department: true,
          },
        },
      },
    });
  }

  private async generateAttendanceReport(companyId: string, parameters: any) {
    return this.prisma.timesheet.findMany({
      where: { 
        employee: { companyId },
        ...(parameters.startDate && { date: { gte: new Date(parameters.startDate) } }),
        ...(parameters.endDate && { date: { lte: new Date(parameters.endDate) } }),
      },
      include: {
        employee: {
          include: {
            user: {
              select: {
                profile: true,
              },
            },
            department: true,
          },
        },
      },
    });
  }

  private async generatePerformanceReport(companyId: string, parameters: any) {
    // Simuler des données de performance
    return [];
  }

  private async generateRecruitmentReport(companyId: string, parameters: any) {
    return this.prisma.jobOffer.findMany({
      where: { 
        companyId,
        ...(parameters.startDate && { createdAt: { gte: new Date(parameters.startDate) } }),
        ...(parameters.endDate && { createdAt: { lte: new Date(parameters.endDate) } }),
      },
      include: {
        applications: {
          include: {
            candidate: true,
          },
        },
        department: true,
      },
    });
  }

  private async calculateMonthlyPayroll(companyId: string): Promise<number> {
    const currentMonth = new Date();
    currentMonth.setDate(1);
    
    const result = await this.prisma.salary.aggregate({
      where: {
        employee: { companyId },
        createdAt: { gte: currentMonth },
      },
      _sum: { amount: true },
    });

    return result._sum.amount || 0;
  }

  private async getRecentHires(companyId: string): Promise<number> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return this.prisma.employeeData.count({
      where: {
        companyId,
        hireDate: { gte: thirtyDaysAgo },
      },
    });
  }

  private async getExpiringContracts(companyId: string): Promise<number> {
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    return this.prisma.contract.count({
      where: {
        companyId,
        status: 'ACTIVE',
        endDate: {
          lte: thirtyDaysFromNow,
          gte: new Date(),
        },
      },
    });
  }

  private async getDepartmentStats(companyId: string) {
    const departments = await this.prisma.department.findMany({
      where: { companyId },
      include: {
        employees: {
          include: {
            salaries: {
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        },
      },
    });

    return departments.map(dept => ({
      departmentName: dept.name,
      employeeCount: dept.employees.length,
      averageSalary: dept.employees.reduce((sum, emp) => {
        const latestSalary = emp.salaries[0]?.amount || 0;
        return sum + latestSalary;
      }, 0) / (dept.employees.length || 1),
    }));
  }

  private async getMonthlyTrends(companyId: string) {
    // Simuler des tendances mensuelles
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      months.push({
        month: date.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' }),
        hires: Math.floor(Math.random() * 10),
        departures: Math.floor(Math.random() * 5),
        payroll: Math.floor(Math.random() * 100000) + 50000,
      });
    }
    return months;
  }

  private getReportInclude(): Prisma.ReportInclude {
    return {
      company: {
        select: {
          id: true,
          companyName: true,
        },
      },
      createdBy: {
        include: {
          user: {
            select: {
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
      },
      executions: {
        orderBy: { startedAt: 'desc' },
        take: 5,
      },
    };
  }
}
