import { Injectable } from '@nestjs/common';
import { PrismaService } from '../shared/db/prisma/prisma.service';
import { 
  AnalyticsQueryDto,
  EmployeeAnalyticsDto,
  PayrollAnalyticsDto,
  LeaveAnalyticsDto,
  PerformanceAnalyticsDto,
  RecruitmentAnalyticsDto,
  ComprehensiveAnalyticsDto
} from './dto/analytics.dto';

@Injectable()
export class AnalyticsService {
  constructor(private readonly prisma: PrismaService) {}

  async getEmployeeAnalytics(query: AnalyticsQueryDto): Promise<EmployeeAnalyticsDto> {
    const { startDate, endDate, companyId, departmentId } = query;
    
    const whereClause: any = {};
    if (companyId) whereClause.companyId = companyId;
    if (departmentId) whereClause.departmentId = departmentId;

    const dateFilter: any = {};
    if (startDate) dateFilter.gte = new Date(startDate);
    if (endDate) dateFilter.lte = new Date(endDate);

    const [
      totalEmployees,
      activeEmployees,
      newHires,
      departures,
      employees,
      departments,
      positions
    ] = await Promise.all([
      this.prisma.employeeData.count({ where: whereClause }),
      this.prisma.employeeData.count({ 
        where: { ...whereClause, isActive: true } 
      }),
      this.prisma.employeeData.count({
        where: {
          ...whereClause,
          hireDate: dateFilter,
        },
      }),
      this.prisma.employeeData.count({
        where: {
          ...whereClause,
          terminationDate: dateFilter,
        },
      }),
      this.prisma.employeeData.findMany({
        where: whereClause,
        include: {
          user: { select: { profile: true } },
          department: true,
          position: true,
        },
      }),
      this.prisma.department.findMany({
        where: companyId ? { companyId } : {},
        include: { _count: { select: { employees: true } } },
      }),
      this.prisma.position.findMany({
        where: companyId ? { companyId } : {},
        include: { _count: { select: { employees: true } } },
      }),
    ]);

    // Calculer le taux de rotation
    const turnoverRate = totalEmployees > 0 ? (departures / totalEmployees) * 100 : 0;

    // Calculer l'âge moyen
    const averageAge = this.calculateAverageAge(employees);

    // Distribution par genre
    const genderDistribution = this.calculateGenderDistribution(employees);

    // Distribution par département
    const departmentDistribution = departments.map(dept => ({
      departmentName: dept.name,
      count: dept._count.employees,
      percentage: totalEmployees > 0 ? (dept._count.employees / totalEmployees) * 100 : 0,
    }));

    // Distribution par poste
    const positionDistribution = positions.map(pos => ({
      positionTitle: pos.title,
      count: pos._count.employees,
      percentage: totalEmployees > 0 ? (pos._count.employees / totalEmployees) * 100 : 0,
    }));

    // Tendances d'embauche (simulées pour l'exemple)
    const hiringTrends = await this.getHiringTrends(companyId, startDate, endDate);

    return {
      totalEmployees,
      activeEmployees,
      newHires,
      departures,
      turnoverRate,
      averageAge,
      genderDistribution,
      departmentDistribution,
      positionDistribution,
      hiringTrends,
    };
  }

  async getPayrollAnalytics(query: AnalyticsQueryDto): Promise<PayrollAnalyticsDto> {
    const { startDate, endDate, companyId, departmentId } = query;

    const whereClause: any = {};
    if (companyId) whereClause.employee = { companyId };
    if (departmentId) whereClause.employee = { ...whereClause.employee, departmentId };

    const dateFilter: any = {};
    if (startDate) dateFilter.gte = new Date(startDate);
    if (endDate) dateFilter.lte = new Date(endDate);
    if (Object.keys(dateFilter).length > 0) {
      whereClause.createdAt = dateFilter;
    }

    const salaries = await this.prisma.salary.findMany({
      where: whereClause,
      include: {
        employee: {
          include: {
            department: true,
          },
        },
      },
    });

    const amounts = salaries.map(s => s.amount);
    const totalPayroll = amounts.reduce((sum, amount) => sum + amount, 0);
    const averageSalary = amounts.length > 0 ? totalPayroll / amounts.length : 0;
    const medianSalary = this.calculateMedian(amounts);
    const highestSalary = Math.max(...amounts, 0);
    const lowestSalary = Math.min(...amounts, 0);

    // Distribution des salaires
    const salaryDistribution = this.calculateSalaryDistribution(amounts);

    // Masse salariale par département
    const departmentPayroll = this.calculateDepartmentPayroll(salaries);

    // Tendances de la masse salariale
    const payrollTrends = await this.getPayrollTrends(companyId, startDate, endDate);

    // Coûts des avantages (simulés)
    const benefitsCosts = {
      healthInsurance: totalPayroll * 0.05,
      retirement: totalPayroll * 0.03,
      other: totalPayroll * 0.02,
      total: totalPayroll * 0.10,
    };

    return {
      totalPayroll,
      averageSalary,
      medianSalary,
      highestSalary,
      lowestSalary,
      salaryDistribution,
      departmentPayroll,
      payrollTrends,
      benefitsCosts,
    };
  }

  async getLeaveAnalytics(query: AnalyticsQueryDto): Promise<LeaveAnalyticsDto> {
    const { startDate, endDate, companyId, departmentId } = query;

    const whereClause: any = {};
    if (companyId) whereClause.employee = { companyId };
    if (departmentId) whereClause.employee = { ...whereClause.employee, departmentId };

    const dateFilter: any = {};
    if (startDate) dateFilter.gte = new Date(startDate);
    if (endDate) dateFilter.lte = new Date(endDate);
    if (Object.keys(dateFilter).length > 0) {
      whereClause.startDate = dateFilter;
    }

    const leaves = await this.prisma.leave.findMany({
      where: whereClause,
      include: {
        employee: {
          include: {
            department: true,
          },
        },
      },
    });

    const totalLeaveRequests = leaves.length;
    const approvedLeaves = leaves.filter(l => l.status === 'APPROVED').length;
    const pendingLeaves = leaves.filter(l => l.status === 'PENDING').length;
    const rejectedLeaves = leaves.filter(l => l.status === 'REJECTED').length;

    const totalDays = leaves.reduce((sum, leave) => {
      const days = Math.ceil((leave.endDate.getTime() - leave.startDate.getTime()) / (1000 * 60 * 60 * 24));
      return sum + days;
    }, 0);

    const averageLeaveDays = totalLeaveRequests > 0 ? totalDays / totalLeaveRequests : 0;

    // Distribution par type de congé
    const leaveTypeDistribution = this.calculateLeaveTypeDistribution(leaves);

    // Statistiques par département
    const departmentLeaveStats = this.calculateDepartmentLeaveStats(leaves);

    // Modèle mensuel des congés
    const monthlyLeavePattern = this.calculateMonthlyLeavePattern(leaves);

    return {
      totalLeaveRequests,
      approvedLeaves,
      pendingLeaves,
      rejectedLeaves,
      averageLeaveDays,
      leaveTypeDistribution,
      departmentLeaveStats,
      monthlyLeavePattern,
    };
  }

  async getPerformanceAnalytics(query: AnalyticsQueryDto): Promise<PerformanceAnalyticsDto> {
    // Simuler des données de performance car le modèle n'existe pas encore
    return {
      averagePerformanceScore: 7.5,
      performanceDistribution: [
        { scoreRange: '9-10', count: 15, percentage: 15 },
        { scoreRange: '7-8', count: 50, percentage: 50 },
        { scoreRange: '5-6', count: 25, percentage: 25 },
        { scoreRange: '0-4', count: 10, percentage: 10 },
      ],
      departmentPerformance: [
        { departmentName: 'IT', averageScore: 8.2, employeeCount: 25 },
        { departmentName: 'HR', averageScore: 7.8, employeeCount: 10 },
        { departmentName: 'Finance', averageScore: 7.5, employeeCount: 15 },
      ],
      topPerformers: [
        { employeeName: 'Jean Dupont', department: 'IT', score: 9.5 },
        { employeeName: 'Marie Martin', department: 'Finance', score: 9.2 },
        { employeeName: 'Pierre Durand', department: 'HR', score: 9.0 },
      ],
      improvementNeeded: [
        { employeeName: 'Paul Bernard', department: 'IT', score: 4.5 },
        { employeeName: 'Sophie Moreau', department: 'Finance', score: 4.8 },
      ],
    };
  }

  async getRecruitmentAnalytics(query: AnalyticsQueryDto): Promise<RecruitmentAnalyticsDto> {
    const { startDate, endDate, companyId } = query;

    const whereClause: any = {};
    if (companyId) whereClause.companyId = companyId;

    const dateFilter: any = {};
    if (startDate) dateFilter.gte = new Date(startDate);
    if (endDate) dateFilter.lte = new Date(endDate);
    if (Object.keys(dateFilter).length > 0) {
      whereClause.createdAt = dateFilter;
    }

    const [jobOffers, applications] = await Promise.all([
      this.prisma.jobOffer.findMany({
        where: whereClause,
        include: {
          applications: true,
          department: true,
        },
      }),
      this.prisma.application.findMany({
        where: {
          jobOffer: whereClause,
        },
      }),
    ]);

    const totalJobOffers = jobOffers.length;
    const activeJobOffers = jobOffers.filter(job => job.status === 'ACTIVE').length;
    const totalApplications = applications.length;
    const averageApplicationsPerJob = totalJobOffers > 0 ? totalApplications / totalJobOffers : 0;

    // Temps moyen d'embauche (simulé)
    const averageTimeToHire = 21; // jours

    // Distribution des statuts des candidatures
    const applicationStatusDistribution = this.calculateApplicationStatusDistribution(applications);

    // Efficacité des sources (simulée)
    const sourceEffectiveness = [
      { source: 'Site web', applications: 45, hires: 8, conversionRate: 17.8 },
      { source: 'LinkedIn', applications: 32, hires: 12, conversionRate: 37.5 },
      { source: 'Référence', applications: 18, hires: 9, conversionRate: 50.0 },
      { source: 'Autres', applications: 15, hires: 3, conversionRate: 20.0 },
    ];

    // Recrutement par département
    const departmentRecruitment = jobOffers.map(job => ({
      departmentName: job.department?.name || 'Non spécifié',
      openPositions: 1,
      applications: job.applications.length,
      hires: job.applications.filter(app => app.status === 'HIRED').length,
    }));

    return {
      totalJobOffers,
      activeJobOffers,
      totalApplications,
      averageApplicationsPerJob,
      averageTimeToHire,
      applicationStatusDistribution,
      sourceEffectiveness,
      departmentRecruitment,
    };
  }

  async getComprehensiveAnalytics(query: AnalyticsQueryDto): Promise<ComprehensiveAnalyticsDto> {
    const [employee, payroll, leave, performance, recruitment] = await Promise.all([
      this.getEmployeeAnalytics(query),
      this.getPayrollAnalytics(query),
      this.getLeaveAnalytics(query),
      this.getPerformanceAnalytics(query),
      this.getRecruitmentAnalytics(query),
    ]);

    return {
      employee,
      payroll,
      leave,
      performance,
      recruitment,
      generatedAt: new Date(),
      period: {
        startDate: query.startDate ? new Date(query.startDate) : new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
        endDate: query.endDate ? new Date(query.endDate) : new Date(),
      },
    };
  }

  // Méthodes utilitaires privées

  private calculateAverageAge(employees: any[]): number {
    const ages = employees
      .filter(emp => emp.user?.profile?.dateOfBirth)
      .map(emp => {
        const birthDate = new Date(emp.user.profile.dateOfBirth);
        const today = new Date();
        return today.getFullYear() - birthDate.getFullYear();
      });

    return ages.length > 0 ? ages.reduce((sum, age) => sum + age, 0) / ages.length : 0;
  }

  private calculateGenderDistribution(employees: any[]) {
    const distribution = { male: 0, female: 0, other: 0 };
    
    employees.forEach(emp => {
      const gender = emp.user?.profile?.gender?.toLowerCase();
      if (gender === 'male' || gender === 'homme') {
        distribution.male++;
      } else if (gender === 'female' || gender === 'femme') {
        distribution.female++;
      } else {
        distribution.other++;
      }
    });

    return distribution;
  }

  private calculateMedian(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    
    const sorted = [...numbers].sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    
    return sorted.length % 2 === 0
      ? (sorted[middle - 1] + sorted[middle]) / 2
      : sorted[middle];
  }

  private calculateSalaryDistribution(amounts: number[]) {
    const ranges = [
      { range: '0-50k', min: 0, max: 50000 },
      { range: '50k-100k', min: 50000, max: 100000 },
      { range: '100k-200k', min: 100000, max: 200000 },
      { range: '200k+', min: 200000, max: Infinity },
    ];

    return ranges.map(range => {
      const count = amounts.filter(amount => amount >= range.min && amount < range.max).length;
      return {
        range: range.range,
        count,
        percentage: amounts.length > 0 ? (count / amounts.length) * 100 : 0,
      };
    });
  }

  private calculateDepartmentPayroll(salaries: any[]) {
    const departmentMap = new Map();

    salaries.forEach(salary => {
      const deptName = salary.employee.department?.name || 'Non spécifié';
      if (!departmentMap.has(deptName)) {
        departmentMap.set(deptName, { total: 0, count: 0 });
      }
      const dept = departmentMap.get(deptName);
      dept.total += salary.amount;
      dept.count += 1;
    });

    return Array.from(departmentMap.entries()).map(([name, data]) => ({
      departmentName: name,
      totalPayroll: data.total,
      averageSalary: data.count > 0 ? data.total / data.count : 0,
      employeeCount: data.count,
    }));
  }

  private async getHiringTrends(companyId?: string, startDate?: string, endDate?: string) {
    // Simuler des tendances d'embauche
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      months.push({
        period: date.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' }),
        hires: Math.floor(Math.random() * 10) + 1,
        departures: Math.floor(Math.random() * 5),
      });
    }
    return months;
  }

  private async getPayrollTrends(companyId?: string, startDate?: string, endDate?: string) {
    // Simuler des tendances de masse salariale
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      months.push({
        period: date.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' }),
        totalPayroll: Math.floor(Math.random() * 100000) + 200000,
        averageSalary: Math.floor(Math.random() * 20000) + 80000,
      });
    }
    return months;
  }

  private calculateLeaveTypeDistribution(leaves: any[]) {
    const typeMap = new Map();

    leaves.forEach(leave => {
      const type = leave.leaveType || 'Non spécifié';
      if (!typeMap.has(type)) {
        typeMap.set(type, { count: 0, totalDays: 0 });
      }
      const data = typeMap.get(type);
      data.count += 1;
      const days = Math.ceil((leave.endDate.getTime() - leave.startDate.getTime()) / (1000 * 60 * 60 * 24));
      data.totalDays += days;
    });

    return Array.from(typeMap.entries()).map(([type, data]) => ({
      leaveType: type,
      count: data.count,
      totalDays: data.totalDays,
    }));
  }

  private calculateDepartmentLeaveStats(leaves: any[]) {
    const deptMap = new Map();

    leaves.forEach(leave => {
      const deptName = leave.employee.department?.name || 'Non spécifié';
      if (!deptMap.has(deptName)) {
        deptMap.set(deptName, { requests: 0, totalDays: 0, approved: 0 });
      }
      const dept = deptMap.get(deptName);
      dept.requests += 1;
      const days = Math.ceil((leave.endDate.getTime() - leave.startDate.getTime()) / (1000 * 60 * 60 * 24));
      dept.totalDays += days;
      if (leave.status === 'APPROVED') dept.approved += 1;
    });

    return Array.from(deptMap.entries()).map(([name, data]) => ({
      departmentName: name,
      totalRequests: data.requests,
      averageDays: data.requests > 0 ? data.totalDays / data.requests : 0,
      approvalRate: data.requests > 0 ? (data.approved / data.requests) * 100 : 0,
    }));
  }

  private calculateMonthlyLeavePattern(leaves: any[]) {
    const monthMap = new Map();

    leaves.forEach(leave => {
      const month = leave.startDate.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' });
      if (!monthMap.has(month)) {
        monthMap.set(month, { requests: 0, totalDays: 0 });
      }
      const data = monthMap.get(month);
      data.requests += 1;
      const days = Math.ceil((leave.endDate.getTime() - leave.startDate.getTime()) / (1000 * 60 * 60 * 24));
      data.totalDays += days;
    });

    return Array.from(monthMap.entries()).map(([month, data]) => ({
      month,
      requests: data.requests,
      totalDays: data.totalDays,
    }));
  }

  private calculateApplicationStatusDistribution(applications: any[]) {
    const statusMap = new Map();

    applications.forEach(app => {
      const status = app.status || 'Non spécifié';
      statusMap.set(status, (statusMap.get(status) || 0) + 1);
    });

    const total = applications.length;
    return Array.from(statusMap.entries()).map(([status, count]) => ({
      status,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0,
    }));
  }
}
