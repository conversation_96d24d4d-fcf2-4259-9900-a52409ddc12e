import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsUUID,
} from 'class-validator';
import { ContractTypeEnum } from '@prisma/client';

export class CreateContractTemplateDto {
  @ApiProperty({ description: 'Nom du template' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Description du template' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Contenu du template avec placeholders' })
  @IsString()
  content: string;

  @ApiProperty({ 
    enum: ContractTypeEnum,
    description: 'Type de contrat pour ce template'
  })
  @IsEnum(ContractTypeEnum)
  contractType: ContractTypeEnum;

  @ApiProperty({ description: 'ID de l\'entreprise' })
  @IsUUID()
  companyId: string;

  @ApiPropertyOptional({ description: 'Template actif', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateContractTemplateDto extends PartialType(CreateContractTemplateDto) {}

export class ContractTemplateResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiProperty()
  content: string;

  @ApiProperty({ enum: ContractTypeEnum })
  contractType: ContractTypeEnum;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  companyId: string;
}

export class ContractTemplateQueryDto {
  @ApiPropertyOptional({ enum: ContractTypeEnum })
  @IsOptional()
  @IsEnum(ContractTypeEnum)
  contractType?: ContractTypeEnum;

  @ApiPropertyOptional({ description: 'Filtrer par templates actifs' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: 'ID de l\'entreprise' })
  @IsOptional()
  @IsUUID()
  companyId?: string;
}

export class GenerateContractFromTemplateDto {
  @ApiProperty({ description: 'ID du template' })
  @IsUUID()
  templateId: string;

  @ApiProperty({ description: 'ID de l\'employé' })
  @IsUUID()
  employeeId: string;

  @ApiProperty({ description: 'Variables pour remplacer les placeholders' })
  variables: Record<string, any>;
}
