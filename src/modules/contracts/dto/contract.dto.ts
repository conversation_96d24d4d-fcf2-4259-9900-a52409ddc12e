import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsDateString,
  IsNumber,
  IsArray,
  IsUUID,
  Min,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ContractTypeEnum, ContractStatusEnum } from '@prisma/client';

export class CreateContractDto {
  @ApiProperty({ description: 'Titre du contrat' })
  @IsString()
  title: string;

  @ApiProperty({ 
    enum: ContractTypeEnum,
    description: 'Type de contrat'
  })
  @IsEnum(ContractTypeEnum)
  contractType: ContractTypeEnum;

  @ApiProperty({ description: 'Date de début du contrat' })
  @IsDateString()
  startDate: string;

  @ApiPropertyOptional({ description: 'Date de fin du contrat' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Salaire en CDF' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  salary?: number;

  @ApiPropertyOptional({ description: 'Devise', default: 'CDF' })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({ description: 'Heures de travail par semaine' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  workingHours?: number;

  @ApiPropertyOptional({ description: 'Période d\'essai en mois' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  probationPeriod?: number;

  @ApiPropertyOptional({ description: 'Période de préavis en jours' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  noticePeriod?: number;

  @ApiPropertyOptional({ description: 'Conditions de renouvellement' })
  @IsOptional()
  @IsString()
  renewalTerms?: string;

  @ApiPropertyOptional({ description: 'Clause de résiliation' })
  @IsOptional()
  @IsString()
  terminationClause?: string;

  @ApiPropertyOptional({ description: 'Avantages sociaux', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  benefits?: string[];

  @ApiPropertyOptional({ description: 'Responsabilités', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  responsibilities?: string[];

  @ApiProperty({ description: 'ID de l\'employé' })
  @IsUUID()
  employeeId: string;

  @ApiProperty({ description: 'ID de l\'entreprise' })
  @IsUUID()
  companyId: string;

  @ApiPropertyOptional({ description: 'ID du template utilisé' })
  @IsOptional()
  @IsUUID()
  templateId?: string;
}

export class UpdateContractDto extends PartialType(CreateContractDto) {
  @ApiPropertyOptional({ 
    enum: ContractStatusEnum,
    description: 'Statut du contrat'
  })
  @IsOptional()
  @IsEnum(ContractStatusEnum)
  status?: ContractStatusEnum;

  @ApiPropertyOptional({ description: 'Date de signature' })
  @IsOptional()
  @IsDateString()
  signedAt?: string;
}

export class ContractQueryDto {
  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({ enum: ContractStatusEnum })
  @IsOptional()
  @IsEnum(ContractStatusEnum)
  status?: ContractStatusEnum;

  @ApiPropertyOptional({ enum: ContractTypeEnum })
  @IsOptional()
  @IsEnum(ContractTypeEnum)
  contractType?: ContractTypeEnum;

  @ApiPropertyOptional({ description: 'ID de l\'employé' })
  @IsOptional()
  @IsUUID()
  employeeId?: string;

  @ApiPropertyOptional({ description: 'ID de l\'entreprise' })
  @IsOptional()
  @IsUUID()
  companyId?: string;
}

export class SignContractDto {
  @ApiProperty({ description: 'Date de signature' })
  @IsDateString()
  signedAt: string;

  @ApiPropertyOptional({ description: 'Commentaires sur la signature' })
  @IsOptional()
  @IsString()
  comments?: string;
}

export class ContractResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  contractNumber: string;

  @ApiProperty()
  title: string;

  @ApiProperty({ enum: ContractTypeEnum })
  contractType: ContractTypeEnum;

  @ApiProperty({ enum: ContractStatusEnum })
  status: ContractStatusEnum;

  @ApiProperty()
  startDate: Date;

  @ApiPropertyOptional()
  endDate?: Date;

  @ApiPropertyOptional()
  salary?: number;

  @ApiProperty()
  currency: string;

  @ApiPropertyOptional()
  workingHours?: number;

  @ApiPropertyOptional()
  probationPeriod?: number;

  @ApiPropertyOptional()
  noticePeriod?: number;

  @ApiPropertyOptional()
  renewalTerms?: string;

  @ApiPropertyOptional()
  terminationClause?: string;

  @ApiProperty({ type: [String] })
  benefits: string[];

  @ApiProperty({ type: [String] })
  responsibilities: string[];

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiPropertyOptional()
  signedAt?: Date;

  @ApiProperty()
  employeeId: string;

  @ApiProperty()
  companyId: string;

  @ApiPropertyOptional()
  templateId?: string;
}

export class ContractStatsDto {
  @ApiProperty()
  total: number;

  @ApiProperty()
  active: number;

  @ApiProperty()
  expired: number;

  @ApiProperty()
  draft: number;

  @ApiProperty()
  pendingApproval: number;
}
