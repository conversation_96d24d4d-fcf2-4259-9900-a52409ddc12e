import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { ContractsService, ContractTemplatesService } from './contracts.service';
import {
  CreateContractDto,
  UpdateContractDto,
  ContractQueryDto,
  SignContractDto,
  ContractResponseDto,
  ContractStatsDto,
} from './dto/contract.dto';
import {
  CreateContractTemplateDto,
  UpdateContractTemplateDto,
  ContractTemplateResponseDto,
  ContractTemplateQueryDto,
  GenerateContractFromTemplateDto,
} from './dto/contract-template.dto';
import { JwtGuard } from '../shared/guards/jtw.guard';
import { RolesGuard } from '../shared/guards/roles.guard';
import { Roles } from '../shared/decorators/roles.decorator';
import { RoleEnum } from '@prisma/client';

@ApiTags('Contracts')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('api/v1/contracts')
export class ContractsController {
  constructor(
    private readonly contractsService: ContractsService,
    private readonly contractTemplatesService: ContractTemplatesService,
  ) {}

  @Post()
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Créer un nouveau contrat' })
  @ApiResponse({
    status: 201,
    description: 'Contrat créé avec succès',
    type: ContractResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  @ApiResponse({ status: 404, description: 'Employé ou entreprise non trouvé' })
  create(@Body() createContractDto: CreateContractDto) {
    return this.contractsService.create(createContractDto);
  }

  @Get()
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer tous les contrats' })
  @ApiResponse({
    status: 200,
    description: 'Liste des contrats récupérée avec succès',
  })
  findAll(@Query() query: ContractQueryDto) {
    return this.contractsService.findAll(query);
  }

  @Get('stats')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Statistiques des contrats' })
  @ApiResponse({
    status: 200,
    description: 'Statistiques récupérées avec succès',
    type: ContractStatsDto,
  })
  @ApiQuery({ name: 'companyId', required: false })
  getStats(@Query('companyId') companyId?: string) {
    return this.contractsService.getContractStats(companyId);
  }

  @Get('expiring')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Contrats arrivant à expiration' })
  @ApiResponse({
    status: 200,
    description: 'Contrats expirant bientôt',
  })
  @ApiQuery({ name: 'companyId', required: false })
  @ApiQuery({ name: 'daysAhead', required: false, type: Number })
  getExpiringContracts(
    @Query('companyId') companyId?: string,
    @Query('daysAhead') daysAhead?: number,
  ) {
    return this.contractsService.getExpiringContracts(companyId, daysAhead);
  }

  @Get('templates')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer tous les templates de contrats' })
  @ApiResponse({
    status: 200,
    description: 'Templates récupérés avec succès',
    type: [ContractTemplateResponseDto],
  })
  findAllTemplates(@Query() query: ContractTemplateQueryDto) {
    return this.contractTemplatesService.findAll(query);
  }

  @Post('templates')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Créer un nouveau template de contrat' })
  @ApiResponse({
    status: 201,
    description: 'Template créé avec succès',
    type: ContractTemplateResponseDto,
  })
  createTemplate(@Body() createTemplateDto: CreateContractTemplateDto) {
    return this.contractTemplatesService.create(createTemplateDto);
  }

  @Post('templates/:id/generate')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Générer un contrat à partir d\'un template' })
  @ApiResponse({
    status: 200,
    description: 'Contrat généré avec succès',
  })
  generateFromTemplate(
    @Param('id') templateId: string,
    @Body() generateDto: GenerateContractFromTemplateDto,
  ) {
    return this.contractTemplatesService.generateContract(
      templateId,
      generateDto.variables,
    );
  }

  @Get('templates/:id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER)
  @ApiOperation({ summary: 'Récupérer un template de contrat par ID' })
  @ApiResponse({
    status: 200,
    description: 'Template récupéré avec succès',
    type: ContractTemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Template non trouvé' })
  findOneTemplate(@Param('id') id: string) {
    return this.contractTemplatesService.findOne(id);
  }

  @Patch('templates/:id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Modifier un template de contrat' })
  @ApiResponse({
    status: 200,
    description: 'Template modifié avec succès',
    type: ContractTemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Template non trouvé' })
  updateTemplate(
    @Param('id') id: string,
    @Body() updateTemplateDto: UpdateContractTemplateDto,
  ) {
    return this.contractTemplatesService.update(id, updateTemplateDto);
  }

  @Delete('templates/:id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Supprimer un template de contrat' })
  @ApiResponse({ status: 200, description: 'Template supprimé avec succès' })
  @ApiResponse({ status: 404, description: 'Template non trouvé' })
  removeTemplate(@Param('id') id: string) {
    return this.contractTemplatesService.remove(id);
  }

  @Get(':id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.OWNER, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Récupérer un contrat par ID' })
  @ApiResponse({
    status: 200,
    description: 'Contrat récupéré avec succès',
    type: ContractResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Contrat non trouvé' })
  findOne(@Param('id') id: string) {
    return this.contractsService.findOne(id);
  }

  @Patch(':id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Modifier un contrat' })
  @ApiResponse({
    status: 200,
    description: 'Contrat modifié avec succès',
    type: ContractResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Contrat non trouvé' })
  @ApiResponse({ status: 400, description: 'Modification non autorisée' })
  update(@Param('id') id: string, @Body() updateContractDto: UpdateContractDto) {
    return this.contractsService.update(id, updateContractDto);
  }

  @Post(':id/sign')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN, RoleEnum.EMPLOYEE)
  @ApiOperation({ summary: 'Signer un contrat' })
  @ApiResponse({
    status: 200,
    description: 'Contrat signé avec succès',
    type: ContractResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Contrat non trouvé' })
  @ApiResponse({ status: 400, description: 'Contrat ne peut pas être signé' })
  signContract(@Param('id') id: string, @Body() signDto: SignContractDto) {
    return this.contractsService.signContract(id, signDto);
  }

  @Delete(':id')
  @Roles(RoleEnum.ADMIN_HR, RoleEnum.SUPER_ADMIN)
  @ApiOperation({ summary: 'Supprimer un contrat' })
  @ApiResponse({ status: 200, description: 'Contrat supprimé avec succès' })
  @ApiResponse({ status: 404, description: 'Contrat non trouvé' })
  @ApiResponse({ status: 400, description: 'Suppression non autorisée' })
  remove(@Param('id') id: string) {
    return this.contractsService.remove(id);
  }
}
