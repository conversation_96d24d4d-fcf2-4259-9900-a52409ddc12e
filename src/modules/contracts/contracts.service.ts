import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../shared/db/prisma/prisma.service';
import {
  CreateContractDto,
  UpdateContractDto,
  ContractQueryDto,
  SignContractDto,
  ContractStatsDto,
} from './dto/contract.dto';
import { ContractStatusEnum, Prisma } from '@prisma/client';

@Injectable()
export class ContractTemplatesService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createTemplateDto: any) {
    return this.prisma.contractTemplate.create({
      data: createTemplateDto,
    });
  }

  async findAll(query: any = {}) {
    const where: Prisma.ContractTemplateWhereInput = {};

    if (query.contractType) {
      where.contractType = query.contractType;
    }

    if (query.isActive !== undefined) {
      where.isActive = query.isActive;
    }

    if (query.companyId) {
      where.companyId = query.companyId;
    }

    return this.prisma.contractTemplate.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: string) {
    const template = await this.prisma.contractTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new NotFoundException(`Contract template with ID ${id} not found`);
    }

    return template;
  }

  async update(id: string, updateTemplateDto: any) {
    await this.findOne(id);

    return this.prisma.contractTemplate.update({
      where: { id },
      data: updateTemplateDto,
    });
  }

  async remove(id: string) {
    await this.findOne(id);

    return this.prisma.contractTemplate.delete({
      where: { id },
    });
  }

  async generateContract(templateId: string, variables: Record<string, any>) {
    const template = await this.findOne(templateId);

    let content = template.content;

    // Remplacer les placeholders par les variables
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      content = content.replace(new RegExp(placeholder, 'g'), value);
    });

    return {
      content,
      templateId: template.id,
      contractType: template.contractType,
    };
  }
}

@Injectable()
export class ContractsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createContractDto: CreateContractDto) {
    const { employeeId, companyId, ...contractData } = createContractDto;

    // Vérifier que l'employé existe
    const employee = await this.prisma.employeeData.findUnique({
      where: { id: employeeId },
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${employeeId} not found`);
    }

    // Vérifier que l'entreprise existe
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }

    // Générer un numéro de contrat unique
    const contractNumber = await this.generateContractNumber(companyId);

    return this.prisma.contract.create({
      data: {
        ...contractData,
        contractNumber,
        employee: { connect: { id: employeeId } },
        company: { connect: { id: companyId } },
        template: contractData.templateId
          ? { connect: { id: contractData.templateId } }
          : undefined,
      },
      include: this.getContractInclude(),
    });
  }

  async findAll(query: ContractQueryDto) {
    const { page = 1, limit = 10, ...filters } = query;
    const skip = (page - 1) * limit;

    const where: Prisma.ContractWhereInput = {};

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.contractType) {
      where.contractType = filters.contractType;
    }

    if (filters.employeeId) {
      where.employeeId = filters.employeeId;
    }

    if (filters.companyId) {
      where.companyId = filters.companyId;
    }

    const [contracts, total] = await Promise.all([
      this.prisma.contract.findMany({
        where,
        skip,
        take: limit,
        include: this.getContractInclude(),
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.contract.count({ where }),
    ]);

    return {
      data: contracts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string) {
    const contract = await this.prisma.contract.findUnique({
      where: { id },
      include: this.getContractInclude(),
    });

    if (!contract) {
      throw new NotFoundException(`Contract with ID ${id} not found`);
    }

    return contract;
  }

  async update(id: string, updateContractDto: UpdateContractDto) {
    const existingContract = await this.findOne(id);

    // Vérifier si le contrat peut être modifié
    if (
      existingContract.status === ContractStatusEnum.ACTIVE &&
      existingContract.signedAt
    ) {
      throw new BadRequestException(
        'Cannot modify a signed and active contract',
      );
    }

    return this.prisma.contract.update({
      where: { id },
      data: updateContractDto,
      include: this.getContractInclude(),
    });
  }

  async remove(id: string) {
    const contract = await this.findOne(id);

    if (contract.status === ContractStatusEnum.ACTIVE) {
      throw new BadRequestException('Cannot delete an active contract');
    }

    return this.prisma.contract.delete({
      where: { id },
    });
  }

  async signContract(id: string, signContractDto: SignContractDto) {
    const contract = await this.findOne(id);

    if (contract.status !== ContractStatusEnum.PENDING_APPROVAL) {
      throw new BadRequestException(
        'Contract must be in pending approval status to be signed',
      );
    }

    return this.prisma.contract.update({
      where: { id },
      data: {
        status: ContractStatusEnum.ACTIVE,
        signedAt: new Date(signContractDto.signedAt),
      },
      include: this.getContractInclude(),
    });
  }

  async getContractStats(companyId?: string): Promise<ContractStatsDto> {
    const where: Prisma.ContractWhereInput = companyId ? { companyId } : {};

    const [total, active, expired, draft, pendingApproval] = await Promise.all([
      this.prisma.contract.count({ where }),
      this.prisma.contract.count({
        where: { ...where, status: ContractStatusEnum.ACTIVE },
      }),
      this.prisma.contract.count({
        where: { ...where, status: ContractStatusEnum.EXPIRED },
      }),
      this.prisma.contract.count({
        where: { ...where, status: ContractStatusEnum.DRAFT },
      }),
      this.prisma.contract.count({
        where: { ...where, status: ContractStatusEnum.PENDING_APPROVAL },
      }),
    ]);

    return {
      total,
      active,
      expired,
      draft,
      pendingApproval,
    };
  }

  async getExpiringContracts(companyId?: string, daysAhead: number = 30) {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);

    const where: Prisma.ContractWhereInput = {
      status: ContractStatusEnum.ACTIVE,
      endDate: {
        lte: futureDate,
        gte: new Date(),
      },
    };

    if (companyId) {
      where.companyId = companyId;
    }

    return this.prisma.contract.findMany({
      where,
      include: this.getContractInclude(),
      orderBy: { endDate: 'asc' },
    });
  }

  private async generateContractNumber(companyId: string): Promise<string> {
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
      select: { companyName: true },
    });

    const year = new Date().getFullYear();
    const companyCode =
      company?.companyName.substring(0, 3).toUpperCase() || 'COM';

    const lastContract = await this.prisma.contract.findFirst({
      where: { companyId },
      orderBy: { createdAt: 'desc' },
      select: { contractNumber: true },
    });

    let sequence = 1;
    if (lastContract?.contractNumber) {
      const lastSequence = parseInt(
        lastContract.contractNumber.split('-').pop() || '0',
      );
      sequence = lastSequence + 1;
    }

    return `${companyCode}-${year}-${sequence.toString().padStart(4, '0')}`;
  }

  private getContractInclude(): Prisma.ContractInclude {
    return {
      employee: {
        include: {
          user: {
            select: {
              id: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          department: true,
          position: true,
        },
      },
      company: {
        select: {
          id: true,
          companyName: true,
          email: true,
        },
      },
      template: true,
      documents: true,
    };
  }
}
