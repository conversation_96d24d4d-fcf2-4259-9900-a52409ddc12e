import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JobOffersService } from './job-offers.service';
import { CreateJobOfferDto } from './dto/create-job-offer.dto';
import { UpdateJobOfferDto } from './dto/update-job-offer.dto';
import { Public } from '../shared/decorators/public.decorator';

@ApiTags("Offres d'emploi")
@Controller('api/v1/companies/:companyId/job-offers')
export class JobOffersController {
  constructor(private readonly jobOffersService: JobOffersService) {}

  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: "Créer une nouvelle offre d'emploi" })
  @ApiResponse({
    status: 201,
    description: "L'offre d'emploi a été créée avec succès.",
  })
  @ApiResponse({ status: 403, description: 'Accès non autorisé.' })
  create(
    @Body() createJobOfferDto: CreateJobOfferDto,
    @Param('companyId') companyId: string,
  ) {
    return this.jobOffersService.create(companyId, createJobOfferDto);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: "Récupérer toutes les offres d'emploi" })
  @ApiResponse({
    status: 200,
    description: "Liste des offres d'emploi récupérée avec succès.",
  })
  findAll() {
    return this.jobOffersService.findAll();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: "Récupérer une offre d'emploi par son ID" })
  @ApiResponse({ status: 200, description: "L'offre d'emploi a été trouvée." })
  @ApiResponse({
    status: 404,
    description: "L'offre d'emploi n'a pas été trouvée.",
  })
  findOne(@Param('id') id: string) {
    return this.jobOffersService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: "Mettre à jour une offre d'emploi" })
  @ApiResponse({
    status: 200,
    description: "L'offre d'emploi a été mise à jour avec succès.",
  })
  @ApiResponse({
    status: 404,
    description: "L'offre d'emploi n'a pas été trouvée.",
  })
  update(
    @Param('id') id: string,
    @Body() updateJobOfferDto: UpdateJobOfferDto,
  ) {
    return this.jobOffersService.update(id, updateJobOfferDto);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: "Supprimer une offre d'emploi" })
  @ApiResponse({
    status: 200,
    description: "L'offre d'emploi a été supprimée avec succès.",
  })
  @ApiResponse({
    status: 404,
    description: "L'offre d'emploi n'a pas été trouvée.",
  })
  remove(@Param('id') id: string) {
    return this.jobOffersService.remove(id);
  }

  @Post(':id/save')
  @ApiBearerAuth()
  @ApiOperation({ summary: "Sauvegarder une offre d'emploi" })
  @ApiResponse({
    status: 200,
    description: "L'offre d'emploi a été sauvegardée avec succès.",
  })
  saveJobOffer(@Param('id') id: string, @Request() req) {
    return this.jobOffersService.saveJobOffer(id, req.user.id);
  }

  @Get('saved')
  @ApiBearerAuth()
  @ApiOperation({ summary: "Récupérer les offres d'emploi sauvegardées" })
  @ApiResponse({
    status: 200,
    description:
      "Liste des offres d'emploi sauvegardées récupérée avec succès.",
  })
  getSavedJobOffers(@Request() req) {
    return this.jobOffersService.getSavedJobOffers(req.user.id);
  }
}
