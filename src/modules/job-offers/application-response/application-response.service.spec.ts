import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationResponseService } from './application-response.service';

describe('ApplicationResponseService', () => {
  let service: ApplicationResponseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ApplicationResponseService],
    }).compile();

    service = module.get<ApplicationResponseService>(ApplicationResponseService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
