import { Controller, Get, Post, Param, Body } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ApplicationResponseService } from './application-response.service';
import { CreateApplicationResponseDto } from '../dto/create-application-response.dto';
import { Public } from 'src/modules/shared/decorators/public.decorator';

@ApiTags('Réponses aux candidatures')
@Public()
@Controller('api/v1/companies/:companyId/application-responses')
export class ApplicationResponsesController {
  constructor(
    private readonly applicationResponsesService: ApplicationResponseService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer une réponse à une candidature' })
  @ApiResponse({
    status: 201,
    description: 'La réponse a été créée avec succès.',
  })
  @ApiResponse({ status: 403, description: 'Accès non autorisé.' })
  create(
    @Body() createApplicationResponseDto: CreateApplicationResponseDto,
    @Param('companyId') companyId: string,
  ) {
    return this.applicationResponsesService.createForCompany(
      companyId,
      createApplicationResponseDto,
    );
  }

  @Get()
  @ApiBearerAuth()
  @ApiOperation({ summary: "Récupérer toutes les réponses de l'entreprise" })
  @ApiResponse({
    status: 200,
    description: 'Liste des réponses récupérée avec succès.',
  })
  findAll(@Param('companyId') companyId: string) {
    return this.applicationResponsesService.findAllForCompany(companyId);
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer une réponse par son ID' })
  @ApiResponse({ status: 200, description: 'La réponse a été trouvée.' })
  @ApiResponse({ status: 404, description: "La réponse n'a pas été trouvée." })
  findOne(@Param('id') id: string, @Param('companyId') companyId: string) {
    return this.applicationResponsesService.findOneForCompany(id, companyId);
  }

  @Get('applications/:applicationId')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les réponses pour une candidature' })
  @ApiResponse({
    status: 200,
    description: 'Liste des réponses récupérée avec succès.',
  })
  findByApplicationId(
    @Param('applicationId') applicationId: string,
    @Param('companyId') companyId: string,
  ) {
    return this.applicationResponsesService.findByApplicationIdForCompany(
      applicationId,
      companyId,
    );
  }
}
