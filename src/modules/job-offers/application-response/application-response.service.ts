import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateApplicationResponseDto } from '../dto/create-application-response.dto';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';

@Injectable()
export class ApplicationResponseService {
  constructor(private prisma: PrismaService) {}

  /**
   * Crée une nouvelle réponse à une candidature pour une entreprise spécifique
   * @param companyId L'ID de l'entreprise
   * @param createApplicationResponseDto Les données de la réponse
   * @returns La réponse créée
   */
  async createForCompany(
    companyId: string,
    createApplicationResponseDto: CreateApplicationResponseDto,
  ) {
    // Vérifie que l'application appartient à l'entreprise
    const application = await this.prisma.application.findUnique({
      where: { id: createApplicationResponseDto.applicationId },
      include: {
        jobOffer: true,
      },
    });

    if (!application || application.jobOffer.companyId !== companyId) {
      throw new NotFoundException(
        `Application avec l'ID ${createApplicationResponseDto.applicationId} non trouvée pour cette entreprise`,
      );
    }

    return this.prisma.applicationResponse.create({
      data: {
        ...createApplicationResponseDto,
        applicationResponseDate: new Date(),
      },
      include: {
        application: {
          include: {
            user: true,
            jobOffer: {
              include: {
                company: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Récupère toutes les réponses aux candidatures pour une entreprise spécifique
   * @param companyId L'ID de l'entreprise
   * @returns Liste de toutes les réponses de l'entreprise
   */
  async findAllForCompany(companyId: string) {
    return this.prisma.applicationResponse.findMany({
      where: {
        application: {
          jobOffer: {
            companyId: companyId,
          },
        },
      },
      include: {
        application: {
          include: {
            user: true,
            jobOffer: {
              include: {
                company: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Récupère une réponse par son ID pour une entreprise spécifique
   * @param id L'ID de la réponse
   * @param companyId L'ID de l'entreprise
   * @returns La réponse trouvée
   * @throws NotFoundException si la réponse n'est pas trouvée ou n'appartient pas à l'entreprise
   */
  async findOneForCompany(id: string, companyId: string) {
    const response = await this.prisma.applicationResponse.findUnique({
      where: { id },
      include: {
        application: {
          include: {
            jobOffer: true,
            user: true,
          },
        },
      },
    });

    if (!response || response.application.jobOffer.companyId !== companyId) {
      throw new NotFoundException(
        `Réponse avec l'ID ${id} non trouvée pour cette entreprise`,
      );
    }

    return response;
  }

  /**
   * Récupère toutes les réponses pour une candidature d'une entreprise spécifique
   * @param applicationId L'ID de la candidature
   * @param companyId L'ID de l'entreprise
   * @returns Liste des réponses pour la candidature
   */
  async findByApplicationIdForCompany(
    applicationId: string,
    companyId: string,
  ) {
    // Vérifie d'abord que l'application appartient à l'entreprise
    const application = await this.prisma.application.findUnique({
      where: { id: applicationId },
      include: {
        jobOffer: true,
      },
    });

    if (!application || application.jobOffer.companyId !== companyId) {
      throw new NotFoundException(
        `Application avec l'ID ${applicationId} non trouvée pour cette entreprise`,
      );
    }

    return this.prisma.applicationResponse.findMany({
      where: { applicationId },
      include: {
        application: {
          include: {
            user: true,
            jobOffer: {
              include: {
                company: true,
              },
            },
          },
        },
      },
      orderBy: {
        applicationResponseDate: 'desc',
      },
    });
  }
}
