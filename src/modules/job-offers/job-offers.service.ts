import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateJobOfferDto } from './dto/create-job-offer.dto';
import { UpdateJobOfferDto } from './dto/update-job-offer.dto';
import { PrismaService } from '../shared/db/prisma/prisma.service';

@Injectable()
export class JobOffersService {
  constructor(private prisma: PrismaService) {}

  /**
   * Crée une nouvelle offre d'emploi
   * @param createJobOfferDto Les données de l'offre d'emploi à créer
   * @returns L'offre d'emploi créée
   */
  async create(companyId: string, createJobOfferDto: CreateJobOfferDto) {
    return this.prisma.jobOffer.create({
      data: {
        title: createJobOfferDto.title,
        description: createJobOfferDto.description,
        publishDate: createJobOfferDto.publishDate,
        expirationDate: createJobOfferDto.expirationDate,
        status: createJobOfferDto.status,
        location: createJobOfferDto.location,
        contractTypes: createJobOfferDto.contractTypes,
        minSalary: createJobOfferDto.minSalary,
        maxSalary: createJobOfferDto.maxSalary,
        requiredSkills: createJobOfferDto.requiredSkills || [],
        company: {
          connect: { id: companyId },
        },
        ...(createJobOfferDto.departmentId && {
          department: {
            connect: { id: createJobOfferDto.departmentId },
          },
        }),
        ...(createJobOfferDto.positionId && {
          position: {
            connect: { id: createJobOfferDto.positionId },
          },
        }),
      },
      include: {
        company: true,
        department: true,
        position: true,
      },
    });
  }

  /**
   * Récupère toutes les offres d'emploi
   * @returns Liste de toutes les offres d'emploi
   */
  async findAll() {
    return this.prisma.jobOffer.findMany({
      include: {
        company: true,
        department: true,
        position: true,
        applications: true,
      },
    });
  }

  /**
   * Récupère une offre d'emploi par son ID
   * @param id L'ID de l'offre d'emploi
   * @returns L'offre d'emploi trouvée
   * @throws NotFoundException si l'offre n'est pas trouvée
   */
  async findOne(id: string) {
    const jobOffer = await this.prisma.jobOffer.findUnique({
      where: { id },
      include: {
        company: true,
        department: true,
        position: true,
        applications: true,
      },
    });

    if (!jobOffer) {
      throw new NotFoundException(`Offre d'emploi avec l'ID ${id} non trouvée`);
    }

    return jobOffer;
  }

  /**
   * Met à jour une offre d'emploi
   * @param id L'ID de l'offre d'emploi à mettre à jour
   * @param updateJobOfferDto Les données de mise à jour
   * @returns L'offre d'emploi mise à jour
   * @throws NotFoundException si l'offre n'est pas trouvée
   */
  async update(id: string, updateJobOfferDto: UpdateJobOfferDto) {
    try {
      const updateData: any = { ...updateJobOfferDto };

      // Gérer les relations
      if (updateJobOfferDto.departmentId !== undefined) {
        if (updateJobOfferDto.departmentId === null) {
          updateData.department = { disconnect: true };
        } else {
          updateData.department = {
            connect: { id: updateJobOfferDto.departmentId },
          };
        }
        delete updateData.departmentId;
      }

      if (updateJobOfferDto.positionId !== undefined) {
        if (updateJobOfferDto.positionId === null) {
          updateData.position = { disconnect: true };
        } else {
          updateData.position = {
            connect: { id: updateJobOfferDto.positionId },
          };
        }
        delete updateData.positionId;
      }

      return await this.prisma.jobOffer.update({
        where: { id },
        data: updateData,
        include: {
          company: true,
          department: true,
          position: true,
        },
      });
    } catch (error) {
      throw new NotFoundException(`Offre d'emploi avec l'ID ${id} non trouvée`);
    }
  }

  /**
   * Supprime une offre d'emploi
   * @param id L'ID de l'offre d'emploi à supprimer
   * @returns L'offre d'emploi supprimée
   * @throws NotFoundException si l'offre n'est pas trouvée
   */
  async remove(id: string) {
    try {
      return await this.prisma.jobOffer.delete({
        where: { id },
      });
    } catch (error) {
      throw new NotFoundException(`Offre d'emploi avec l'ID ${id} non trouvée`);
    }
  }

  /**
   * Sauvegarde une offre d'emploi pour un utilisateur
   * @param jobOfferId L'ID de l'offre d'emploi
   * @param userId L'ID de l'utilisateur
   * @returns L'offre d'emploi sauvegardée
   */
  async saveJobOffer(jobOfferId: string, userId: string) {
    return this.prisma.jobOffer.update({
      where: { id: jobOfferId },
      data: {
        savedByUsers: {
          connect: { id: userId },
        },
      },
    });
  }

  /**
   * Récupère les offres d'emploi sauvegardées par un utilisateur
   * @param userId L'ID de l'utilisateur
   * @returns Liste des offres d'emploi sauvegardées
   */
  async getSavedJobOffers(userId: string) {
    return this.prisma.jobOffer.findMany({
      where: {
        savedByUsers: {
          some: {
            id: userId,
          },
        },
      },
      include: {
        company: true,
        department: true,
        position: true,
      },
    });
  }
}
