import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ApplicationService } from './application.service';
import {
  CreateApplicationDto,
  UpdateApplicationDto,
} from '../dto/create-application.dto';
import { Public } from 'src/modules/shared/decorators/public.decorator';

@ApiTags('Candidatures')
@Public()
@Controller('api/v1/companies/:companyId/applications')
export class ApplicationsController {
  constructor(private readonly applicationsService: ApplicationService) {}

  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: "Postuler à une offre d'emploi" })
  @ApiResponse({
    status: 201,
    description: 'La candidature a été créée avec succès.',
  })
  @ApiResponse({ status: 403, description: 'Accès non autorisé.' })
  create(
    @Body() createApplicationDto: CreateApplicationDto,
    @Param('companyId') companyId: string,
    @Request() req,
  ) {
    return this.applicationsService.create(req.user.id, createApplicationDto);
  }

  @Get()
  @ApiBearerAuth()
  @ApiOperation({
    summary: "Récupérer toutes les candidatures de l'entreprise",
  })
  @ApiResponse({
    status: 200,
    description: 'Liste des candidatures récupérée avec succès.',
  })
  findAll(@Param('companyId') companyId: string) {
    return this.applicationsService.findAllByCompany(companyId);
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer une candidature par son ID' })
  @ApiResponse({ status: 200, description: 'La candidature a été trouvée.' })
  @ApiResponse({
    status: 404,
    description: "La candidature n'a pas été trouvée.",
  })
  findOne(@Param('id') id: string, @Param('companyId') companyId: string) {
    return this.applicationsService.findOneForCompany(id, companyId);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour une candidature' })
  @ApiResponse({
    status: 200,
    description: 'La candidature a été mise à jour avec succès.',
  })
  @ApiResponse({
    status: 404,
    description: "La candidature n'a pas été trouvée.",
  })
  update(
    @Param('id') id: string,
    @Param('companyId') companyId: string,
    @Body() updateApplicationDto: UpdateApplicationDto,
  ) {
    return this.applicationsService.updateForCompany(
      id,
      companyId,
      updateApplicationDto,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprimer une candidature' })
  @ApiResponse({
    status: 200,
    description: 'La candidature a été supprimée avec succès.',
  })
  @ApiResponse({
    status: 404,
    description: "La candidature n'a pas été trouvée.",
  })
  remove(@Param('id') id: string, @Param('companyId') companyId: string) {
    return this.applicationsService.removeForCompany(id, companyId);
  }

  @Get('job-offers/:jobId')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Récupérer les candidatures pour une offre spécifique',
  })
  @ApiResponse({
    status: 200,
    description: 'Liste des candidatures récupérée avec succès.',
  })
  findByJobId(
    @Param('jobId') jobId: string,
    @Param('companyId') companyId: string,
  ) {
    return this.applicationsService.findByJobIdForCompany(jobId, companyId);
  }

  @Get('users/me')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer mes candidatures' })
  @ApiResponse({
    status: 200,
    description: 'Liste de mes candidatures récupérée avec succès.',
  })
  findMyApplications(@Request() req) {
    return this.applicationsService.findByUserId(req.user.id);
  }
}
