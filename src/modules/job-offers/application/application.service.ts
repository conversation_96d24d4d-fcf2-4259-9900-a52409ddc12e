import { Injectable, NotFoundException } from '@nestjs/common';
import {
  CreateApplicationDto,
  UpdateApplicationDto,
} from '../dto/create-application.dto';
import { ApplicationStatusEnum } from '@prisma/client';
import { PrismaService } from 'src/modules/shared/db/prisma/prisma.service';

@Injectable()
export class ApplicationService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Crée une nouvelle candidature
   * @param userId L'ID de l'utilisateur qui postule
   * @param createApplicationDto Les données de la candidature
   * @returns La candidature créée
   */
  async create(userId: string, createApplicationDto: CreateApplicationDto) {
    const { jobId, ...applicationData } = createApplicationDto;

    return this.prisma.application.create({
      data: {
        applicationDate: new Date(),
        status: applicationData.status || ApplicationStatusEnum.PENDING,
        coverLetter: applicationData.coverLetter,
        resume: applicationData.resume,
        references: applicationData.references,
        additionalDocuments: applicationData.additionalDocuments,
        preferredStartDate: applicationData.preferredStartDate,
        currentEmploymentStatus: applicationData.currentEmploymentStatus,
        desiredSalary: applicationData.desiredSalary,
        userId: userId,
        jobId: jobId,
      },
      include: {
        user: true,
        jobOffer: {
          include: {
            company: true,
          },
        },
        applicationResponses: true,
      },
    });
  }

  /**
   * Récupère toutes les candidatures d'une entreprise
   * @param companyId L'ID de l'entreprise
   * @returns Liste de toutes les candidatures de l'entreprise
   */
  async findAllByCompany(companyId: string) {
    return this.prisma.application.findMany({
      where: {
        jobOffer: {
          companyId: companyId,
        },
      },
      include: {
        user: true,
        jobOffer: {
          include: {
            company: true,
          },
        },
        applicationResponses: true,
      },
    });
  }

  /**
   * Récupère une candidature par son ID pour une entreprise spécifique
   * @param id L'ID de la candidature
   * @param companyId L'ID de l'entreprise
   * @returns La candidature trouvée
   * @throws NotFoundException si la candidature n'est pas trouvée ou n'appartient pas à l'entreprise
   */
  async findOneForCompany(id: string, companyId: string) {
    const application = await this.prisma.application.findUnique({
      where: { id },
      include: {
        user: true,
        jobOffer: {
          include: {
            company: true,
          },
        },
        applicationResponses: true,
      },
    });

    if (!application || application.jobOffer.companyId !== companyId) {
      throw new NotFoundException(
        `Candidature avec l'ID ${id} non trouvée pour cette entreprise`,
      );
    }

    return application;
  }

  /**
   * Met à jour une candidature pour une entreprise spécifique
   * @param id L'ID de la candidature à mettre à jour
   * @param companyId L'ID de l'entreprise
   * @param updateApplicationDto Les données de mise à jour
   * @returns La candidature mise à jour
   * @throws NotFoundException si la candidature n'est pas trouvée ou n'appartient pas à l'entreprise
   */
  async updateForCompany(
    id: string,
    companyId: string,
    updateApplicationDto: UpdateApplicationDto,
  ) {
    const { jobId, ...updateData } = updateApplicationDto;

    // Vérifie d'abord que l'application appartient à l'entreprise
    await this.findOneForCompany(id, companyId);

    try {
      return await this.prisma.application.update({
        where: { id },
        data: {
          ...updateData,
          ...(jobId && { jobId }),
        },
        include: {
          user: true,
          jobOffer: {
            include: {
              company: true,
            },
          },
          applicationResponses: true,
        },
      });
    } catch (error) {
      throw new NotFoundException(`Candidature avec l'ID ${id} non trouvée`);
    }
  }

  /**
   * Supprime une candidature pour une entreprise spécifique
   * @param id L'ID de la candidature à supprimer
   * @param companyId L'ID de l'entreprise
   * @returns La candidature supprimée
   * @throws NotFoundException si la candidature n'est pas trouvée ou n'appartient pas à l'entreprise
   */
  async removeForCompany(id: string, companyId: string) {
    // Vérifie d'abord que l'application appartient à l'entreprise
    await this.findOneForCompany(id, companyId);

    try {
      return await this.prisma.application.delete({
        where: { id },
      });
    } catch (error) {
      throw new NotFoundException(`Candidature avec l'ID ${id} non trouvée`);
    }
  }

  /**
   * Récupère les candidatures d'un utilisateur
   * @param userId L'ID de l'utilisateur
   * @returns Liste des candidatures de l'utilisateur
   */
  async findByUserId(userId: string) {
    return this.prisma.application.findMany({
      where: { userId },
      include: {
        jobOffer: {
          include: {
            company: true,
          },
        },
        applicationResponses: true,
      },
    });
  }

  /**
   * Récupère les candidatures pour une offre d'emploi d'une entreprise spécifique
   * @param jobId L'ID de l'offre d'emploi
   * @param companyId L'ID de l'entreprise
   * @returns Liste des candidatures pour l'offre d'emploi
   */
  async findByJobIdForCompany(jobId: string, companyId: string) {
    return this.prisma.application.findMany({
      where: {
        jobId,
        jobOffer: {
          companyId: companyId,
        },
      },
      include: {
        user: true,
        applicationResponses: true,
        jobOffer: {
          include: {
            company: true,
          },
        },
      },
    });
  }
}
