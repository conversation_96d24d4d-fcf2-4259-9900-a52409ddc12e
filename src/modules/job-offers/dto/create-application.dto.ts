import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { ApplicationStatusEnum } from '@prisma/client';
import {
  IsOptional,
  IsDateString,
  IsEnum,
  IsInt,
  IsString,
  IsArray,
  IsNumber,
  IsNotEmpty,
  IsUUID,
  IsDate,
} from 'class-validator';

export class CreateApplicationDto {
  @ApiProperty({
    description: 'L\'ID de l\'offre d\'emploi',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  jobId: string;

  @ApiProperty({
    description: 'La lettre de motivation',
    example: 'Je suis très intéressé par ce poste...'
  })
  @IsString()
  @IsNotEmpty()
  coverLetter: string;

  @ApiProperty({
    description: 'Le CV (URL ou chemin du fichier)',
    example: 'https://example.com/cv.pdf'
  })
  @IsString()
  @IsNotEmpty()
  resume: string;

  @ApiProperty({
    description: 'Les références professionnelles',
    example: '<PERSON>, Directeur Technique, +1234567890'
  })
  @IsString()
  @IsOptional()
  references?: string;

  @ApiProperty({
    description: 'Documents additionnels (URLs ou chemins des fichiers)',
    example: ['https://example.com/cert1.pdf', 'https://example.com/cert2.pdf']
  })
  @IsString({ each: true })
  @IsOptional()
  additionalDocuments?: string[];

  @ApiProperty({
    description: 'Date de début souhaitée',
    example: '2024-04-01T00:00:00Z'
  })
  @IsDate()
  @IsOptional()
  preferredStartDate?: Date;

  @ApiProperty({
    description: 'Statut d\'emploi actuel',
    example: 'Employé à temps plein'
  })
  @IsString()
  @IsOptional()
  currentEmploymentStatus?: string;

  @ApiProperty({
    description: 'Salaire souhaité',
    example: 50000
  })
  @IsNumber()
  @IsOptional()
  desiredSalary?: number;

  @ApiProperty({
    description: 'Statut de la candidature',
    enum: ApplicationStatusEnum,
    example: ApplicationStatusEnum.PENDING
  })
  @IsEnum(ApplicationStatusEnum)
  @IsOptional()
  status?: ApplicationStatusEnum;
}

export class UpdateApplicationDto extends PartialType(CreateApplicationDto) {}
