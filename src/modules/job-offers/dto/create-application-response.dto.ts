import { IsString, <PERSON>NotEmpty, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateApplicationResponseDto {
  @ApiProperty({
    description: 'L\'ID de la candidature',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  applicationId: string;

  @ApiProperty({
    description: 'Le contenu de la réponse',
    example: 'Nous avons bien reçu votre candidature et nous vous remercions...'
  })
  @IsString()
  @IsNotEmpty()
  content: string;
}
