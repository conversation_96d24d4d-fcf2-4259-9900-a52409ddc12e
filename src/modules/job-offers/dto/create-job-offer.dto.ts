import {
  IsString,
  <PERSON>NotEmpty,
  IsDate,
  IsUUID,
  IsOptional,
  IsEnum,
  IsArray,
  IsNumber,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ContractTypeEnum, JobOfferStatusEnum } from '@prisma/client';

export class CreateJobOfferDto {
  @ApiProperty({
    description: 'Le titre du poste',
    example: 'Développeur Full Stack',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'La description détaillée du poste',
    example: 'Nous recherchons un développeur full stack expérimenté...',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: "La date de publication de l'offre",
    example: '2024-03-20T00:00:00Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  publishDate: Date;

  @ApiProperty({
    description: "La date d'expiration de l'offre",
    example: '2024-04-20T00:00:00Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  expirationDate: Date;

  @ApiProperty({
    description: "Le statut de l'offre d'emploi",
    enum: JobOfferStatusEnum,
    default: JobOfferStatusEnum.ACTIVE,
    example: JobOfferStatusEnum.ACTIVE,
  })
  @IsEnum(JobOfferStatusEnum)
  @IsOptional()
  status?: JobOfferStatusEnum;

  @ApiProperty({
    description: 'La localisation du poste',
    example: 'Paris, France',
  })
  @IsString()
  @IsNotEmpty()
  location: string;

  @ApiProperty({
    description: 'Les types de contrat (peut en combiner plusieurs)',
    enum: ContractTypeEnum,
    isArray: true,
    example: [ContractTypeEnum.FULL_TIME, ContractTypeEnum.REMOTE],
  })
  @IsEnum(ContractTypeEnum, { each: true })
  @IsArray()
  @IsNotEmpty()
  contractTypes: ContractTypeEnum[];

  @ApiProperty({
    description: 'Salaire minimum (optionnel)',
    example: 40000,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  minSalary?: number;

  @ApiProperty({
    description: 'Salaire maximum (optionnel)',
    example: 60000,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  maxSalary?: number;

  @ApiProperty({
    description: 'Compétences requises pour le poste',
    example: ['JavaScript', 'TypeScript', 'Angular', 'NestJS'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  requiredSkills?: string[];

  @ApiProperty({
    description: "L'ID du département (optionnel)",
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  departmentId?: string;

  @ApiProperty({
    description: "L'ID du poste (optionnel)",
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  positionId?: string;
}
