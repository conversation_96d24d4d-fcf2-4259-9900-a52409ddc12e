import { Modu<PERSON> } from '@nestjs/common';
import { JobOffersService } from './job-offers.service';
import { JobOffersController } from './job-offers.controller';
import { ApplicationService } from './application/application.service';
import { ApplicationResponseService } from './application-response/application-response.service';
import { ApplicationsController } from './application/application.controller';
import { ApplicationResponsesController } from './application-response/application-response.controller';

@Module({
  controllers: [
    JobOffersController,
    ApplicationsController,
    ApplicationResponsesController,
  ],
  providers: [JobOffersService, ApplicationService, ApplicationResponseService],
  exports: [JobOffersService, ApplicationService, ApplicationResponseService],
})
export class JobOffersModule {}
