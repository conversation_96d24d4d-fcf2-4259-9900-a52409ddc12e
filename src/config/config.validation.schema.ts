import * as <PERSON><PERSON> from 'joi';

export const configValidationSchema = Joi.object({
  SALT_ROUNDS: Joi.number().required(),

  JWT_SECRET: Joi.string().required(),
  JWT_TOKEN_AUDIENCE: Joi.string().required(),
  JWT_TOKEN_ISSUER: Joi.string().required(),
  JWT_ACCESS_TOKEN_TTL: Joi.number().required(),
  JWT_REFRESH_TOKEN_TTL: Joi.number().required(),
  MAIL_HOST: Joi.string().required(),
  MAIL_PORT: Joi.number().required(),
  MAIL_SECURE: Joi.boolean().required(),
  MAIL_AUTH_TYPE: Joi.string().required(),
  MAIL_SENDER: Joi.string().required(),
  CLIENT_URL: Joi.string().required(),
});
