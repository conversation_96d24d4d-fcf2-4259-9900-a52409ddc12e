import { Module } from '@nestjs/common';
import { CompanyModule } from './modules/company/company.module';
import { SharedModule } from './modules/shared/shared.module';
import { ConfigModule } from '@nestjs/config';
import { configValidationSchema } from './config/config.validation.schema';
import { JwtGuard } from './modules/shared/guards/jtw.guard';
import { APP_GUARD } from '@nestjs/core';
import { IamModule } from './modules/iam/iam.module';
import { EmployeeModule } from './modules/employee/employee.module';
import { JobOffersModule } from './modules/job-offers/job-offers.module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.NODE_ENV}`,
      validationSchema: configValidationSchema,
    }),
    IamModule,
    CompanyModule,
    EmployeeModule,
    JobOffersModule,
    SharedModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: JwtGuard,
    },
  ],
})
export class AppModule {}
