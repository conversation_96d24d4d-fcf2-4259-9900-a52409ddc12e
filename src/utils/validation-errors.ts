import { Separators, StringUtils } from './string.utils';

export class ValidationErrors {
  static emptyFieldError(fieldName: string) {
    return `${this.formatString(fieldName)} cannot be empty`;
  }

  static validString(fieldName: string) {
    return `${this.formatString(
      fieldName,
    )} should be a valid string and not be empty`;
  }

  static validEmail(fieldName: string) {
    return `${this.formatString(fieldName)} adresse is not a valid email`;
  }

  static validPassword(fieldName: string) {
    return `${this.formatString(fieldName)} doesn't match the requirements`;
  }

  static formatString(fieldName: string) {
    const textWithSpace = StringUtils.camelCaseToSeparatorWith(
      Separators.space,
      fieldName,
    );
    return StringUtils.capitalize(textWithSpace, true);
  }
}
