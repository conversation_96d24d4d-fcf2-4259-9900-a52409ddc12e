interface Obj {
  [key: string]: any;
}

export function getObjectSize(obj: Obj): number {
  return Object.keys(obj).length;
}

export function objectsDiff(incoming: Obj, current: Obj) {
  const diffs = {};
  for (const attr in incoming) {
    if (incoming[attr] !== current[attr]) {
      diffs[attr] = incoming[attr];
    }
  }
  return diffs;
}

export function isObjectEmpty(obj: Obj) {
  return getObjectSize(obj) === 0;
}
