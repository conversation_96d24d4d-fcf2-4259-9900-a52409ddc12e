export enum Separators {
  underscore = '_',
  dash = '-',
  space = ' ',
}

export class StringUtils {
  static camelCaseToSeparatorWith(separator: Separators, text: string): string {
    return text.replace(
      /[A-Z]/g,
      (letter) => `${separator}${letter.toLowerCase()}`,
    );
  }

  static capitalize(text: string, firstOnly = false) {
    let capitalized = text[0].toUpperCase();
    if (firstOnly) {
      return capitalized + text.slice(1).toLowerCase();
    }
    for (let i = 1; i < text.length; i++) {
      if (text[i - 1] === ' ') {
        capitalized += text[i].toUpperCase();
      } else {
        capitalized += text[i].toLowerCase();
      }
    }
    return capitalized;
  }
}
