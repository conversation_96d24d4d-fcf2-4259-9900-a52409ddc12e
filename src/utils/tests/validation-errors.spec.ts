import { ValidationErrors } from '../validation-errors';

describe.each([
  { input: 'firstName', expected: 'First name cannot be empty' },
  { input: 'lastName', expected: 'Last name cannot be empty' },
  { input: 'nomComplet', expected: 'Nom complet cannot be empty' },
  { input: 'email', expected: 'Email cannot be empty' },
])('Should display Field name cannot be empty', ({ input, expected }) => {
  it(`Should transform return ${expected} for input field ${input}`, () => {
    expect(ValidationErrors.emptyFieldError(input)).toBe(expected);
  });
});

describe.each([
  {
    input: 'firstName',
    expected: 'First name should be a valid string and not be empty',
  },
  {
    input: 'lastName',
    expected: 'Last name should be a valid string and not be empty',
  },
  {
    input: 'nomComplet',
    expected: 'Nom complet should be a valid string and not be empty',
  },
  {
    input: 'email',
    expected: 'Email should be a valid string and not be empty',
  },
])(
  'Should display Field name should be a valid string and not be empty',
  ({ input, expected }) => {
    it(`Should transform return ${expected} for input field ${input}`, () => {
      expect(ValidationErrors.validString(input)).toBe(expected);
    });
  },
);

describe.each([
  {
    input: 'email',
    expected: 'Email adresse is not a valid email',
  },
])('Should display Field name  is not a valid email', ({ input, expected }) => {
  it(`Should transform return ${expected} for input field ${input}`, () => {
    expect(ValidationErrors.validEmail(input)).toBe(expected);
  });
});

describe.each([
  {
    input: 'password',
    expected: "Password doesn't match the requirements",
  },
])(
  "Should display Field name doesn't match the requirements",
  ({ input, expected }) => {
    it(`Should transform return ${expected} for input field ${input}`, () => {
      expect(ValidationErrors.validPassword(input)).toBe(expected);
    });
  },
);
