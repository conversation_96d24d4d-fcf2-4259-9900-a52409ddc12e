import { getObjectSize, isObjectEmpty, objectsDiff } from '../object-utils';

describe.each([
  { obj: {}, expected: true },
  { obj: { name: 'chris' }, expected: false },
])('Test if object is empty', ({ obj, expected }) => {
  it(`should return ${expected} if object is ${JSON.stringify(obj)}`, () => {
    expect(isObjectEmpty(obj)).toBe(expected);
  });
});

describe.each([
  { obj: {}, expected: 0 },
  { obj: { name: 'chris' }, expected: 1 },
  { obj: { name: 'chris', country: 'congo' }, expected: 2 },
  {
    obj: {
      name: 'chris',
      country: 'congo',
      profession: 'developer',
      position: 'techlead',
    },
    expected: 4,
  },
])('Test object size', ({ obj, expected }) => {
  it(`should return ${expected} if object is ${JSON.stringify(obj)}`, () => {
    expect(getObjectSize(obj)).toBe(expected);
  });
});

describe.each([
  {
    incoming: { a: 1, b: 1, c: 1 },
    current: { a: 1, b: 1, c: 1 },
    expected: {},
  },
  {
    incoming: { z: 1, b: 1, x: 1 },
    current: { a: 1, b: 1, c: 1 },
    expected: { z: 1, x: 1 },
  },
  {
    incoming: { '2022-11-20': 1, '2022-11-21': 1, '2022-11-22': 1 },
    current: { '2022-11-20': 1, '2022-11-21': 1, '2022-11-22': 1 },
    expected: {},
  },
  {
    incoming: {
      '2022-11-20': 1,
      '2022-11-21': 1,
      '2022-11-22': 1,
      '2023-01-5': 1,
      '2023-01-06': 1,
    },
    current: { '2022-11-20': 1, '2022-11-21': 1, '2022-11-22': 1 },
    expected: { '2023-01-5': 1, '2023-01-06': 1 },
  },
  ,
])('Test object difference', ({ incoming, current, expected }) => {
  it(`should return ${expected} if incoming object is ${JSON.stringify(
    incoming,
  )} and current object  ${JSON.stringify(current)}`, () => {
    expect(objectsDiff(incoming, current)).toStrictEqual(expected);
  });
});
