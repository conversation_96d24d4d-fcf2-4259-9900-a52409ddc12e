import { DateUtil } from '../date.util';

describe.each([
  {
    dates: ['2022-12-30', '2022-12-01', '2022-12-02', '2022-12-30'],
    expected: true,
  },
  {
    dates: ['2021-12-30', '2022-12-01', '2022-12-02', '2022-12-30'],
    expected: false,
  },
  {
    dates: ['2022-11-28', '2022-12-01', '2022-12-02', '2022-12-30'],
    expected: false,
  },
  {
    dates: ['2022-11-29', '2022-12-01', '2022-12-02', '2022-12-30'],
    expected: false,
  },
])('Testing date utils', ({ dates, expected }) => {
  it('Should return true if the dates are all in the future', () => {
    expect(DateUtil.areNotPastDates(dates)).toBe(expected);
  });
});

describe.each([
  {
    dates: ['2022-11-30', '2022-12-01', '2022-12-02', '2022-12-03'],
    expected: false,
  },
  {
    dates: ['2021-11-30', '2022-12-01', '2022-12-02', '2022-12-05'],
    expected: true,
  },
  {
    dates: ['2022-11-20', '2022-12-01', '2022-11-27', '2022-12-30'],
    expected: false,
  },
  {
    dates: ['2022-12-05', '2022-12-06', '2022-12-07', '2022-12-08'],
    expected: true,
  },
])('Testing date utils', ({ dates, expected }) => {
  it('Should return true if the dates are not weekends', () => {
    expect(DateUtil.areNotWeekends(dates)).toBe(expected);
  });
});
