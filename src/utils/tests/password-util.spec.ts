import { PasswordUtil } from '../password-util';
import * as bcrypt from 'bcrypt';

let hashed = '';
let password = '';
beforeAll(async () => {
  password = 'AbcBca23';
  hashed = await PasswordUtil.hashPassword(password);
});

describe('Test password Hashing', () => {
  it('should start with $', async () => {
    expect(hashed).toMatch(/$/);
  });

  it('should be 60 character long', async () => {
    expect(hashed.length).toBe(60);
  });
});

describe('Test password Hashing comparison', () => {
  it('Should return true when the same password is provided', async () => {
    const providedPassword = 'AbcBca23';
    const isMatch = await bcrypt.compare(providedPassword, hashed);
    expect(isMatch).toBe(true);
  });

  it('Should return false when the provided password is different from the one set', async () => {
    const providedPassword = 'AbcBca2';
    const isMatch = await bcrypt.compare(providedPassword, hashed);
    expect(isMatch).toBe(false);
  });
});
