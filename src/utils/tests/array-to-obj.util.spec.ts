import { arrayToObj } from '../array-to-obj.util';

describe.each([
  {
    array: ['2022-11-30', '2022-12-01', '2022-12-02', '2022-12-30'],
    expected: {
      '2022-11-30': 1,
      '2022-12-01': 1,
      '2022-12-02': 1,
      '2022-12-30': 1,
    },
  },
  {
    array: ['2019-01-02', '2015-11-11'],
    expected: { '2019-01-02': 1, '2015-11-11': 1 },
  },
])('Testing array being transform to objects', ({ array, expected }) => {
  it('Should test that string array to become a valid object', function () {
    expect(arrayToObj(array)).toStrictEqual(expected);
  });
});
