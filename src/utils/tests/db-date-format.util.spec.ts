import { DateFormator } from '../db-date-format.util';

describe.each([
  {
    array: [
      '2022-11-27T21:00:00.000Z',
      '2022-11-28T21:00:00.000Z',
      '2022-11-29T21:00:00.000Z',
      '2022-11-30T21:00:00.000Z',
    ],
    expected: ['2022-11-27', '2022-11-28', '2022-11-29', '2022-11-30'],
  },
])(
  'Testing date objects to be converted in YYYY-MM-DD formated strings',
  ({ array, expected }) => {
    it('Should test array of date objects become array string', function () {
      const arrayStrToDates = array.map((date) => new Date(date));
      expect(DateFormator.formatDates(arrayStrToDates)).toEqual(expected);
    });
  },
);
