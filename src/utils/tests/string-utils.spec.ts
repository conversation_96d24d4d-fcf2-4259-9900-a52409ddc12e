import { Separators, StringUtils } from '../string.utils';

describe.each([
  { input: 'firstName', expected: 'first_name' },
  { input: 'lastName', expected: 'last_name' },
  { input: 'nomComplet', expected: 'nom_complet' },
])('Test camelCase to snake case', ({ input, expected }) => {
  it(`Should transform ${input} to ${expected}`, () => {
    expect(
      StringUtils.camelCaseToSeparatorWith(Separators.underscore, input),
    ).toBe(expected);
  });
});

describe.each([
  { input: 'firstName', expected: 'first-name' },
  { input: 'lastName', expected: 'last-name' },
  { input: 'nomComplet', expected: 'nom-complet' },
])('Test camelCase to Kebab case', ({ input, expected }) => {
  it(`Should transform ${input} to ${expected}`, () => {
    expect(StringUtils.camelCaseToSeparatorWith(Separators.dash, input)).toBe(
      expected,
    );
  });
});

describe.each([
  { input: 'firstName', expected: 'first name' },
  { input: 'lastName', expected: 'last name' },
  { input: 'nomComplet', expected: 'nom complet' },
])('Test camelCase to space separated strings', ({ input, expected }) => {
  it(`Should transform ${input} to ${expected}`, () => {
    expect(StringUtils.camelCaseToSeparatorWith(Separators.space, input)).toBe(
      expected,
    );
  });
});

describe.each([
  { input: 'first name', expected: 'First name' },
  { input: 'last Name', expected: 'Last name' },
  { input: 'nom complet', expected: 'Nom complet' },
])(
  'Test capitalize first letter of the first word only',
  ({ input, expected }) => {
    it(`Should transform ${input} to ${expected}`, () => {
      expect(StringUtils.capitalize(input, true)).toBe(expected);
    });
  },
);

describe.each([
  { input: 'first name', expected: 'First Name' },
  { input: 'last Name', expected: 'Last Name' },
  { input: 'nom complet', expected: 'Nom Complet' },
])('Test capitalize all first letter', ({ input, expected }) => {
  it(`Should transform ${input} to ${expected}`, () => {
    expect(StringUtils.capitalize(input)).toBe(expected);
  });
});
