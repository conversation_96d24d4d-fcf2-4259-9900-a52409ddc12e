import {
  UseInterceptors,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';

import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { plainToClass } from 'class-transformer';

// This means any class
interface ClassConstructor {
  // eslint-disable-next-line
  new (...args: any[]): {};
}
// Our custom decorator
export function Serialize(dto: ClassConstructor) {
  return UseInterceptors(new SerializeInterceptor(dto));
}

export class SerializeInterceptor implements NestInterceptor {
  // eslint-disable-next-line
  constructor(private dto: any) {}

  // eslint-disable-next-line
  intercept(
    context: ExecutionContext,
    next: <PERSON><PERSON><PERSON><PERSON><any>,
  ): Observable<any> {
    //   Code something before router handler
    return next.handle().pipe(
      map((data: any) => {
        //   Something before sending the response
        return plainToClass(this.dto, data, {
          excludeExtraneousValues: true, //this property is the one that make sure that the excluded properties in the DTO are not sent
        });
      }),
    );
  }
}
