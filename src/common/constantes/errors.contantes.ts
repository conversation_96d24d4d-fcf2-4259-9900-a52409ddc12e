type IAMErrors =
  | 'ERR_PASS_FORMAT'
  | 'ERR_COUNTRY_CODE_REQUIRE_PHONE'
  | 'ERR_PHONE_REQUIRE_COUNTY_CODE'
  | 'ERR_UNUSED_PHONE_NUMBER'
  | 'ERR_USER_NOT_FOUND'
  | 'ERR_NO_PASS_AUTH_NEEDED'
  | 'ERR_PASS_NOT_MATCH'
  | 'ERR_INVALID_2FA_CODE'
  | 'ERR_REFRESH_TOKEN_INVALID'
  | 'ERR_ACCESS_TOKEN_INVALID'
  | 'ERR_ACCESS_DENIED'
  | 'ERR_PASS_CONFIRM_NOT_MATCH'
  | 'ERR_OLD_PASS_NOT_MATCH'
  | 'ERR_REFRESH_TOKEN_NOT_PROVIDED'
  | 'ERR_ACCESS_TOKEN_EXPIRED'
  | 'ERR_EMAIL_CONFIRM_NOT_MATCH'
  | 'ERR_EMAIL_IN_USE'
  | 'ERR_PHONE_NUMBER_ALREADY_IN_USE'
  | 'ERR_INVALID_COUNTRY_CODE'
  | 'ERR_ACCOUNT_NOT_VERIFIED'
  | 'ERR_INVALID_PHONE_NUMBER'
  | 'ERR_MISSING_TOKEN'
  | 'ERR_INVALID_CREDENTIALS'
  | 'ERR_OTP_INVALID'
  | 'ERR_OTP_EXPIRED';

type IAMErr = {
  [key in IAMErrors]: any; //Todo : Avoid using any
};

export const IAM_ERRORS: IAMErr = {
  ERR_PASS_FORMAT:
    'Password should have at least 8 characters containing at least 1 uppercase, 1 lowercase,1 digital and 1 special character.',
  ERR_COUNTRY_CODE_REQUIRE_PHONE:
    'countryCode is required if phoneNumber is provided.',
  ERR_PHONE_REQUIRE_COUNTY_CODE:
    'phoneNumber is required if countryCode is provided.',
  ERR_UNUSED_PHONE_NUMBER: (dialCode: string, phoneNumber: string) =>
    `Le phone number ${dialCode} ${phoneNumber} is not used.`,
  ERR_USER_NOT_FOUND: 'User does not exist.',
  ERR_NO_PASS_AUTH_NEEDED:
    'This type of account does not require password authentication.',
  ERR_PASS_NOT_MATCH: 'Password not match',
  ERR_INVALID_2FA_CODE: 'Invalid 2FA code.',
  ERR_REFRESH_TOKEN_INVALID: 'Refresh token is invalid.',
  ERR_ACCESS_TOKEN_INVALID: 'token is invalid.',
  ERR_OTP_INVALID: 'Otp is invalid.',
  ERR_ACCESS_DENIED: 'Access denied.',
  ERR_PASS_CONFIRM_NOT_MATCH: "password and confirmation password don't match.",
  ERR_OLD_PASS_NOT_MATCH: 'Old password does not match.',
  ERR_REFRESH_TOKEN_NOT_PROVIDED: 'Reset token not provided.',
  ERR_ACCESS_TOKEN_EXPIRED: 'Expired token.',
  ERR_EMAIL_CONFIRM_NOT_MATCH:
    'New email and confirmation email are different.',
  ERR_EMAIL_IN_USE: 'The email is already taken',
  ERR_PHONE_NUMBER_ALREADY_IN_USE: 'The phone number is not available',
  ERR_INVALID_COUNTRY_CODE: 'The country code is not valid',
  ERR_ACCOUNT_NOT_VERIFIED: 'This account is not verified',
  ERR_INVALID_PHONE_NUMBER: 'Invalid phone number',
  ERR_INVALID_CREDENTIALS: 'Invalid credentials',
  ERR_MISSING_TOKEN: 'Missing token',
  ERR_OTP_EXPIRED: 'Expired Otp.',
};

// 2.2. Messages

type IAMServiceMessages =
  | 'ACCOUNT_ALREADY_VERIFIED'
  | 'RESET_PASSWORD_SUCCESS'
  | 'ACCOUNT_CREATED'
  | 'PASSWORD_GENERATED_AND_SENT';
type IamServiceMessage = {
  [key in IAMServiceMessages];
};

export const IAM_SERVICE_MESSAGES: IamServiceMessage = {
  ACCOUNT_ALREADY_VERIFIED: 'Your account is already verified.',
  RESET_PASSWORD_SUCCESS: 'Password succefuly reset',
  ACCOUNT_CREATED: 'Account created',
  PASSWORD_GENERATED_AND_SENT: 'Mot de passe généré et envoyé avec succès',
};

type MAILServiceMessages = 'EMAIL_VERIFIED' | 'EMAIL_SEND';
type MailServiceMessage = {
  [key in MAILServiceMessages];
};

export const MAIL_SERVICE_MESSAGES: MailServiceMessage = {
  EMAIL_VERIFIED: 'Your Email is  verified.',
  EMAIL_SEND: 'Email send !',
};

type TutorMessages =
  | 'MAKE_TUTOR'
  | 'UPDATE_PROFILE'
  | 'NOT_TUTOR'
  | 'VIDEO_NOT_FOUND'
  | 'COURSE_NOT_FOUND'
  | 'INVALID_RANGE_TIME'
  | 'SCHEDULE_NOT_FOUND'
  | 'MISSING_ARGUMENTS'
  | 'FAILED_INSERT'
  | 'TUTOR_NOT_FOUND'
  | 'COURSE_ADD_SUCCEFULLY'
  | 'YOU_ARE_TUTOR';
type TutorMessage = {
  [key in TutorMessages];
};

export const TUTOR_MESSAGES: TutorMessage = {
  MAKE_TUTOR: 'Your are now a Tutor',
  UPDATE_PROFILE: 'Your Profile is update succefully',
  NOT_TUTOR: 'Your are not a Tutor',
  COURSE_ADD_SUCCEFULLY: 'Course.s add succefully',
  VIDEO_NOT_FOUND: 'Video not found',
  COURSE_NOT_FOUND: 'This Course does not exist',
  INVALID_RANGE_TIME: 'Invalid range time',
  SCHEDULE_NOT_FOUND: 'Schedule not found',
  MISSING_ARGUMENTS: 'Missing argument ',
  FAILED_INSERT: 'Insert failed',
  TUTOR_NOT_FOUND: 'Tutor not found',
  YOU_ARE_TUTOR: 'Your are already Tutor',
};
type SessionMessages =
  | 'SESSION_NOT_FOUND'
  | 'NO_CLIENT_AUTH'
  | 'NO_CLIENT_TUTOR_AUTH'
  | 'RATING_NOT_FOUND'
  | 'SESSION_CREATED'
  | 'SESSION_DELETED'
  | 'SESSION_UPDATED'
  | 'IS_NOT_GROUPE_SESSION'
  | 'MAX_PARTICIPANTS'
  | 'GROUPE_SESSION_CREATED'
  | 'JOIN_SESSION'
  | 'DECLINE_SESSION'
  | 'DUPLICATE_BOOKING'
  | 'SESSION_DATE_IN_FUTURE'
  | 'ERROR_DATA';

type SessionMessage = {
  [key in SessionMessages]: string;
};

export const SESSION_MESSAGE: SessionMessage = {
  SESSION_NOT_FOUND: 'Session not found',
  RATING_NOT_FOUND: 'Rating not found',
  NO_CLIENT_AUTH: 'Session not found or doesnot belong to client',
  NO_CLIENT_TUTOR_AUTH:
    'only client or the owner of a session can add comments to it',
  SESSION_CREATED: 'Session create succefuly',
  SESSION_DELETED: 'Session delete succefuly',
  SESSION_UPDATED: 'Session update succefuly',
  IS_NOT_GROUPE_SESSION: 'Is not a groupe session',
  MAX_PARTICIPANTS:
    'the number of participants is greater than the maximum number specified',
  GROUPE_SESSION_CREATED: 'groupe session created successfully',
  JOIN_SESSION: 'You have joined this session',
  DECLINE_SESSION: 'You have declined this session',
  ERROR_DATA: 'Verify your data',
  DUPLICATE_BOOKING:
    'We noticed you re trying to book another session for the same tutor schedule. To avoid conflicts, please select a different time slot or contact the tutor directly',
  SESSION_DATE_IN_FUTURE:
    'You cannot schedule a session for a past date. Please choose a date from the future or present.',
};

type AnnouncementMessages = 'ANNOUNCEMENT_NOT_FOUND' | 'NO_USER_AUTH';

type AnnouncementMessage = {
  [key in AnnouncementMessages]: string;
};

export const ANNOUNCEMENT_MESSAGE: AnnouncementMessage = {
  ANNOUNCEMENT_NOT_FOUND: 'Announcement not found',
  NO_USER_AUTH: 'This announcement is not yours',
};

type EmailErrors = 'COULD_NOT_SEND_EMAIL' | 'INVALID_MAIL_TYPE';

type EmailError = {
  [key in EmailErrors]: string;
};

export const EMAIL_ERROR: EmailError = {
  COULD_NOT_SEND_EMAIL: 'Could not send email',
  INVALID_MAIL_TYPE: 'Invalid mail type',
};
export const GOOGLE_API_AUTH = 'https://www.googleapis.com/oauth2/v3/userinfo';
