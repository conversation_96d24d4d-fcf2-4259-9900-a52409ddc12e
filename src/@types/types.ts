export type status = 'active' | 'inactive';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class PaginationDto {
  @ApiPropertyOptional({
    description: 'Page number (1-based)',
    minimum: 1,
    default: 1,
    example: 1,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 10,
    example: 10,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'createdAt',
    type: String,
  })
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({
    description: 'Sort direction (asc or desc)',
    enum: ['asc', 'desc'],
    example: 'desc',
  })
  @IsOptional()
  sortDirection?: 'asc' | 'desc';

  /**
   * Calculates the number of items to skip for pagination
   */
  get skip(): number {
    return (this.page - 1) * this.limit;
  }

  /**
   * Generates Prisma orderBy clause based on sort fields
   */
  get orderBy(): Record<string, 'asc' | 'desc'> | undefined {
    if (!this.sortBy) return undefined;

    return {
      [this.sortBy]: this.sortDirection || 'asc',
    };
  }
}
