# TODO: Implémentation des modules manquants pour Compulse HR API

## 🔧 Problèmes à résoudre dans le schéma Prisma

### Relations dupliquées à nettoyer
- **Document model** : Supprimer les lignes dupliquées (lignes 105-106)
- **EmployeeData model** : Supprimer la ligne dupliquée `DocumentApproval DocumentApproval[]`
- **Contract model** : Supprimer les relations auto-générées en double
- **DocumentTemplate model** : Supprimer les relations auto-générées en double

### Relations manquantes à ajouter dans DocumentApproval
```prisma
model DocumentApproval {
  // ... champs existants
  
  // Relations corrigées
  approverId  String       @map("approver_id")
  approver    EmployeeData @relation("DocumentApprovalApprover", fields: [approverId], references: [id])
  requesterId String       @map("requester_id")
  requester   EmployeeData @relation("DocumentApprovalRequester", fields: [requesterId], references: [id])
}
```

## 📋 Modules à implémenter

### 1. Module Contrats (/src/modules/contracts)
**Fichiers à créer :**
- `contracts.module.ts`
- `contracts.controller.ts`
- `contracts.service.ts`
- `dto/contract.dto.ts`
- `dto/contract-template.dto.ts`

**Endpoints requis :**
- `GET /api/v1/contracts` - Liste des contrats
- `POST /api/v1/contracts` - Créer un contrat
- `GET /api/v1/contracts/:id` - Détails d'un contrat
- `PUT /api/v1/contracts/:id` - Modifier un contrat
- `DELETE /api/v1/contracts/:id` - Supprimer un contrat
- `POST /api/v1/contracts/:id/sign` - Signer un contrat
- `GET /api/v1/contracts/templates` - Templates de contrats
- `POST /api/v1/contracts/templates` - Créer un template

### 2. Module Documents (/src/modules/documents)
**Fichiers à créer :**
- `documents.module.ts`
- `documents.controller.ts`
- `documents.service.ts`
- `document-templates.controller.ts`
- `document-templates.service.ts`
- `document-approvals.controller.ts`
- `document-approvals.service.ts`
- `dto/document.dto.ts`
- `dto/document-template.dto.ts`
- `dto/document-approval.dto.ts`

**Endpoints requis :**
- `GET /api/v1/documents` - Bibliothèque de documents
- `POST /api/v1/documents` - Upload document
- `GET /api/v1/documents/templates` - Templates de documents
- `POST /api/v1/documents/templates` - Créer template
- `GET /api/v1/documents/approvals` - Demandes d'approbation
- `POST /api/v1/documents/:id/approve` - Approuver document
- `POST /api/v1/documents/:id/reject` - Rejeter document

### 3. Module Rapports (/src/modules/reports)
**Fichiers à créer :**
- `reports.module.ts`
- `reports.controller.ts`
- `reports.service.ts`
- `analytics.controller.ts`
- `analytics.service.ts`
- `dto/report.dto.ts`
- `dto/analytics.dto.ts`

**Endpoints requis :**
- `GET /api/v1/reports` - Liste des rapports
- `POST /api/v1/reports` - Créer un rapport
- `GET /api/v1/reports/:id/execute` - Exécuter un rapport
- `GET /api/v1/reports/hr` - Rapports RH prédéfinis
- `GET /api/v1/reports/analytics` - Analytics et métriques
- `GET /api/v1/reports/dashboard` - Données tableau de bord

### 4. Module Paramètres (/src/modules/settings)
**Fichiers à créer :**
- `settings.module.ts`
- `settings.controller.ts`
- `settings.service.ts`
- `dto/company-settings.dto.ts`

**Endpoints requis :**
- `GET /api/v1/settings` - Paramètres de l'entreprise
- `PUT /api/v1/settings` - Modifier paramètres
- `GET /api/v1/settings/payroll` - Configuration paie
- `PUT /api/v1/settings/payroll` - Modifier config paie
- `GET /api/v1/settings/tax` - Configuration fiscale
- `PUT /api/v1/settings/tax` - Modifier config fiscale

## 🌱 Seeds à créer (/prisma/seeds)

### Fichiers de seeds requis :
- `seed-companies.ts` - Entreprises RDC (Kinshasa, Lubumbashi, etc.)
- `seed-departments.ts` - Départements typiques
- `seed-positions.ts` - Postes de travail
- `seed-employees.ts` - Employés avec données réalistes RDC
- `seed-contracts.ts` - Contrats de travail variés
- `seed-contract-templates.ts` - Templates de contrats RDC
- `seed-document-templates.ts` - Templates de documents RH
- `seed-company-settings.ts` - Paramètres pour contexte RDC
- `seed-reports.ts` - Rapports prédéfinis
- `main-seed.ts` - Script principal d'exécution

### Contexte RDC à intégrer :
- **Monnaie** : Franc Congolais (CDF)
- **Timezone** : Africa/Kinshasa
- **Langue** : Français
- **Villes** : Kinshasa, Lubumbashi, Goma, Bukavu, Mbuji-Mayi
- **Secteurs** : Mining, Banking, Telecom, NGO, Government
- **Congés légaux** : 21 jours annuels, 98 jours maternité
- **Horaires** : 8h/jour, 5 jours/semaine

## 🔄 Migrations Prisma
Après correction du schéma, exécuter :
```bash
npx prisma migrate dev --name add-contracts-documents-reports-settings
npx prisma generate
```

## 📦 Dépendances à ajouter
```bash
npm install @nestjs/swagger class-validator class-transformer
npm install --save-dev @types/multer
```

## 🧪 Tests à créer
Pour chaque module, créer :
- Tests unitaires des services
- Tests d'intégration des contrôleurs
- Tests E2E des endpoints principaux

## 📝 Documentation API
Ajouter la documentation Swagger pour tous les nouveaux endpoints avec :
- Descriptions détaillées
- Exemples de requêtes/réponses
- Codes d'erreur possibles
- Schémas de validation

## 🔐 Sécurité et Permissions
Implémenter les guards et permissions pour :
- Accès aux contrats (RH uniquement)
- Approbation de documents (managers)
- Génération de rapports (admins)
- Modification des paramètres (super admin)

## 🌍 Internationalisation
Préparer les messages d'erreur et labels en français pour le contexte RDC.
