/*
  Warnings:

  - You are about to drop the column `department_name` on the `departments` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[departmentName,company_id]` on the table `departments` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "departments_department_name_key";

-- AlterTable
ALTER TABLE "departments" DROP COLUMN "department_name",
ADD COLUMN     "departmentName" TEXT;

-- AlterTable
ALTER TABLE "workdays" ALTER COLUMN "arrival_time" DROP NOT NULL,
ALTER COLUMN "departure_time" DROP NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "departments_departmentName_company_id_key" ON "departments"("departmentName", "company_id");
