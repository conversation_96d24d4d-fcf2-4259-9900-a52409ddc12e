/*
  Warnings:

  - You are about to drop the column `position` on the `employee_data` table. All the data in the column will be lost.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "TaskStatusEnum" AS ENUM ('TODO', 'IN_PROGRESS', 'REVIEW', 'COMPLETED', 'BLOCKED');

-- CreateEnum
CREATE TYPE "TaskPriorityEnum" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- C<PERSON><PERSON>num
CREATE TYPE "TaxPaymentFrequencyEnum" AS ENUM ('MONTHLY', 'QUARTERLY', 'ANNUALLY');

-- CreateEnum
CREATE TYPE "PayrollCycleEnum" AS ENUM ('MONTHLY', 'SEMI_MONTHLY', 'BI_WEEKLY', 'WEEKLY');

-- CreateEnum
CREATE TYPE "BonusCalculationEnum" AS ENUM ('PERCENTAGE_OF_SALARY', 'FIXED_AMOUNT', 'PERFORMANCE_BASED');

-- AlterTable
ALTER TABLE "employee_data" DROP COLUMN "position",
ADD COLUMN     "department_id" INTEGER,
ADD COLUMN     "position_id" INTEGER;

-- CreateTable
CREATE TABLE "company_tax_settings" (
    "settings_id" SERIAL NOT NULL,
    "company_id" INTEGER NOT NULL,
    "income_tax_rate" DOUBLE PRECISION NOT NULL,
    "social_security_rate" DOUBLE PRECISION NOT NULL,
    "unemployment_insurance_rate" DOUBLE PRECISION NOT NULL,
    "health_insurance_rate" DOUBLE PRECISION NOT NULL,
    "pension_contribution_rate" DOUBLE PRECISION NOT NULL,
    "income_tax_threshold" DOUBLE PRECISION,
    "social_security_threshold" DOUBLE PRECISION,
    "standard_deduction" DOUBLE PRECISION,
    "family_allowance" DOUBLE PRECISION,
    "tax_payment_frequency" "TaxPaymentFrequencyEnum" NOT NULL,

    CONSTRAINT "company_tax_settings_pkey" PRIMARY KEY ("settings_id")
);

-- CreateTable
CREATE TABLE "payroll_configuration" (
    "config_id" SERIAL NOT NULL,
    "company_id" INTEGER NOT NULL,
    "payroll_cycle" "PayrollCycleEnum" NOT NULL,
    "payment_day" INTEGER NOT NULL,
    "overtime_multiplier" DOUBLE PRECISION NOT NULL DEFAULT 1.5,
    "max_overtime_hours" DOUBLE PRECISION,
    "bonus_type" "BonusCalculationEnum",
    "performance_bonus_rate" DOUBLE PRECISION,
    "paid_time_off_accrual_rate" DOUBLE PRECISION,

    CONSTRAINT "payroll_configuration_pkey" PRIMARY KEY ("config_id")
);

-- CreateTable
CREATE TABLE "departments" (
    "department_id" SERIAL NOT NULL,
    "department_name" TEXT NOT NULL,
    "description" TEXT,
    "company_id" INTEGER NOT NULL,

    CONSTRAINT "departments_pkey" PRIMARY KEY ("department_id")
);

-- CreateTable
CREATE TABLE "positions" (
    "position_id" SERIAL NOT NULL,
    "position_title" TEXT NOT NULL,
    "description" TEXT,
    "department_id" INTEGER NOT NULL,
    "required_skills" TEXT[],

    CONSTRAINT "positions_pkey" PRIMARY KEY ("position_id")
);

-- CreateTable
CREATE TABLE "tasks" (
    "task_id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "due_date" TIMESTAMP(3) NOT NULL,
    "status" "TaskStatusEnum" NOT NULL DEFAULT 'TODO',
    "priority" "TaskPriorityEnum" NOT NULL DEFAULT 'MEDIUM',
    "assigned_to_id" INTEGER,
    "created_by_id" INTEGER NOT NULL,
    "start_date" TIMESTAMP(3),
    "completed_date" TIMESTAMP(3),
    "parent_task_id" INTEGER,

    CONSTRAINT "tasks_pkey" PRIMARY KEY ("task_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "company_tax_settings_company_id_key" ON "company_tax_settings"("company_id");

-- CreateIndex
CREATE UNIQUE INDEX "payroll_configuration_company_id_key" ON "payroll_configuration"("company_id");

-- CreateIndex
CREATE UNIQUE INDEX "departments_department_name_key" ON "departments"("department_name");

-- AddForeignKey
ALTER TABLE "company_tax_settings" ADD CONSTRAINT "company_tax_settings_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("company_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payroll_configuration" ADD CONSTRAINT "payroll_configuration_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("company_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "departments" ADD CONSTRAINT "departments_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("company_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "positions" ADD CONSTRAINT "positions_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("department_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_assigned_to_id_fkey" FOREIGN KEY ("assigned_to_id") REFERENCES "employee_data"("employeeId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_created_by_id_fkey" FOREIGN KEY ("created_by_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_parent_task_id_fkey" FOREIGN KEY ("parent_task_id") REFERENCES "tasks"("task_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_data" ADD CONSTRAINT "employee_data_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("department_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_data" ADD CONSTRAINT "employee_data_position_id_fkey" FOREIGN KEY ("position_id") REFERENCES "positions"("position_id") ON DELETE SET NULL ON UPDATE CASCADE;
