/*
  Warnings:

  - The primary key for the `address` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `address_id` on the `address` table. All the data in the column will be lost.
  - The primary key for the `application_response` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `application_response_id` on the `application_response` table. All the data in the column will be lost.
  - The primary key for the `applications` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `application_id` on the `applications` table. All the data in the column will be lost.
  - The primary key for the `companies` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `company_id` on the `companies` table. All the data in the column will be lost.
  - The primary key for the `company_tax_settings` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `settings_id` on the `company_tax_settings` table. All the data in the column will be lost.
  - The primary key for the `departments` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `department_id` on the `departments` table. All the data in the column will be lost.
  - The primary key for the `disciplinary_actions` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `action_id` on the `disciplinary_actions` table. All the data in the column will be lost.
  - You are about to drop the column `employeeId` on the `employee_data` table. All the data in the column will be lost.
  - The primary key for the `expenses_reports` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `report_id` on the `expenses_reports` table. All the data in the column will be lost.
  - The primary key for the `job_offer` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `job_id` on the `job_offer` table. All the data in the column will be lost.
  - The primary key for the `jobs_vacancy` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `job_vacancy_id` on the `jobs_vacancy` table. All the data in the column will be lost.
  - The primary key for the `payroll_configuration` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `config_id` on the `payroll_configuration` table. All the data in the column will be lost.
  - The primary key for the `payslip` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `payslip_id` on the `payslip` table. All the data in the column will be lost.
  - The primary key for the `performance_evaluations` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `evaluation_id` on the `performance_evaluations` table. All the data in the column will be lost.
  - The primary key for the `positions` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `position_id` on the `positions` table. All the data in the column will be lost.
  - The primary key for the `profiles` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `profile_id` on the `profiles` table. All the data in the column will be lost.
  - The primary key for the `salaries` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `salary_id` on the `salaries` table. All the data in the column will be lost.
  - The primary key for the `tasks` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `task_id` on the `tasks` table. All the data in the column will be lost.
  - The primary key for the `timesheets` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `timesheet_id` on the `timesheets` table. All the data in the column will be lost.
  - The primary key for the `users` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `workdays` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `work_day_id` on the `workdays` table. All the data in the column will be lost.
  - You are about to drop the `Benefit` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Complaint` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Departure` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Induction` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Leave` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Recommendation` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Training` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `issue_object` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `probation_period` table. If the table is not empty, all the data it contains will be lost.
  - The required column `id` was added to the `address` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `application_response` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `applications` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `companies` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `company_tax_settings` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `departments` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `disciplinary_actions` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `employee_data` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `expenses_reports` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `job_offer` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `jobs_vacancy` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `payroll_configuration` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `payslip` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `performance_evaluations` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `positions` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `profiles` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `salaries` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `tasks` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `timesheets` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `workdays` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.

*/
-- DropForeignKey
ALTER TABLE "Benefit" DROP CONSTRAINT "Benefit_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "Complaint" DROP CONSTRAINT "Complaint_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "Departure" DROP CONSTRAINT "Departure_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "Induction" DROP CONSTRAINT "Induction_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "Leave" DROP CONSTRAINT "Leave_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "Recommendation" DROP CONSTRAINT "Recommendation_receiver_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "Recommendation" DROP CONSTRAINT "Recommendation_sender_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "Training" DROP CONSTRAINT "Training_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "_SavedJobOffers" DROP CONSTRAINT "_SavedJobOffers_A_fkey";

-- DropForeignKey
ALTER TABLE "_SavedJobOffers" DROP CONSTRAINT "_SavedJobOffers_B_fkey";

-- DropForeignKey
ALTER TABLE "application_response" DROP CONSTRAINT "application_response_application_id_fkey";

-- DropForeignKey
ALTER TABLE "applications" DROP CONSTRAINT "applications_job_id_fkey";

-- DropForeignKey
ALTER TABLE "applications" DROP CONSTRAINT "applications_user_id_fkey";

-- DropForeignKey
ALTER TABLE "companies" DROP CONSTRAINT "companies_address_id_fkey";

-- DropForeignKey
ALTER TABLE "company_tax_settings" DROP CONSTRAINT "company_tax_settings_company_id_fkey";

-- DropForeignKey
ALTER TABLE "departments" DROP CONSTRAINT "departments_company_id_fkey";

-- DropForeignKey
ALTER TABLE "disciplinary_actions" DROP CONSTRAINT "disciplinary_actions_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "employee_data" DROP CONSTRAINT "employee_data_company_id_fkey";

-- DropForeignKey
ALTER TABLE "employee_data" DROP CONSTRAINT "employee_data_department_id_fkey";

-- DropForeignKey
ALTER TABLE "employee_data" DROP CONSTRAINT "employee_data_employeeId_fkey";

-- DropForeignKey
ALTER TABLE "employee_data" DROP CONSTRAINT "employee_data_position_id_fkey";

-- DropForeignKey
ALTER TABLE "expenses_reports" DROP CONSTRAINT "expenses_reports_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "issue_object" DROP CONSTRAINT "issue_object_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "job_offer" DROP CONSTRAINT "job_offer_company_id_fkey";

-- DropForeignKey
ALTER TABLE "jobs_vacancy" DROP CONSTRAINT "jobs_vacancy_company_id_fkey";

-- DropForeignKey
ALTER TABLE "payroll_configuration" DROP CONSTRAINT "payroll_configuration_company_id_fkey";

-- DropForeignKey
ALTER TABLE "payslip" DROP CONSTRAINT "payslip_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "performance_evaluations" DROP CONSTRAINT "performance_evaluations_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "positions" DROP CONSTRAINT "positions_department_id_fkey";

-- DropForeignKey
ALTER TABLE "probation_period" DROP CONSTRAINT "probation_period_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "profiles" DROP CONSTRAINT "profiles_address_id_fkey";

-- DropForeignKey
ALTER TABLE "profiles" DROP CONSTRAINT "profiles_user_id_fkey";

-- DropForeignKey
ALTER TABLE "salaries" DROP CONSTRAINT "salaries_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "tasks" DROP CONSTRAINT "tasks_assigned_to_id_fkey";

-- DropForeignKey
ALTER TABLE "tasks" DROP CONSTRAINT "tasks_created_by_id_fkey";

-- DropForeignKey
ALTER TABLE "tasks" DROP CONSTRAINT "tasks_parent_task_id_fkey";

-- DropForeignKey
ALTER TABLE "timesheets" DROP CONSTRAINT "timesheets_employee_id_fkey";

-- DropForeignKey
ALTER TABLE "workdays" DROP CONSTRAINT "workdays_timesheet_id_fkey";

-- DropIndex
DROP INDEX "employee_data_employeeId_key";

-- AlterTable
ALTER TABLE "_SavedJobOffers" ALTER COLUMN "A" SET DATA TYPE TEXT,
ALTER COLUMN "B" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "address" DROP CONSTRAINT "address_pkey",
DROP COLUMN "address_id",
ADD COLUMN     "id" TEXT NOT NULL,
ADD CONSTRAINT "address_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "application_response" DROP CONSTRAINT "application_response_pkey",
DROP COLUMN "application_response_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "application_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "application_response_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "applications" DROP CONSTRAINT "applications_pkey",
DROP COLUMN "application_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "job_id" SET DATA TYPE TEXT,
ALTER COLUMN "user_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "applications_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "companies" DROP CONSTRAINT "companies_pkey",
DROP COLUMN "company_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "address_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "companies_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "company_tax_settings" DROP CONSTRAINT "company_tax_settings_pkey",
DROP COLUMN "settings_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "company_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "company_tax_settings_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "departments" DROP CONSTRAINT "departments_pkey",
DROP COLUMN "department_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "company_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "departments_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "disciplinary_actions" DROP CONSTRAINT "disciplinary_actions_pkey",
DROP COLUMN "action_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "employee_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "disciplinary_actions_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "employee_data" DROP COLUMN "employeeId",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "company_id" SET DATA TYPE TEXT,
ALTER COLUMN "department_id" SET DATA TYPE TEXT,
ALTER COLUMN "position_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "employee_data_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "expenses_reports" DROP CONSTRAINT "expenses_reports_pkey",
DROP COLUMN "report_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "employee_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "expenses_reports_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "job_offer" DROP CONSTRAINT "job_offer_pkey",
DROP COLUMN "job_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "company_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "job_offer_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "jobs_vacancy" DROP CONSTRAINT "jobs_vacancy_pkey",
DROP COLUMN "job_vacancy_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "company_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "jobs_vacancy_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "payroll_configuration" DROP CONSTRAINT "payroll_configuration_pkey",
DROP COLUMN "config_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "company_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "payroll_configuration_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "payslip" DROP CONSTRAINT "payslip_pkey",
DROP COLUMN "payslip_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "employee_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "payslip_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "performance_evaluations" DROP CONSTRAINT "performance_evaluations_pkey",
DROP COLUMN "evaluation_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "employee_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "performance_evaluations_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "positions" DROP CONSTRAINT "positions_pkey",
DROP COLUMN "position_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "department_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "positions_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "profiles" DROP CONSTRAINT "profiles_pkey",
DROP COLUMN "profile_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "address_id" SET DATA TYPE TEXT,
ALTER COLUMN "user_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "salaries" DROP CONSTRAINT "salaries_pkey",
DROP COLUMN "salary_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "employee_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "salaries_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "tasks" DROP CONSTRAINT "tasks_pkey",
DROP COLUMN "task_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "assigned_to_id" SET DATA TYPE TEXT,
ALTER COLUMN "created_by_id" SET DATA TYPE TEXT,
ALTER COLUMN "parent_task_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "tasks_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "timesheets" DROP CONSTRAINT "timesheets_pkey",
DROP COLUMN "timesheet_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "employee_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "timesheets_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "users" DROP CONSTRAINT "users_pkey",
ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");
DROP SEQUENCE "users_id_seq";

-- AlterTable
ALTER TABLE "workdays" DROP CONSTRAINT "workdays_pkey",
DROP COLUMN "work_day_id",
ADD COLUMN     "id" TEXT NOT NULL,
ALTER COLUMN "timesheet_id" SET DATA TYPE TEXT,
ADD CONSTRAINT "workdays_pkey" PRIMARY KEY ("id");

-- DropTable
DROP TABLE "Benefit";

-- DropTable
DROP TABLE "Complaint";

-- DropTable
DROP TABLE "Departure";

-- DropTable
DROP TABLE "Induction";

-- DropTable
DROP TABLE "Leave";

-- DropTable
DROP TABLE "Recommendation";

-- DropTable
DROP TABLE "Training";

-- DropTable
DROP TABLE "issue_object";

-- DropTable
DROP TABLE "probation_period";

-- CreateTable
CREATE TABLE "documents" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "description" TEXT,
    "uploaded_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "user_id" TEXT,
    "company_id" TEXT,
    "employee_id" TEXT,

    CONSTRAINT "documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "leaves" (
    "id" TEXT NOT NULL,
    "leave_type" "LeaveTypeEnum" NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "status" "LeaveStatusEnum" NOT NULL,
    "employee_id" TEXT NOT NULL,

    CONSTRAINT "leaves_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "trainings" (
    "id" TEXT NOT NULL,
    "training_name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "employee_id" TEXT NOT NULL,

    CONSTRAINT "trainings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "issue_objects" (
    "id" TEXT NOT NULL,
    "object_name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "issued_date" TIMESTAMP(3) NOT NULL,
    "return_date" TIMESTAMP(3) NOT NULL,
    "employee_id" TEXT NOT NULL,

    CONSTRAINT "issue_objects_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "complaints" (
    "id" TEXT NOT NULL,
    "complaint_date" TIMESTAMP(3) NOT NULL,
    "description" TEXT NOT NULL,
    "resolution_date" TIMESTAMP(3) NOT NULL,
    "resolution_details" TEXT NOT NULL,
    "employee_id" TEXT NOT NULL,

    CONSTRAINT "complaints_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "benefits" (
    "id" TEXT NOT NULL,
    "benefit_type" "BenefitTypeEnum" NOT NULL,
    "description" TEXT NOT NULL,
    "authorized_amount" DOUBLE PRECISION NOT NULL,
    "employee_id" TEXT NOT NULL,

    CONSTRAINT "benefits_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "probation_periods" (
    "id" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "status" "ProbationStatusEnum" NOT NULL,
    "employee_id" TEXT NOT NULL,

    CONSTRAINT "probation_periods_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "recommendations" (
    "id" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "recommendation_date" TIMESTAMP(3) NOT NULL,
    "sender_employee_id" TEXT NOT NULL,
    "receiver_employee_id" TEXT NOT NULL,

    CONSTRAINT "recommendations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "departures" (
    "id" TEXT NOT NULL,
    "departure_date" TIMESTAMP(3) NOT NULL,
    "reason" TEXT NOT NULL,
    "employee_id" TEXT NOT NULL,

    CONSTRAINT "departures_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "inductions" (
    "id" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "trainer" TEXT NOT NULL,
    "feedback" TEXT NOT NULL,
    "employee_id" TEXT NOT NULL,

    CONSTRAINT "inductions_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "profiles" ADD CONSTRAINT "profiles_address_id_fkey" FOREIGN KEY ("address_id") REFERENCES "address"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "profiles" ADD CONSTRAINT "profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "companies" ADD CONSTRAINT "companies_address_id_fkey" FOREIGN KEY ("address_id") REFERENCES "address"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "documents" ADD CONSTRAINT "documents_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "documents" ADD CONSTRAINT "documents_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "documents" ADD CONSTRAINT "documents_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company_tax_settings" ADD CONSTRAINT "company_tax_settings_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payroll_configuration" ADD CONSTRAINT "payroll_configuration_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "job_offer" ADD CONSTRAINT "job_offer_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "applications" ADD CONSTRAINT "applications_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "job_offer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "applications" ADD CONSTRAINT "applications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "application_response" ADD CONSTRAINT "application_response_application_id_fkey" FOREIGN KEY ("application_id") REFERENCES "applications"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "departments" ADD CONSTRAINT "departments_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "positions" ADD CONSTRAINT "positions_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_assigned_to_id_fkey" FOREIGN KEY ("assigned_to_id") REFERENCES "employee_data"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_created_by_id_fkey" FOREIGN KEY ("created_by_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_parent_task_id_fkey" FOREIGN KEY ("parent_task_id") REFERENCES "tasks"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_data" ADD CONSTRAINT "employee_data_id_fkey" FOREIGN KEY ("id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_data" ADD CONSTRAINT "employee_data_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_data" ADD CONSTRAINT "employee_data_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_data" ADD CONSTRAINT "employee_data_position_id_fkey" FOREIGN KEY ("position_id") REFERENCES "positions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payslip" ADD CONSTRAINT "payslip_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "salaries" ADD CONSTRAINT "salaries_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leaves" ADD CONSTRAINT "leaves_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "timesheets" ADD CONSTRAINT "timesheets_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workdays" ADD CONSTRAINT "workdays_timesheet_id_fkey" FOREIGN KEY ("timesheet_id") REFERENCES "timesheets"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "performance_evaluations" ADD CONSTRAINT "performance_evaluations_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "trainings" ADD CONSTRAINT "trainings_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "issue_objects" ADD CONSTRAINT "issue_objects_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "disciplinary_actions" ADD CONSTRAINT "disciplinary_actions_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "complaints" ADD CONSTRAINT "complaints_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "benefits" ADD CONSTRAINT "benefits_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "expenses_reports" ADD CONSTRAINT "expenses_reports_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jobs_vacancy" ADD CONSTRAINT "jobs_vacancy_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "probation_periods" ADD CONSTRAINT "probation_periods_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "recommendations" ADD CONSTRAINT "recommendations_sender_employee_id_fkey" FOREIGN KEY ("sender_employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "recommendations" ADD CONSTRAINT "recommendations_receiver_employee_id_fkey" FOREIGN KEY ("receiver_employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "departures" ADD CONSTRAINT "departures_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inductions" ADD CONSTRAINT "inductions_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SavedJobOffers" ADD CONSTRAINT "_SavedJobOffers_A_fkey" FOREIGN KEY ("A") REFERENCES "job_offer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SavedJobOffers" ADD CONSTRAINT "_SavedJobOffers_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
