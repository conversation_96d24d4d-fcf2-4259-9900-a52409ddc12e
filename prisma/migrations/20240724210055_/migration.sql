/*
  Warnings:

  - You are about to drop the column `deductions` on the `payslip` table. All the data in the column will be lost.
  - You are about to drop the column `amount` on the `salaries` table. All the data in the column will be lost.
  - You are about to drop the `Timesheet` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `other_deductions` to the `payslip` table without a default value. This is not possible if the table is not empty.
  - Added the required column `social_security` to the `payslip` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tax_deductions` to the `payslip` table without a default value. This is not possible if the table is not empty.
  - Added the required column `base_salary` to the `salaries` table without a default value. This is not possible if the table is not empty.
  - Added the required column `bonus` to the `salaries` table without a default value. This is not possible if the table is not empty.
  - Added the required column `housing_allowance` to the `salaries` table without a default value. This is not possible if the table is not empty.
  - Added the required column `overtime_hours` to the `salaries` table without a default value. This is not possible if the table is not empty.
  - Added the required column `overtime_rate` to the `salaries` table without a default value. This is not possible if the table is not empty.
  - Added the required column `transport_allowance` to the `salaries` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Timesheet" DROP CONSTRAINT "Timesheet_employee_id_fkey";

-- AlterTable
ALTER TABLE "payslip" DROP COLUMN "deductions",
ADD COLUMN     "other_deductions" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "social_security" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "tax_deductions" DOUBLE PRECISION NOT NULL;

-- AlterTable
ALTER TABLE "salaries" DROP COLUMN "amount",
ADD COLUMN     "base_salary" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "bonus" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "housing_allowance" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "overtime_hours" INTEGER NOT NULL,
ADD COLUMN     "overtime_rate" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "transport_allowance" DOUBLE PRECISION NOT NULL;

-- DropTable
DROP TABLE "Timesheet";

-- CreateTable
CREATE TABLE "timesheets" (
    "timesheet_id" SERIAL NOT NULL,
    "period_start" TIMESTAMP(3) NOT NULL,
    "period_end" TIMESTAMP(3) NOT NULL,
    "total_regular_hours" DOUBLE PRECISION NOT NULL,
    "total_overtime_hours" DOUBLE PRECISION NOT NULL,
    "total_undertime_hours" DOUBLE PRECISION NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "timesheets_pkey" PRIMARY KEY ("timesheet_id")
);

-- CreateTable
CREATE TABLE "workdays" (
    "work_day_id" SERIAL NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "arrival_time" TIMESTAMP(3) NOT NULL,
    "departure_time" TIMESTAMP(3) NOT NULL,
    "regular_hours" DOUBLE PRECISION NOT NULL,
    "overtime_hours" DOUBLE PRECISION NOT NULL,
    "undertime_hours" DOUBLE PRECISION NOT NULL,
    "timesheet_id" INTEGER NOT NULL,

    CONSTRAINT "workdays_pkey" PRIMARY KEY ("work_day_id")
);

-- AddForeignKey
ALTER TABLE "timesheets" ADD CONSTRAINT "timesheets_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workdays" ADD CONSTRAINT "workdays_timesheet_id_fkey" FOREIGN KEY ("timesheet_id") REFERENCES "timesheets"("timesheet_id") ON DELETE RESTRICT ON UPDATE CASCADE;
