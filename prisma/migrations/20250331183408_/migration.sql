/*
  Warnings:

  - The values [UNPAID] on the enum `LeaveStatusEnum` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "LeaveStatusEnum_new" AS ENUM ('REQUESTED', 'APPROVED', 'REJECTED');
ALTER TABLE "leaves" ALTER COLUMN "status" TYPE "LeaveStatusEnum_new" USING ("status"::text::"LeaveStatusEnum_new");
ALTER TYPE "LeaveStatusEnum" RENAME TO "LeaveStatusEnum_old";
ALTER TYPE "LeaveStatusEnum_new" RENAME TO "LeaveStatusEnum";
DROP TYPE "LeaveStatusEnum_old";
COMMIT;

-- AlterEnum
ALTER TYPE "LeaveTypeEnum" ADD VALUE 'UNPAID';
