/*
  Warnings:

  - Added the required column `location` to the `job_offer` table without a default value. This is not possible if the table is not empty.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "JobOfferStatusEnum" AS ENUM ('DRAFT', 'ACTIVE', 'EXPIRED', 'FILLED', 'CANCELLED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "ContractTypeEnum" AS ENUM ('FULL_TIME', 'PART_TIME', 'TEMPORARY', 'CONTRACT', 'INTERNSHIP', 'REMOTE', 'HYBRID', 'ON_SITE', 'SEASONAL', 'FREELANCE');

-- AlterTable
ALTER TABLE "job_offer" ADD COLUMN     "contract_types" "ContractTypeEnum"[],
ADD COLUMN     "department_id" TEXT,
ADD COLUMN     "location" TEXT NOT NULL,
ADD COLUMN     "max_salary" DOUBLE PRECISION,
ADD COLUMN     "min_salary" DOUBLE PRECISION,
ADD COLUMN     "position_id" TEXT,
ADD COLUMN     "required_skills" TEXT[],
ADD COLUMN     "status" "JobOfferStatusEnum" NOT NULL DEFAULT 'ACTIVE';

-- AddForeignKey
ALTER TABLE "job_offer" ADD CONSTRAINT "job_offer_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "job_offer" ADD CONSTRAINT "job_offer_position_id_fkey" FOREIGN KEY ("position_id") REFERENCES "positions"("id") ON DELETE SET NULL ON UPDATE CASCADE;
