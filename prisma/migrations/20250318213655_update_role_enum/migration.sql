/*
  Warnings:

  - The values [INDIVIDUAL] on the enum `RoleEnum` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "RoleEnum_new" AS ENUM ('SUPER_ADMIN', 'ADMIN_HR', 'ADMIN_RECRUITMENT', 'OWNER', '<PERSON>MPLOYEE');
ALTER TABLE "users" ALTER COLUMN "role" TYPE "RoleEnum_new" USING ("role"::text::"RoleEnum_new");
ALTER TYPE "RoleEnum" RENAME TO "RoleEnum_old";
ALTER TYPE "RoleEnum_new" RENAME TO "RoleEnum";
DROP TYPE "RoleEnum_old";
COMMIT;
