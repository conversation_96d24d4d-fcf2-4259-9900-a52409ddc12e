-- CreateEnum
CREATE TYPE "RoleEnum" AS ENUM ('SUPER_ADMIN', 'ADMIN_HR', 'ADMIN_RECRUITMENT', 'OWNER', 'INDIVIDUAL');

-- CreateEnum
CREATE TYPE "PermissionEnum" AS ENUM ('CAN_CREATE', 'CAN_EDIT', 'CAN_DELETE', 'CAN_READ');

-- <PERSON>reate<PERSON>num
CREATE TYPE "LeaveTypeEnum" AS ENUM ('ANNUAL', 'SICK', 'MATERNITY');

-- CreateEnum
CREATE TYPE "LeaveStatusEnum" AS ENUM ('REQUESTED', 'APPROVED', 'REJECTED');

-- C<PERSON><PERSON>num
CREATE TYPE "BenefitTypeEnum" AS ENUM ('INSURANCE', 'VEHICLE');

-- CreateEnum
CREATE TYPE "ApplicationStatusEnum" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "ProbationStatusEnum" AS ENUM ('IN_PROGRESS', 'CONFIRMED', 'EXTENDED');

-- CreateTable
CREATE TABLE "users" (
    "id" SERIAL NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "google_id" TEXT,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "verification_otp" TEXT,
    "role" "RoleEnum" NOT NULL,
    "password_reset_otp" JSONB NOT NULL DEFAULT '{"otp": null, "expiresAt": null}',

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "profiles" (
    "profile_id" SERIAL NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "avatar" TEXT,
    "phone_number" TEXT,
    "birth_date" TIMESTAMP(3),
    "address_id" INTEGER,
    "user_id" INTEGER NOT NULL,

    CONSTRAINT "profiles_pkey" PRIMARY KEY ("profile_id")
);

-- CreateTable
CREATE TABLE "companies" (
    "company_id" SERIAL NOT NULL,
    "company_name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone_numbers" TEXT[],
    "web_site" TEXT,
    "logo" TEXT,
    "address_id" INTEGER NOT NULL,

    CONSTRAINT "companies_pkey" PRIMARY KEY ("company_id")
);

-- CreateTable
CREATE TABLE "address" (
    "address_id" SERIAL NOT NULL,
    "street" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "postal_code" TEXT NOT NULL,
    "country" TEXT NOT NULL,

    CONSTRAINT "address_pkey" PRIMARY KEY ("address_id")
);

-- CreateTable
CREATE TABLE "job_offer" (
    "job_id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "publish_date" TIMESTAMP(3) NOT NULL,
    "expiration_date" TIMESTAMP(3) NOT NULL,
    "company_id" INTEGER NOT NULL,

    CONSTRAINT "job_offer_pkey" PRIMARY KEY ("job_id")
);

-- CreateTable
CREATE TABLE "applications" (
    "application_id" SERIAL NOT NULL,
    "application_date" TIMESTAMP(3) NOT NULL,
    "status" "ApplicationStatusEnum" NOT NULL,
    "job_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "cover_letter" TEXT NOT NULL,
    "resume" TEXT NOT NULL,
    "references" TEXT,
    "additional_documents" TEXT[],
    "preferred_start_date" TIMESTAMP(3),
    "current_employment_status" TEXT,
    "desired_salary" DOUBLE PRECISION,

    CONSTRAINT "applications_pkey" PRIMARY KEY ("application_id")
);

-- CreateTable
CREATE TABLE "application_response" (
    "application_response_id" SERIAL NOT NULL,
    "application_response_date" TIMESTAMP(3) NOT NULL,
    "content" TEXT NOT NULL,
    "application_id" INTEGER NOT NULL,

    CONSTRAINT "application_response_pkey" PRIMARY KEY ("application_response_id")
);

-- CreateTable
CREATE TABLE "employee_data" (
    "employeeId" INTEGER NOT NULL,
    "hire_date" TIMESTAMP(3) NOT NULL,
    "position" TEXT NOT NULL,
    "company_id" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "salaries" (
    "salary_id" SERIAL NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "effective_date" TIMESTAMP(3) NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "salaries_pkey" PRIMARY KEY ("salary_id")
);

-- CreateTable
CREATE TABLE "payslip" (
    "payslip_id" SERIAL NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "gross_salary" DOUBLE PRECISION NOT NULL,
    "deductions" DOUBLE PRECISION NOT NULL,
    "net_salary" DOUBLE PRECISION NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "payslip_pkey" PRIMARY KEY ("payslip_id")
);

-- CreateTable
CREATE TABLE "Leave" (
    "leave_id" SERIAL NOT NULL,
    "leave_type" "LeaveTypeEnum" NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "status" "LeaveStatusEnum" NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "Leave_pkey" PRIMARY KEY ("leave_id")
);

-- CreateTable
CREATE TABLE "Timesheet" (
    "timesheet_id" SERIAL NOT NULL,
    "period_start" TIMESTAMP(3) NOT NULL,
    "period_end" TIMESTAMP(3) NOT NULL,
    "regular_hours" INTEGER NOT NULL,
    "overtime_hours" INTEGER NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "Timesheet_pkey" PRIMARY KEY ("timesheet_id")
);

-- CreateTable
CREATE TABLE "performance_evaluations" (
    "evaluation_id" SERIAL NOT NULL,
    "period_start" TIMESTAMP(3) NOT NULL,
    "period_end" TIMESTAMP(3) NOT NULL,
    "goals" TEXT NOT NULL,
    "self_evaluation" TEXT NOT NULL,
    "manager_evaluation" TEXT NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "performance_evaluations_pkey" PRIMARY KEY ("evaluation_id")
);

-- CreateTable
CREATE TABLE "Training" (
    "training_id" SERIAL NOT NULL,
    "training_name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "Training_pkey" PRIMARY KEY ("training_id")
);

-- CreateTable
CREATE TABLE "issue_object" (
    "object_id" SERIAL NOT NULL,
    "object_name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "issued_date" TIMESTAMP(3) NOT NULL,
    "return_date" TIMESTAMP(3) NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "issue_object_pkey" PRIMARY KEY ("object_id")
);

-- CreateTable
CREATE TABLE "disciplinary_actions" (
    "action_id" SERIAL NOT NULL,
    "action_date" TIMESTAMP(3) NOT NULL,
    "description" TEXT NOT NULL,
    "action_taken" TEXT NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "disciplinary_actions_pkey" PRIMARY KEY ("action_id")
);

-- CreateTable
CREATE TABLE "Complaint" (
    "complaint_id" SERIAL NOT NULL,
    "complaint_date" TIMESTAMP(3) NOT NULL,
    "description" TEXT NOT NULL,
    "resolution_date" TIMESTAMP(3) NOT NULL,
    "resolution_details" TEXT NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "Complaint_pkey" PRIMARY KEY ("complaint_id")
);

-- CreateTable
CREATE TABLE "Benefit" (
    "benefit_id" SERIAL NOT NULL,
    "benefit_type" "BenefitTypeEnum" NOT NULL,
    "description" TEXT NOT NULL,
    "authorized_amount" DOUBLE PRECISION NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "Benefit_pkey" PRIMARY KEY ("benefit_id")
);

-- CreateTable
CREATE TABLE "expenses_reports" (
    "report_id" SERIAL NOT NULL,
    "description" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "report_date" TIMESTAMP(3) NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "expenses_reports_pkey" PRIMARY KEY ("report_id")
);

-- CreateTable
CREATE TABLE "jobs_vacancy" (
    "job_vacancy_id" SERIAL NOT NULL,
    "job_title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "opening_date" TIMESTAMP(3) NOT NULL,
    "closing_date" TIMESTAMP(3) NOT NULL,
    "company_id" INTEGER NOT NULL,

    CONSTRAINT "jobs_vacancy_pkey" PRIMARY KEY ("job_vacancy_id")
);

-- CreateTable
CREATE TABLE "probation_period" (
    "probation_period_id" SERIAL NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "status" "ProbationStatusEnum" NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "probation_period_pkey" PRIMARY KEY ("probation_period_id")
);

-- CreateTable
CREATE TABLE "Recommendation" (
    "recommendation_id" SERIAL NOT NULL,
    "description" TEXT NOT NULL,
    "recommendation_date" TIMESTAMP(3) NOT NULL,
    "sender_employee_id" INTEGER NOT NULL,
    "receiver_employee_id" INTEGER NOT NULL,

    CONSTRAINT "Recommendation_pkey" PRIMARY KEY ("recommendation_id")
);

-- CreateTable
CREATE TABLE "Departure" (
    "departure_id" SERIAL NOT NULL,
    "departure_date" TIMESTAMP(3) NOT NULL,
    "reason" TEXT NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "Departure_pkey" PRIMARY KEY ("departure_id")
);

-- CreateTable
CREATE TABLE "Induction" (
    "induction_id" SERIAL NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "trainer" TEXT NOT NULL,
    "feedback" TEXT NOT NULL,
    "employee_id" INTEGER NOT NULL,

    CONSTRAINT "Induction_pkey" PRIMARY KEY ("induction_id")
);

-- CreateTable
CREATE TABLE "_SavedJobOffers" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "users_id_key" ON "users"("id");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "profiles_address_id_key" ON "profiles"("address_id");

-- CreateIndex
CREATE UNIQUE INDEX "profiles_user_id_key" ON "profiles"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "companies_company_name_key" ON "companies"("company_name");

-- CreateIndex
CREATE UNIQUE INDEX "companies_email_key" ON "companies"("email");

-- CreateIndex
CREATE UNIQUE INDEX "companies_address_id_key" ON "companies"("address_id");

-- CreateIndex
CREATE UNIQUE INDEX "employee_data_employeeId_key" ON "employee_data"("employeeId");

-- CreateIndex
CREATE UNIQUE INDEX "_SavedJobOffers_AB_unique" ON "_SavedJobOffers"("A", "B");

-- CreateIndex
CREATE INDEX "_SavedJobOffers_B_index" ON "_SavedJobOffers"("B");

-- AddForeignKey
ALTER TABLE "profiles" ADD CONSTRAINT "profiles_address_id_fkey" FOREIGN KEY ("address_id") REFERENCES "address"("address_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "profiles" ADD CONSTRAINT "profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "companies" ADD CONSTRAINT "companies_address_id_fkey" FOREIGN KEY ("address_id") REFERENCES "address"("address_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "job_offer" ADD CONSTRAINT "job_offer_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("company_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "applications" ADD CONSTRAINT "applications_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "job_offer"("job_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "applications" ADD CONSTRAINT "applications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "application_response" ADD CONSTRAINT "application_response_application_id_fkey" FOREIGN KEY ("application_id") REFERENCES "applications"("application_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_data" ADD CONSTRAINT "employee_data_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_data" ADD CONSTRAINT "employee_data_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("company_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "salaries" ADD CONSTRAINT "salaries_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payslip" ADD CONSTRAINT "payslip_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Leave" ADD CONSTRAINT "Leave_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Timesheet" ADD CONSTRAINT "Timesheet_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "performance_evaluations" ADD CONSTRAINT "performance_evaluations_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Training" ADD CONSTRAINT "Training_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "issue_object" ADD CONSTRAINT "issue_object_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "disciplinary_actions" ADD CONSTRAINT "disciplinary_actions_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Complaint" ADD CONSTRAINT "Complaint_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Benefit" ADD CONSTRAINT "Benefit_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "expenses_reports" ADD CONSTRAINT "expenses_reports_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jobs_vacancy" ADD CONSTRAINT "jobs_vacancy_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("company_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "probation_period" ADD CONSTRAINT "probation_period_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recommendation" ADD CONSTRAINT "Recommendation_sender_employee_id_fkey" FOREIGN KEY ("sender_employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recommendation" ADD CONSTRAINT "Recommendation_receiver_employee_id_fkey" FOREIGN KEY ("receiver_employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Departure" ADD CONSTRAINT "Departure_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Induction" ADD CONSTRAINT "Induction_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee_data"("employeeId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SavedJobOffers" ADD CONSTRAINT "_SavedJobOffers_A_fkey" FOREIGN KEY ("A") REFERENCES "job_offer"("job_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SavedJobOffers" ADD CONSTRAINT "_SavedJobOffers_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
