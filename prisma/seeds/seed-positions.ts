import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedPositions(departments: any[]) {
  console.log('💼 Seeding positions...');

  const positionsByDepartment = {
    'Direction Générale': [
      { title: 'Directeur Général', description: 'Direction exécutive', level: 'EXECUTIVE', salaryMin: 8000000, salaryMax: 15000000 },
      { title: 'Directeur Général Adjoint', description: 'Direction adjointe', level: 'EXECUTIVE', salaryMin: 6000000, salaryMax: 10000000 },
      { title: 'Secrétaire de Direction', description: 'Assistance de direction', level: 'SENIOR', salaryMin: 1500000, salaryMax: 2500000 },
      { title: 'Conseiller Stratégique', description: 'Conseil stratégique', level: 'SENIOR', salaryMin: 3000000, salaryMax: 5000000 },
    ],
    'Ressources Humaines': [
      { title: 'Directeur RH', description: 'Direction des ressources humaines', level: 'EXECUTIVE', salaryMin: 4000000, salaryMax: 7000000 },
      { title: 'Responsable Recrutement', description: 'Gestion du recrutement', level: 'SENIOR', salaryMin: 2000000, salaryMax: 3500000 },
      { title: 'Gestionnaire RH', description: 'Gestion administrative RH', level: 'MID', salaryMin: 1200000, salaryMax: 2000000 },
      { title: 'Assistant RH', description: 'Assistance ressources humaines', level: 'JUNIOR', salaryMin: 800000, salaryMax: 1200000 },
      { title: 'Responsable Formation', description: 'Formation et développement', level: 'SENIOR', salaryMin: 1800000, salaryMax: 3000000 },
    ],
    'Informatique': [
      { title: 'Directeur IT', description: 'Direction informatique', level: 'EXECUTIVE', salaryMin: 5000000, salaryMax: 8000000 },
      { title: 'Architecte Système', description: 'Architecture des systèmes', level: 'SENIOR', salaryMin: 3500000, salaryMax: 5500000 },
      { title: 'Développeur Senior', description: 'Développement logiciel senior', level: 'SENIOR', salaryMin: 2500000, salaryMax: 4000000 },
      { title: 'Développeur', description: 'Développement logiciel', level: 'MID', salaryMin: 1500000, salaryMax: 2500000 },
      { title: 'Technicien IT', description: 'Support technique', level: 'JUNIOR', salaryMin: 1000000, salaryMax: 1500000 },
      { title: 'Administrateur Réseau', description: 'Administration réseau', level: 'MID', salaryMin: 1800000, salaryMax: 2800000 },
    ],
    'Finance et Comptabilité': [
      { title: 'Directeur Financier', description: 'Direction financière', level: 'EXECUTIVE', salaryMin: 4500000, salaryMax: 7500000 },
      { title: 'Contrôleur de Gestion', description: 'Contrôle de gestion', level: 'SENIOR', salaryMin: 2500000, salaryMax: 4000000 },
      { title: 'Comptable Senior', description: 'Comptabilité senior', level: 'SENIOR', salaryMin: 2000000, salaryMax: 3000000 },
      { title: 'Comptable', description: 'Comptabilité générale', level: 'MID', salaryMin: 1200000, salaryMax: 2000000 },
      { title: 'Assistant Comptable', description: 'Assistance comptable', level: 'JUNIOR', salaryMin: 800000, salaryMax: 1200000 },
    ],
    'Commercial': [
      { title: 'Directeur Commercial', description: 'Direction commerciale', level: 'EXECUTIVE', salaryMin: 4000000, salaryMax: 6500000 },
      { title: 'Responsable Ventes', description: 'Responsable des ventes', level: 'SENIOR', salaryMin: 2200000, salaryMax: 3500000 },
      { title: 'Commercial Senior', description: 'Vente senior', level: 'SENIOR', salaryMin: 1800000, salaryMax: 2800000 },
      { title: 'Commercial', description: 'Représentant commercial', level: 'MID', salaryMin: 1200000, salaryMax: 2000000 },
      { title: 'Assistant Commercial', description: 'Assistance commerciale', level: 'JUNIOR', salaryMin: 900000, salaryMax: 1300000 },
    ],
    'Production': [
      { title: 'Directeur de Production', description: 'Direction de la production', level: 'EXECUTIVE', salaryMin: 4500000, salaryMax: 7000000 },
      { title: 'Chef de Production', description: 'Supervision production', level: 'SENIOR', salaryMin: 2500000, salaryMax: 4000000 },
      { title: 'Superviseur', description: 'Supervision d\'équipe', level: 'MID', salaryMin: 1500000, salaryMax: 2300000 },
      { title: 'Opérateur de Production', description: 'Opération de production', level: 'JUNIOR', salaryMin: 900000, salaryMax: 1400000 },
      { title: 'Contrôleur Qualité', description: 'Contrôle qualité', level: 'MID', salaryMin: 1300000, salaryMax: 2000000 },
    ],
    'Marketing': [
      { title: 'Directeur Marketing', description: 'Direction marketing', level: 'EXECUTIVE', salaryMin: 3500000, salaryMax: 6000000 },
      { title: 'Chef de Produit', description: 'Gestion de produit', level: 'SENIOR', salaryMin: 2200000, salaryMax: 3500000 },
      { title: 'Responsable Communication', description: 'Communication et relations publiques', level: 'SENIOR', salaryMin: 2000000, salaryMax: 3200000 },
      { title: 'Chargé de Marketing', description: 'Exécution marketing', level: 'MID', salaryMin: 1300000, salaryMax: 2100000 },
      { title: 'Assistant Marketing', description: 'Assistance marketing', level: 'JUNIOR', salaryMin: 900000, salaryMax: 1400000 },
    ],
    'Service Client': [
      { title: 'Responsable Service Client', description: 'Direction service client', level: 'SENIOR', salaryMin: 2000000, salaryMax: 3200000 },
      { title: 'Superviseur Call Center', description: 'Supervision centre d\'appels', level: 'MID', salaryMin: 1400000, salaryMax: 2200000 },
      { title: 'Agent Service Client', description: 'Service à la clientèle', level: 'JUNIOR', salaryMin: 800000, salaryMax: 1200000 },
      { title: 'Technicien Support', description: 'Support technique client', level: 'MID', salaryMin: 1100000, salaryMax: 1700000 },
    ],
    'Logistique': [
      { title: 'Directeur Logistique', description: 'Direction logistique', level: 'EXECUTIVE', salaryMin: 3500000, salaryMax: 5500000 },
      { title: 'Responsable Entrepôt', description: 'Gestion d\'entrepôt', level: 'SENIOR', salaryMin: 1800000, salaryMax: 2800000 },
      { title: 'Gestionnaire Stock', description: 'Gestion des stocks', level: 'MID', salaryMin: 1200000, salaryMax: 1900000 },
      { title: 'Magasinier', description: 'Opérations d\'entrepôt', level: 'JUNIOR', salaryMin: 800000, salaryMax: 1200000 },
      { title: 'Chauffeur', description: 'Transport et livraison', level: 'JUNIOR', salaryMin: 700000, salaryMax: 1100000 },
    ],
    'Sécurité': [
      { title: 'Responsable Sécurité', description: 'Direction sécurité', level: 'SENIOR', salaryMin: 2200000, salaryMax: 3500000 },
      { title: 'Agent de Sécurité', description: 'Surveillance et sécurité', level: 'JUNIOR', salaryMin: 600000, salaryMax: 1000000 },
      { title: 'Superviseur Sécurité', description: 'Supervision équipe sécurité', level: 'MID', salaryMin: 1200000, salaryMax: 1800000 },
    ],
    'Maintenance': [
      { title: 'Chef de Maintenance', description: 'Direction maintenance', level: 'SENIOR', salaryMin: 2500000, salaryMax: 4000000 },
      { title: 'Technicien Maintenance', description: 'Maintenance technique', level: 'MID', salaryMin: 1200000, salaryMax: 1900000 },
      { title: 'Électricien', description: 'Maintenance électrique', level: 'MID', salaryMin: 1100000, salaryMax: 1700000 },
      { title: 'Mécanicien', description: 'Maintenance mécanique', level: 'MID', salaryMin: 1000000, salaryMax: 1600000 },
    ],
  };

  const createdPositions = [];

  for (const department of departments) {
    const positions = positionsByDepartment[department.name] || [
      { title: 'Responsable', description: 'Responsable de département', level: 'SENIOR', salaryMin: 2000000, salaryMax: 3500000 },
      { title: 'Assistant', description: 'Assistant de département', level: 'JUNIOR', salaryMin: 900000, salaryMax: 1400000 },
    ];

    for (const posData of positions) {
      try {
        const position = await prisma.position.create({
          data: {
            ...posData,
            departmentId: department.id,
            companyId: department.companyId,
          },
        });
        createdPositions.push(position);
        console.log(`✅ Created position: ${position.title} in ${department.name}`);
      } catch (error) {
        console.error(`❌ Error creating position ${posData.title}:`, error.message);
      }
    }
  }

  console.log(`💼 Created ${createdPositions.length} positions`);
  return createdPositions;
}
