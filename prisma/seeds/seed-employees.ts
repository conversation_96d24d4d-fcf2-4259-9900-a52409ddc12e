import { PrismaClient, RoleEnum, GenderEnum } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

export async function seedEmployees(companies: any[], departments: any[], positions: any[]) {
  console.log('👥 Seeding employees...');

  // Noms congolais typiques
  const congoleseNames = {
    male: {
      firstNames: [
        '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
      ],
      lastNames: [
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>a', '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>d', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON>wi<PERSON>b<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>s<PERSON><PERSON>'
      ]
    },
    female: {
      first<PERSON><PERSON>s: [
        '<PERSON>', '<PERSON>h<PERSON>r<PERSON><PERSON>', '<PERSON>', 'Françoise', 'Antoinette', 'Bernadette', 'Christine', 'Denise',
        'Élisabeth', 'Georgette', 'Hélène', 'Immaculée', 'Joséphine', 'Khadija', 'Lucie', 'Madeleine',
        'Nathalie', 'Odette', 'Pascaline', 'Rosalie', 'Solange', 'Thérèse', 'Ursule', 'Véronique',
        'Wivine', 'Yvonne', 'Zawadi', 'Amina', 'Blandine', 'Cécile'
      ],
      lastNames: [
        'Mukendi', 'Tshilombo', 'Kabongo', 'Mbuyi', 'Ngoy', 'Kasongo', 'Mulumba', 'Tshisekedi',
        'Kabila', 'Lukaku', 'Ndala', 'Mwamba', 'Kalala', 'Katumba', 'Mbala', 'Nzuzi',
        'Ilunga', 'Kabeya', 'Mujinga', 'Nkulu', 'Tshiaba', 'Kapend', 'Mwenze', 'Luboya',
        'Kalonji', 'Mwilambwe', 'Tshimbombo', 'Kayembe', 'Mwanangombe', 'Tshiswaka'
      ]
    }
  };

  // Adresses typiques de Kinshasa et autres villes
  const addresses = [
    'Avenue Lumumba, Commune de Kinshasa, Kinshasa',
    'Boulevard du 30 Juin, Commune de la Gombe, Kinshasa',
    'Avenue de la Justice, Commune de Kalamu, Kinshasa',
    'Rue de la Paix, Commune de Lemba, Kinshasa',
    'Avenue des Cliniques, Commune de Lingwala, Kinshasa',
    'Boulevard Triomphal, Commune de Matete, Kinshasa',
    'Avenue de l\'Université, Commune de Lemba, Kinshasa',
    'Rue du Commerce, Commune de Barumbu, Kinshasa',
    'Avenue Kasa-Vubu, Commune de Kalamu, Kinshasa',
    'Boulevard Laurent Kabila, Commune de la Gombe, Kinshasa',
    'Avenue Mobutu, Lubumbashi, Haut-Katanga',
    'Rue de la Mine, Lubumbashi, Haut-Katanga',
    'Avenue de l\'Indépendance, Mbuji-Mayi, Kasaï-Oriental',
    'Boulevard Lumumba, Kisangani, Tshopo',
    'Avenue de la République, Bukavu, Sud-Kivu',
    'Rue de la Paix, Goma, Nord-Kivu',
    'Avenue des Martyrs, Kananga, Kasaï-Central',
    'Boulevard de la Révolution, Matadi, Kongo-Central'
  ];

  const createdEmployees = [];
  const hashedPassword = await bcrypt.hash('password123', 10);

  // Créer des employés pour chaque entreprise
  for (const company of companies) {
    const companyDepartments = departments.filter(d => d.companyId === company.id);
    const companyPositions = positions.filter(p => p.companyId === company.id);
    
    // Nombre d'employés basé sur la taille de l'entreprise
    const employeeCount = Math.min(Math.floor(company.employeeCount / 10), 50); // Limiter pour la démo
    
    for (let i = 0; i < employeeCount; i++) {
      const isManager = i < Math.ceil(employeeCount * 0.1); // 10% de managers
      const isHR = i < Math.ceil(employeeCount * 0.05); // 5% RH
      const isMale = Math.random() > 0.4; // 60% hommes, 40% femmes
      
      const gender = isMale ? GenderEnum.MALE : GenderEnum.FEMALE;
      const names = isMale ? congoleseNames.male : congoleseNames.female;
      
      const firstName = names.firstNames[Math.floor(Math.random() * names.firstNames.length)];
      const lastName = names.lastNames[Math.floor(Math.random() * names.lastNames.length)];
      const email = `${firstName.toLowerCase().replace(/[^a-z]/g, '')}.${lastName.toLowerCase()}@${company.companyName.toLowerCase().replace(/[^a-z]/g, '')}.cd`;
      
      // Sélectionner un département et une position
      const department = companyDepartments[Math.floor(Math.random() * companyDepartments.length)];
      const departmentPositions = companyPositions.filter(p => p.departmentId === department.id);
      const position = departmentPositions.length > 0 
        ? departmentPositions[Math.floor(Math.random() * departmentPositions.length)]
        : companyPositions[Math.floor(Math.random() * companyPositions.length)];

      // Déterminer le rôle
      let role = RoleEnum.EMPLOYEE;
      if (isHR && department.name.includes('Ressources Humaines')) {
        role = RoleEnum.ADMIN_HR;
      } else if (isManager || position.title.includes('Directeur') || position.title.includes('Chef')) {
        role = RoleEnum.MANAGER;
      }

      // Générer des dates réalistes
      const hireDate = new Date();
      hireDate.setFullYear(hireDate.getFullYear() - Math.floor(Math.random() * 10)); // Embauché dans les 10 dernières années
      
      const birthDate = new Date();
      birthDate.setFullYear(birthDate.getFullYear() - (25 + Math.floor(Math.random() * 35))); // Âge entre 25 et 60 ans

      // Salaire basé sur la position
      const salaryRange = position.salaryMax - position.salaryMin;
      const salary = position.salaryMin + Math.floor(Math.random() * salaryRange);

      try {
        // Créer l'utilisateur
        const user = await prisma.user.create({
          data: {
            email,
            password: hashedPassword,
            role,
            isActive: true,
            profile: {
              create: {
                firstName,
                lastName,
                phone: `+243 ${Math.floor(Math.random() * 90) + 10} ${Math.floor(Math.random() * 900) + 100} ${Math.floor(Math.random() * 9000) + 1000}`,
                address: addresses[Math.floor(Math.random() * addresses.length)],
                dateOfBirth: birthDate,
                gender,
                nationality: 'Congolaise',
                profilePicture: `https://api.dicebear.com/7.x/avataaars/svg?seed=${firstName}${lastName}`,
              },
            },
          },
          include: {
            profile: true,
          },
        });

        // Créer les données d'employé
        const employee = await prisma.employeeData.create({
          data: {
            employeeNumber: `EMP${company.id.slice(-4).toUpperCase()}${String(i + 1).padStart(4, '0')}`,
            hireDate,
            isActive: true,
            userId: user.id,
            companyId: company.id,
            departmentId: department.id,
            positionId: position.id,
          },
        });

        // Créer un salaire initial
        await prisma.salary.create({
          data: {
            amount: salary,
            currency: company.currency,
            effectiveDate: hireDate,
            employeeId: employee.id,
          },
        });

        createdEmployees.push({ user, employee });
        console.log(`✅ Created employee: ${firstName} ${lastName} at ${company.companyName}`);
      } catch (error) {
        console.error(`❌ Error creating employee ${firstName} ${lastName}:`, error.message);
      }
    }
  }

  console.log(`👥 Created ${createdEmployees.length} employees`);
  return createdEmployees;
}

export async function seedLeaves(employees: any[]) {
  console.log('🏖️ Seeding leaves...');

  const leaveTypes = ['ANNUAL', 'SICK', 'MATERNITY', 'PATERNITY', 'PERSONAL', 'EMERGENCY'];
  const statuses = ['PENDING', 'APPROVED', 'REJECTED'];
  
  const createdLeaves = [];

  for (const { employee } of employees.slice(0, 30)) { // Créer des congés pour 30 employés
    const leaveCount = Math.floor(Math.random() * 3) + 1; // 1-3 congés par employé
    
    for (let i = 0; i < leaveCount; i++) {
      const leaveType = leaveTypes[Math.floor(Math.random() * leaveTypes.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + Math.floor(Math.random() * 90) - 30); // ±30 jours
      
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 10) + 1); // 1-10 jours
      
      try {
        const leave = await prisma.leave.create({
          data: {
            leaveType,
            startDate,
            endDate,
            reason: `Demande de congé ${leaveType.toLowerCase()}`,
            status,
            employeeId: employee.id,
          },
        });
        
        createdLeaves.push(leave);
        console.log(`✅ Created leave: ${leaveType} for employee ${employee.employeeNumber}`);
      } catch (error) {
        console.error(`❌ Error creating leave:`, error.message);
      }
    }
  }

  console.log(`🏖️ Created ${createdLeaves.length} leaves`);
  return createdLeaves;
}

export async function seedTimesheets(employees: any[]) {
  console.log('⏰ Seeding timesheets...');

  const createdTimesheets = [];

  for (const { employee } of employees.slice(0, 20)) { // Créer des feuilles de temps pour 20 employés
    // Créer des feuilles de temps pour les 30 derniers jours
    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      // Ignorer les weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue;
      
      const clockIn = new Date(date);
      clockIn.setHours(8 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60)); // 8h-10h
      
      const clockOut = new Date(clockIn);
      clockOut.setHours(clockOut.getHours() + 8 + Math.floor(Math.random() * 2)); // 8-10h de travail
      
      const hoursWorked = (clockOut.getTime() - clockIn.getTime()) / (1000 * 60 * 60);
      
      try {
        const timesheet = await prisma.timesheet.create({
          data: {
            date,
            clockIn,
            clockOut,
            hoursWorked,
            employeeId: employee.id,
          },
        });
        
        createdTimesheets.push(timesheet);
      } catch (error) {
        console.error(`❌ Error creating timesheet:`, error.message);
      }
    }
  }

  console.log(`⏰ Created ${createdTimesheets.length} timesheets`);
  return createdTimesheets;
}
