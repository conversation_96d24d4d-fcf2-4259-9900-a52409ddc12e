import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedDepartments(companies: any[]) {
  console.log('🏛️ Seeding departments...');

  const departmentsByIndustry = {
    Banking: [
      { name: 'Direction Générale', description: 'Direction exécutive de la banque' },
      { name: 'Crédit et Risques', description: 'Gestion des crédits et analyse des risques' },
      { name: 'Opérations Bancaires', description: 'Opérations quotidiennes et transactions' },
      { name: 'Ressources Humaines', description: 'Gestion du personnel et développement RH' },
      { name: 'Informatique', description: 'Systèmes d\'information et technologie' },
      { name: 'Audit Interne', description: 'Contrôle interne et conformité' },
      { name: 'Relations Clientèle', description: 'Service client et relations publiques' },
    ],
    Mining: [
      { name: 'Direction Générale', description: 'Direction exécutive de l\'entreprise minière' },
      { name: 'Extraction', description: 'Opérations d\'extraction minière' },
      { name: 'Traitement', description: 'Traitement et raffinage des minerais' },
      { name: 'Géologie', description: 'Exploration et études géologiques' },
      { name: 'Sécurité et Environnement', description: 'Sécurité au travail et protection environnementale' },
      { name: 'Maintenance', description: 'Maintenance des équipements miniers' },
      { name: 'Ressources Humaines', description: 'Gestion du personnel minier' },
      { name: 'Logistique', description: 'Transport et approvisionnement' },
    ],
    Telecommunications: [
      { name: 'Direction Générale', description: 'Direction exécutive des télécommunications' },
      { name: 'Réseau et Infrastructure', description: 'Gestion du réseau et infrastructure technique' },
      { name: 'Service Client', description: 'Support et service à la clientèle' },
      { name: 'Marketing et Ventes', description: 'Marketing et développement commercial' },
      { name: 'Informatique', description: 'Systèmes d\'information et développement' },
      { name: 'Ressources Humaines', description: 'Gestion des ressources humaines' },
      { name: 'Finance et Comptabilité', description: 'Gestion financière et comptable' },
    ],
    NGO: [
      { name: 'Direction Pays', description: 'Direction nationale du programme' },
      { name: 'Programmes', description: 'Mise en œuvre des programmes humanitaires' },
      { name: 'Logistique', description: 'Approvisionnement et logistique' },
      { name: 'Ressources Humaines', description: 'Gestion des ressources humaines' },
      { name: 'Finance et Administration', description: 'Gestion financière et administrative' },
      { name: 'Communication', description: 'Communication et plaidoyer' },
      { name: 'Partenariats', description: 'Relations avec les partenaires' },
    ],
    'Food & Beverage': [
      { name: 'Direction Générale', description: 'Direction exécutive de la brasserie' },
      { name: 'Production', description: 'Production de bières et boissons' },
      { name: 'Qualité', description: 'Contrôle qualité et assurance' },
      { name: 'Commercial', description: 'Ventes et distribution' },
      { name: 'Marketing', description: 'Marketing et communication' },
      { name: 'Ressources Humaines', description: 'Gestion des ressources humaines' },
      { name: 'Maintenance', description: 'Maintenance industrielle' },
      { name: 'Approvisionnement', description: 'Achats et approvisionnement' },
    ],
    Education: [
      { name: 'Rectorat', description: 'Direction de l\'université' },
      { name: 'Faculté de Médecine', description: 'Enseignement médical et recherche' },
      { name: 'Faculté de Droit', description: 'Enseignement juridique' },
      { name: 'Faculté des Sciences', description: 'Sciences exactes et naturelles' },
      { name: 'Faculté des Lettres', description: 'Sciences humaines et littérature' },
      { name: 'Administration', description: 'Administration universitaire' },
      { name: 'Bibliothèque', description: 'Services de bibliothèque et documentation' },
      { name: 'Recherche', description: 'Coordination de la recherche' },
    ],
    Healthcare: [
      { name: 'Direction Médicale', description: 'Direction médicale de l\'hôpital' },
      { name: 'Médecine Interne', description: 'Service de médecine interne' },
      { name: 'Chirurgie', description: 'Services chirurgicaux' },
      { name: 'Pédiatrie', description: 'Soins pédiatriques' },
      { name: 'Urgences', description: 'Service des urgences' },
      { name: 'Pharmacie', description: 'Pharmacie hospitalière' },
      { name: 'Laboratoire', description: 'Analyses médicales' },
      { name: 'Administration', description: 'Administration hospitalière' },
      { name: 'Ressources Humaines', description: 'Gestion du personnel médical' },
    ],
    Aviation: [
      { name: 'Direction Générale', description: 'Direction de la compagnie aérienne' },
      { name: 'Opérations Aériennes', description: 'Opérations de vol et navigation' },
      { name: 'Maintenance', description: 'Maintenance aéronautique' },
      { name: 'Service Client', description: 'Service à la clientèle aérienne' },
      { name: 'Sécurité', description: 'Sécurité aérienne et sûreté' },
      { name: 'Commercial', description: 'Ventes et réservations' },
      { name: 'Ressources Humaines', description: 'Gestion du personnel aéronautique' },
    ],
  };

  const createdDepartments = [];

  for (const company of companies) {
    const departments = departmentsByIndustry[company.industry] || departmentsByIndustry['NGO'];
    
    for (const deptData of departments) {
      try {
        const department = await prisma.department.create({
          data: {
            ...deptData,
            companyId: company.id,
          },
        });
        createdDepartments.push(department);
        console.log(`✅ Created department: ${department.name} for ${company.companyName}`);
      } catch (error) {
        console.error(`❌ Error creating department ${deptData.name}:`, error.message);
      }
    }
  }

  console.log(`🏛️ Created ${createdDepartments.length} departments`);
  return createdDepartments;
}
