import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedCompanies() {
  console.log('🏢 Seeding companies...');

  const companies = [
    {
      companyName: 'Banque Centrale du Congo',
      email: '<EMAIL>',
      phone: '+243 81 123 4567',
      address: '563 Boulevard du 30 Juin, Kinshasa, RDC',
      website: 'https://www.bcc.cd',
      industry: 'Banking',
      description: 'Banque centrale de la République Démocratique du Congo',
      logo: 'https://example.com/logos/bcc.png',
      foundedYear: 1964,
      employeeCount: 850,
      revenue: **********, // 2.5 milliards CDF
      currency: 'CDF',
      timeZone: 'Africa/Kinshasa',
      country: 'RDC',
      city: 'Kinshasa',
      postalCode: '7797',
    },
    {
      companyName: 'Gécamines SARL',
      email: '<EMAIL>',
      phone: '+243 97 234 5678',
      address: '310 Avenue Lumumba, Lubumbashi, RDC',
      website: 'https://www.gecamines.cd',
      industry: 'Mining',
      description: 'Société minière leader en extraction de cuivre et cobalt',
      logo: 'https://example.com/logos/gecamines.png',
      foundedYear: 1967,
      employeeCount: 12500,
      revenue: ***********, // 15 milliards CDF
      currency: 'CDF',
      timeZone: 'Africa/Kinshasa',
      country: 'RDC',
      city: 'Lubumbashi',
      postalCode: '1854',
    },
    {
      companyName: 'Vodacom Congo',
      email: '<EMAIL>',
      phone: '+243 81 345 6789',
      address: '4856 Avenue des Cliniques, Kinshasa, RDC',
      website: 'https://www.vodacom.cd',
      industry: 'Telecommunications',
      description: 'Opérateur de télécommunications mobile leader en RDC',
      logo: 'https://example.com/logos/vodacom.png',
      foundedYear: 2002,
      employeeCount: 2800,
      revenue: 8500000000, // 8.5 milliards CDF
      currency: 'CDF',
      timeZone: 'Africa/Kinshasa',
      country: 'RDC',
      city: 'Kinshasa',
      postalCode: '8341',
    },
    {
      companyName: 'UNICEF RDC',
      email: '<EMAIL>',
      phone: '+243 81 456 7890',
      address: '372 Avenue de la Justice, Kinshasa, RDC',
      website: 'https://www.unicef.org/drc',
      industry: 'NGO',
      description: 'Organisation internationale pour la protection des droits des enfants',
      logo: 'https://example.com/logos/unicef.png',
      foundedYear: 1946,
      employeeCount: 450,
      revenue: 1200000000, // 1.2 milliards CDF
      currency: 'USD',
      timeZone: 'Africa/Kinshasa',
      country: 'RDC',
      city: 'Kinshasa',
      postalCode: '7240',
    },
    {
      companyName: 'Brasseries du Congo',
      email: '<EMAIL>',
      phone: '+243 99 567 8901',
      address: '1245 Boulevard Triomphal, Kinshasa, RDC',
      website: 'https://www.bracongo.cd',
      industry: 'Food & Beverage',
      description: 'Brasserie et producteur de boissons leader en RDC',
      logo: 'https://example.com/logos/bracongo.png',
      foundedYear: 1923,
      employeeCount: 3200,
      revenue: 5800000000, // 5.8 milliards CDF
      currency: 'CDF',
      timeZone: 'Africa/Kinshasa',
      country: 'RDC',
      city: 'Kinshasa',
      postalCode: '9876',
    },
    {
      companyName: 'Université de Kinshasa',
      email: '<EMAIL>',
      phone: '+243 81 678 9012',
      address: 'Campus de Lemba, Kinshasa, RDC',
      website: 'https://www.unikin.ac.cd',
      industry: 'Education',
      description: 'Université publique de référence en République Démocratique du Congo',
      logo: 'https://example.com/logos/unikin.png',
      foundedYear: 1954,
      employeeCount: 1850,
      revenue: *********, // 950 millions CDF
      currency: 'CDF',
      timeZone: 'Africa/Kinshasa',
      country: 'RDC',
      city: 'Kinshasa',
      postalCode: '190',
    },
    {
      companyName: 'Hôpital Général de Référence de Kinshasa',
      email: '<EMAIL>',
      phone: '+243 97 789 0123',
      address: '1234 Avenue de l\'Hôpital, Kinshasa, RDC',
      website: 'https://www.hgrk.cd',
      industry: 'Healthcare',
      description: 'Hôpital de référence pour les soins de santé spécialisés',
      logo: 'https://example.com/logos/hgrk.png',
      foundedYear: 1958,
      employeeCount: 2100,
      revenue: **********, // 1.8 milliards CDF
      currency: 'CDF',
      timeZone: 'Africa/Kinshasa',
      country: 'RDC',
      city: 'Kinshasa',
      postalCode: '4563',
    },
    {
      companyName: 'Congo Airways',
      email: '<EMAIL>',
      phone: '+243 81 890 1234',
      address: 'Aéroport International de N\'djili, Kinshasa, RDC',
      website: 'https://www.congoairways.com',
      industry: 'Aviation',
      description: 'Compagnie aérienne nationale de la République Démocratique du Congo',
      logo: 'https://example.com/logos/congo-airways.png',
      foundedYear: 2015,
      employeeCount: 680,
      revenue: **********, // 3.2 milliards CDF
      currency: 'USD',
      timeZone: 'Africa/Kinshasa',
      country: 'RDC',
      city: 'Kinshasa',
      postalCode: '8024',
    },
  ];

  const createdCompanies = [];

  for (const companyData of companies) {
    try {
      const company = await prisma.company.create({
        data: companyData,
      });
      createdCompanies.push(company);
      console.log(`✅ Created company: ${company.companyName}`);
    } catch (error) {
      console.error(`❌ Error creating company ${companyData.companyName}:`, error.message);
    }
  }

  console.log(`🏢 Created ${createdCompanies.length} companies`);
  return createdCompanies;
}

export async function seedCompanySettings(companies: any[]) {
  console.log('⚙️ Seeding company settings...');

  const settingsData = [
    {
      workingDaysPerWeek: 5,
      workingHoursPerDay: 8.0,
      timeZone: 'Africa/Kinshasa',
      currency: 'CDF',
      language: 'fr',
      annualLeaveEntitlement: 21,
      sickLeaveEntitlement: 10,
      maternityLeaveEntitlement: 98, // 14 semaines selon la loi RDC
      emailNotifications: true,
      smsNotifications: false,
      passwordMinLength: 8,
      sessionTimeout: 30,
      twoFactorAuth: false,
    },
    {
      workingDaysPerWeek: 6,
      workingHoursPerDay: 7.5,
      timeZone: 'Africa/Kinshasa',
      currency: 'CDF',
      language: 'fr',
      annualLeaveEntitlement: 25,
      sickLeaveEntitlement: 15,
      maternityLeaveEntitlement: 98,
      emailNotifications: true,
      smsNotifications: true,
      passwordMinLength: 10,
      sessionTimeout: 45,
      twoFactorAuth: true,
    },
    {
      workingDaysPerWeek: 5,
      workingHoursPerDay: 8.5,
      timeZone: 'Africa/Kinshasa',
      currency: 'USD',
      language: 'en',
      annualLeaveEntitlement: 30,
      sickLeaveEntitlement: 12,
      maternityLeaveEntitlement: 112, // 16 semaines pour les ONG
      emailNotifications: true,
      smsNotifications: true,
      passwordMinLength: 12,
      sessionTimeout: 60,
      twoFactorAuth: true,
    },
  ];

  const createdSettings = [];

  for (let i = 0; i < companies.length; i++) {
    const company = companies[i];
    const settings = settingsData[i % settingsData.length];

    try {
      const companySettings = await prisma.companySettings.create({
        data: {
          ...settings,
          companyId: company.id,
        },
      });
      createdSettings.push(companySettings);
      console.log(`✅ Created settings for: ${company.companyName}`);
    } catch (error) {
      console.error(`❌ Error creating settings for ${company.companyName}:`, error.message);
    }
  }

  console.log(`⚙️ Created ${createdSettings.length} company settings`);
  return createdSettings;
}
