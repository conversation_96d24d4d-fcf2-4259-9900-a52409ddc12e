import { PrismaClient, ReportTypeEnum, ReportScheduleEnum, ReportExecutionStatusEnum } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedReports(companies: any[], employees: any[]) {
  console.log('📊 Seeding reports...');

  const reportTemplates = [
    {
      name: 'Rapport Mensuel des Employés',
      description: 'Liste complète des employés actifs avec leurs informations',
      reportType: ReportTypeEnum.EMPLOYEE_LIST,
      schedule: ReportScheduleEnum.MONTHLY,
      parameters: {
        includeInactive: false,
        includeSalary: true,
        includeDepartment: true,
        includePosition: true,
      },
      isActive: true,
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON> de Paie Mensuel',
      description: 'Résumé des salaires et charges sociales du mois',
      reportType: ReportTypeEnum.PAYROLL_SUMMARY,
      schedule: ReportScheduleEnum.MONTHLY,
      parameters: {
        includeTaxes: true,
        includeBenefits: true,
        groupByDepartment: true,
      },
      isActive: true,
    },
    {
      name: 'Rapport Trimestriel des Congés',
      description: 'Analyse des congés pris et soldes restants',
      reportType: ReportTypeEnum.LEAVE_REPORT,
      schedule: ReportScheduleEnum.QUARTERLY,
      parameters: {
        includeBalance: true,
        groupByType: true,
        includePending: true,
      },
      isActive: true,
    },
    {
      name: 'Rapport de Présence Hebdomadaire',
      description: 'Suivi de la présence et des heures travaillées',
      reportType: ReportTypeEnum.ATTENDANCE_REPORT,
      schedule: ReportScheduleEnum.WEEKLY,
      parameters: {
        includeOvertime: true,
        includeAbsences: true,
        calculateTotals: true,
      },
      isActive: true,
    },
    {
      name: 'Rapport Annuel de Performance',
      description: 'Évaluation annuelle des performances des employés',
      reportType: ReportTypeEnum.PERFORMANCE_REPORT,
      schedule: ReportScheduleEnum.YEARLY,
      parameters: {
        includeGoals: true,
        includeRatings: true,
        includeComments: true,
      },
      isActive: true,
    },
    {
      name: 'Rapport de Recrutement',
      description: 'Statistiques de recrutement et candidatures',
      reportType: ReportTypeEnum.RECRUITMENT_REPORT,
      schedule: null, // Rapport à la demande
      parameters: {
        includeApplications: true,
        includeHires: true,
        includeRejections: true,
        groupByPosition: true,
      },
      isActive: true,
    },
    {
      name: 'Rapport Personnalisé RH',
      description: 'Rapport personnalisé pour les besoins spécifiques RH',
      reportType: ReportTypeEnum.CUSTOM,
      schedule: null,
      parameters: {
        customFields: [],
        filters: {},
        grouping: 'department',
      },
      isActive: true,
    },
  ];

  const createdReports = [];

  for (const company of companies) {
    // Trouver un employé RH ou manager pour créer les rapports
    const hrEmployees = employees.filter(({ user, employee }) => 
      employee.companyId === company.id && 
      (user.role === 'ADMIN_HR' || user.role === 'MANAGER' || user.role === 'SUPER_ADMIN')
    );

    if (hrEmployees.length === 0) continue;

    const creator = hrEmployees[Math.floor(Math.random() * hrEmployees.length)];

    for (const reportData of reportTemplates) {
      try {
        const report = await prisma.report.create({
          data: {
            ...reportData,
            companyId: company.id,
            createdById: creator.employee.id,
          },
        });

        createdReports.push(report);
        console.log(`✅ Created report: ${report.name} for ${company.companyName}`);
      } catch (error) {
        console.error(`❌ Error creating report ${reportData.name}:`, error.message);
      }
    }
  }

  console.log(`📊 Created ${createdReports.length} reports`);
  return createdReports;
}

export async function seedReportExecutions(reports: any[], employees: any[]) {
  console.log('🔄 Seeding report executions...');

  const createdExecutions = [];

  for (const report of reports.slice(0, 20)) { // Créer des exécutions pour 20 rapports
    const executionCount = Math.floor(Math.random() * 5) + 1; // 1-5 exécutions par rapport
    
    // Trouver des exécuteurs potentiels
    const executors = employees.filter(({ user, employee }) => 
      employee.companyId === report.companyId && 
      (user.role === 'ADMIN_HR' || user.role === 'MANAGER' || user.role === 'SUPER_ADMIN')
    );

    if (executors.length === 0) continue;

    for (let i = 0; i < executionCount; i++) {
      const executor = executors[Math.floor(Math.random() * executors.length)];
      
      const startedAt = new Date();
      startedAt.setDate(startedAt.getDate() - Math.floor(Math.random() * 30)); // Dans les 30 derniers jours
      
      const completedAt = new Date(startedAt);
      completedAt.setMinutes(completedAt.getMinutes() + Math.floor(Math.random() * 60) + 5); // 5-65 minutes plus tard
      
      const statuses = [
        ReportExecutionStatusEnum.COMPLETED,
        ReportExecutionStatusEnum.FAILED,
        ReportExecutionStatusEnum.RUNNING
      ];
      
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      const executionData: any = {
        status,
        startedAt,
        parameters: report.parameters,
        reportId: report.id,
        executedById: executor.employee.id,
      };

      if (status === ReportExecutionStatusEnum.COMPLETED) {
        executionData.completedAt = completedAt;
        executionData.fileUrl = `https://reports.example.com/${report.id}/execution_${i + 1}.pdf`;
      } else if (status === ReportExecutionStatusEnum.FAILED) {
        executionData.completedAt = completedAt;
        executionData.errorMessage = 'Erreur lors de la génération du rapport: données insuffisantes';
      }
      // Pour RUNNING, on ne met ni completedAt ni fileUrl ni errorMessage

      try {
        const execution = await prisma.reportExecution.create({
          data: executionData,
        });

        createdExecutions.push(execution);
        console.log(`✅ Created report execution for: ${report.name}`);
      } catch (error) {
        console.error(`❌ Error creating report execution:`, error.message);
      }
    }
  }

  console.log(`🔄 Created ${createdExecutions.length} report executions`);
  return createdExecutions;
}
