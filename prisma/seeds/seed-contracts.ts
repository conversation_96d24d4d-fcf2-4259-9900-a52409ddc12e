import { PrismaClient, ContractTypeEnum, ContractStatusEnum } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedContractTemplates(companies: any[]) {
  console.log('📋 Seeding contract templates...');

  const templates = [
    {
      name: 'Contrat de Travail à Durée Indéterminée (CDI)',
      description: 'Template standard pour contrat CDI en RDC',
      contractType: ContractTypeEnum.PERMANENT,
      content: `
CONTRAT DE TRAVAIL À DURÉE INDÉTERMINÉE

Entre les soussignés :

{{companyName}}, société de droit congolais, ayant son siège social à {{companyAddress}}, représentée par {{directorName}}, en qualité de {{directorTitle}}, ci-après dénommée "l'Employeur",

D'une part,

Et {{employeeName}}, né(e) le {{birthDate}} à {{birthPlace}}, de nationalité {{nationality}}, demeurant à {{employeeAddress}}, ci-après dénommé(e) "l'Employé(e)",

D'autre part,

Il a été convenu ce qui suit :

ARTICLE 1 - ENGAGEMENT
L'Employeur engage l'Employé(e) en qualité de {{jobTitle}} au sein du département {{department}}.

ARTICLE 2 - DURÉE DU CONTRAT
Le présent contrat est conclu pour une durée indéterminée à compter du {{startDate}}.

ARTICLE 3 - PÉRIODE D'ESSAI
Une période d'essai de {{probationPeriod}} mois est prévue, durant laquelle chacune des parties peut rompre le contrat sans préavis ni indemnité.

ARTICLE 4 - RÉMUNÉRATION
L'Employé(e) percevra un salaire mensuel brut de {{salary}} {{currency}}, payable le {{payDay}} de chaque mois.

ARTICLE 5 - HORAIRES DE TRAVAIL
La durée hebdomadaire de travail est fixée à {{workingHours}} heures, réparties sur {{workingDays}} jours.

ARTICLE 6 - CONGÉS
L'Employé(e) bénéficie de {{annualLeave}} jours de congés payés par année civile.

ARTICLE 7 - OBLIGATIONS
L'Employé(e) s'engage à respecter le règlement intérieur de l'entreprise et à exercer ses fonctions avec diligence et loyauté.

Fait à {{city}}, le {{contractDate}}

L'Employeur                    L'Employé(e)
{{directorName}}               {{employeeName}}
      `,
      isActive: true,
    },
    {
      name: 'Contrat de Travail à Durée Déterminée (CDD)',
      description: 'Template pour contrat CDD en RDC',
      contractType: ContractTypeEnum.FIXED_TERM,
      content: `
CONTRAT DE TRAVAIL À DURÉE DÉTERMINÉE

Entre les soussignés :

{{companyName}}, société de droit congolais, ayant son siège social à {{companyAddress}}, représentée par {{directorName}}, en qualité de {{directorTitle}}, ci-après dénommée "l'Employeur",

D'une part,

Et {{employeeName}}, né(e) le {{birthDate}} à {{birthPlace}}, de nationalité {{nationality}}, demeurant à {{employeeAddress}}, ci-après dénommé(e) "l'Employé(e)",

D'autre part,

Il a été convenu ce qui suit :

ARTICLE 1 - ENGAGEMENT
L'Employeur engage l'Employé(e) en qualité de {{jobTitle}} au sein du département {{department}}.

ARTICLE 2 - DURÉE DU CONTRAT
Le présent contrat est conclu pour une durée déterminée du {{startDate}} au {{endDate}}.

ARTICLE 3 - MOTIF DU CDD
Ce contrat est justifié par : {{cddReason}}.

ARTICLE 4 - RÉMUNÉRATION
L'Employé(e) percevra un salaire mensuel brut de {{salary}} {{currency}}.

ARTICLE 5 - RENOUVELLEMENT
Ce contrat peut être renouvelé une fois pour une durée maximale égale à la durée initiale.

ARTICLE 6 - RÉSILIATION
En cas de rupture anticipée du contrat par l'une des parties, une indemnité égale au salaire restant dû jusqu'au terme sera versée.

Fait à {{city}}, le {{contractDate}}

L'Employeur                    L'Employé(e)
{{directorName}}               {{employeeName}}
      `,
      isActive: true,
    },
    {
      name: 'Contrat de Stage',
      description: 'Template pour contrat de stage',
      contractType: ContractTypeEnum.INTERNSHIP,
      content: `
CONVENTION DE STAGE

Entre les soussignés :

{{companyName}}, société de droit congolais, ayant son siège social à {{companyAddress}}, représentée par {{directorName}}, ci-après dénommée "l'Entreprise d'accueil",

{{schoolName}}, établissement d'enseignement, représenté par {{schoolDirector}}, ci-après dénommé "l'Établissement",

Et {{studentName}}, étudiant(e) en {{studyField}}, né(e) le {{birthDate}}, ci-après dénommé(e) "le/la Stagiaire",

Il a été convenu ce qui suit :

ARTICLE 1 - OBJET DU STAGE
Le stage a pour objet l'application pratique des connaissances théoriques dans le domaine de {{stageField}}.

ARTICLE 2 - DURÉE
Le stage se déroulera du {{startDate}} au {{endDate}}, soit une durée de {{stageDuration}} mois.

ARTICLE 3 - ENCADREMENT
Le stagiaire sera encadré par {{supervisorName}}, {{supervisorTitle}}.

ARTICLE 4 - GRATIFICATION
Une gratification mensuelle de {{allowance}} {{currency}} sera versée au stagiaire.

ARTICLE 5 - OBLIGATIONS
Le stagiaire s'engage à respecter le règlement intérieur et la confidentialité.

Fait à {{city}}, le {{contractDate}}

L'Entreprise        L'Établissement        Le/La Stagiaire
{{directorName}}    {{schoolDirector}}     {{studentName}}
      `,
      isActive: true,
    },
    {
      name: 'Contrat de Consultant',
      description: 'Template pour contrat de consultation',
      contractType: ContractTypeEnum.CONSULTANT,
      content: `
CONTRAT DE PRESTATION DE SERVICES

Entre les soussignés :

{{companyName}}, société de droit congolais, ayant son siège social à {{companyAddress}}, représentée par {{directorName}}, ci-après dénommée "le Client",

D'une part,

Et {{consultantName}}, consultant indépendant, demeurant à {{consultantAddress}}, ci-après dénommé(e) "le Consultant",

D'autre part,

Il a été convenu ce qui suit :

ARTICLE 1 - OBJET DE LA MISSION
Le Consultant s'engage à fournir les services suivants : {{serviceDescription}}.

ARTICLE 2 - DURÉE DE LA MISSION
La mission se déroulera du {{startDate}} au {{endDate}}.

ARTICLE 3 - RÉMUNÉRATION
Les honoraires sont fixés à {{consultantFee}} {{currency}} {{paymentTerms}}.

ARTICLE 4 - LIVRABLES
Le Consultant s'engage à livrer : {{deliverables}}.

ARTICLE 5 - CONFIDENTIALITÉ
Le Consultant s'engage à maintenir la confidentialité sur toutes les informations du Client.

ARTICLE 6 - PROPRIÉTÉ INTELLECTUELLE
Tous les travaux réalisés dans le cadre de cette mission appartiennent au Client.

Fait à {{city}}, le {{contractDate}}

Le Client                      Le Consultant
{{directorName}}               {{consultantName}}
      `,
      isActive: true,
    },
  ];

  const createdTemplates = [];

  for (const company of companies) {
    for (const templateData of templates) {
      try {
        const template = await prisma.contractTemplate.create({
          data: {
            ...templateData,
            companyId: company.id,
          },
        });
        createdTemplates.push(template);
        console.log(`✅ Created template: ${template.name} for ${company.companyName}`);
      } catch (error) {
        console.error(`❌ Error creating template ${templateData.name}:`, error.message);
      }
    }
  }

  console.log(`📋 Created ${createdTemplates.length} contract templates`);
  return createdTemplates;
}

export async function seedContracts(employees: any[], templates: any[]) {
  console.log('📄 Seeding contracts...');

  const createdContracts = [];

  for (const { employee } of employees.slice(0, 40)) { // Créer des contrats pour 40 employés
    const company = await prisma.company.findUnique({
      where: { id: employee.companyId },
    });

    const companyTemplates = templates.filter(t => t.companyId === employee.companyId);
    const template = companyTemplates[Math.floor(Math.random() * companyTemplates.length)];

    if (!template) continue;

    // Générer un numéro de contrat unique
    const contractNumber = `${company.companyName.substring(0, 3).toUpperCase()}-${new Date().getFullYear()}-${String(createdContracts.length + 1).padStart(4, '0')}`;

    const startDate = employee.hireDate;
    let endDate = null;
    let status = ContractStatusEnum.ACTIVE;

    // Pour les CDD, définir une date de fin
    if (template.contractType === ContractTypeEnum.FIXED_TERM) {
      endDate = new Date(startDate);
      endDate.setFullYear(endDate.getFullYear() + 1 + Math.floor(Math.random() * 2)); // 1-3 ans
      
      // Vérifier si le contrat est expiré
      if (endDate < new Date()) {
        status = ContractStatusEnum.EXPIRED;
      }
    }

    // Pour les stages, durée plus courte
    if (template.contractType === ContractTypeEnum.INTERNSHIP) {
      endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 3 + Math.floor(Math.random() * 9)); // 3-12 mois
      
      if (endDate < new Date()) {
        status = ContractStatusEnum.EXPIRED;
      }
    }

    // Récupérer le salaire de l'employé
    const salary = await prisma.salary.findFirst({
      where: { employeeId: employee.id },
      orderBy: { effectiveDate: 'desc' },
    });

    try {
      const contract = await prisma.contract.create({
        data: {
          contractNumber,
          title: `Contrat ${template.contractType} - ${employee.employeeNumber}`,
          contractType: template.contractType,
          status,
          startDate,
          endDate,
          salary: salary?.amount || 1000000,
          currency: company.currency,
          workingHours: 40, // 40h par semaine
          probationPeriod: template.contractType === ContractTypeEnum.PERMANENT ? 3 : null,
          noticePeriod: template.contractType === ContractTypeEnum.PERMANENT ? 30 : 15,
          renewalTerms: template.contractType === ContractTypeEnum.FIXED_TERM ? 'Renouvelable une fois' : null,
          terminationClause: 'Selon le Code du Travail de la RDC',
          benefits: [
            'Assurance maladie',
            'Transport',
            'Allocation familiale',
            'Prime de fin d\'année'
          ],
          responsibilities: [
            'Exécuter les tâches assignées',
            'Respecter le règlement intérieur',
            'Maintenir la confidentialité',
            'Participer aux formations'
          ],
          signedAt: Math.random() > 0.2 ? startDate : null, // 80% des contrats sont signés
          employeeId: employee.id,
          companyId: employee.companyId,
          templateId: template.id,
        },
      });

      createdContracts.push(contract);
      console.log(`✅ Created contract: ${contract.contractNumber} for employee ${employee.employeeNumber}`);
    } catch (error) {
      console.error(`❌ Error creating contract for employee ${employee.employeeNumber}:`, error.message);
    }
  }

  console.log(`📄 Created ${createdContracts.length} contracts`);
  return createdContracts;
}
