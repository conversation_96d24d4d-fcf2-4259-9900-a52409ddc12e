import { PrismaClient, DocumentApprovalStatusEnum } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedDocumentTemplates(companies: any[]) {
  console.log('📑 Seeding document templates...');

  const templates = [
    {
      name: 'Certificat de Travail',
      description: 'Template pour certificat de travail',
      category: 'HR',
      content: `
CERTIFICAT DE TRAVAIL

Je soussigné(e), {{directorName}}, {{directorTitle}} de {{companyName}}, certifie par la présente que :

Monsieur/Madame {{employeeName}}, né(e) le {{birthDate}} à {{birthPlace}}, a été employé(e) dans notre entreprise du {{startDate}} au {{endDate}} en qualité de {{jobTitle}}.

Durant cette période, l'intéressé(e) a fait preuve de :
- Compétence professionnelle
- Assiduité au travail
- Respect de la hiérarchie
- Bonne moralité

Ce certificat est délivré à l'intéressé(e) pour servir et valoir ce que de droit.

Fait à {{city}}, le {{issueDate}}

{{directorName}}
{{directorTitle}}
{{companyName}}

Cachet de l'entreprise
      `,
      isActive: true,
    },
    {
      name: 'Attestation de Salaire',
      description: 'Template pour attestation de salaire',
      category: 'Finance',
      content: `
ATTESTATION DE SALAIRE

Je soussigné(e), {{directorName}}, {{directorTitle}} de {{companyName}}, atteste par la présente que :

Monsieur/Madame {{employeeName}}, employé(e) de notre entreprise en qualité de {{jobTitle}} depuis le {{startDate}}, perçoit un salaire mensuel net de {{netSalary}} {{currency}}.

Les éléments de rémunération sont les suivants :
- Salaire de base : {{baseSalary}} {{currency}}
- Primes et indemnités : {{allowances}} {{currency}}
- Déductions sociales : {{socialDeductions}} {{currency}}
- Impôt sur le revenu : {{incomeTax}} {{currency}}

Cette attestation est délivrée à l'intéressé(e) pour servir et valoir ce que de droit.

Fait à {{city}}, le {{issueDate}}

{{directorName}}
{{directorTitle}}
{{companyName}}

Cachet de l'entreprise
      `,
      isActive: true,
    },
    {
      name: 'Demande de Congé',
      description: 'Template pour demande de congé',
      category: 'HR',
      content: `
DEMANDE DE CONGÉ

Nom et Prénom : {{employeeName}}
Fonction : {{jobTitle}}
Département : {{department}}
Numéro d'employé : {{employeeNumber}}

Type de congé demandé : {{leaveType}}
Date de début : {{startDate}}
Date de fin : {{endDate}}
Nombre de jours : {{leaveDays}}

Motif de la demande :
{{leaveReason}}

Adresse pendant le congé :
{{leaveAddress}}
Téléphone : {{leavePhone}}

Date de la demande : {{requestDate}}

Signature de l'employé : {{employeeName}}

---

AVIS DU SUPÉRIEUR HIÉRARCHIQUE
□ Favorable    □ Défavorable

Observations :
{{supervisorComments}}

Date : {{supervisorDate}}
Signature : {{supervisorName}}

---

DÉCISION DE LA DIRECTION
□ Accordé    □ Refusé    □ Reporté

Date : {{decisionDate}}
Signature : {{directorName}}
      `,
      isActive: true,
    },
    {
      name: 'Rapport d\'Évaluation',
      description: 'Template pour rapport d\'évaluation des employés',
      category: 'HR',
      content: `
RAPPORT D'ÉVALUATION ANNUELLE

INFORMATIONS GÉNÉRALES
Nom et Prénom : {{employeeName}}
Fonction : {{jobTitle}}
Département : {{department}}
Période d'évaluation : {{evaluationPeriod}}
Évaluateur : {{evaluatorName}}

CRITÈRES D'ÉVALUATION

1. COMPÉTENCES TECHNIQUES
□ Excellent  □ Très bien  □ Bien  □ Satisfaisant  □ Insuffisant
Commentaires : {{technicalSkillsComments}}

2. QUALITÉ DU TRAVAIL
□ Excellent  □ Très bien  □ Bien  □ Satisfaisant  □ Insuffisant
Commentaires : {{workQualityComments}}

3. PRODUCTIVITÉ
□ Excellent  □ Très bien  □ Bien  □ Satisfaisant  □ Insuffisant
Commentaires : {{productivityComments}}

4. RELATIONS INTERPERSONNELLES
□ Excellent  □ Très bien  □ Bien  □ Satisfaisant  □ Insuffisant
Commentaires : {{interpersonalComments}}

5. PONCTUALITÉ ET ASSIDUITÉ
□ Excellent  □ Très bien  □ Bien  □ Satisfaisant  □ Insuffisant
Commentaires : {{attendanceComments}}

OBJECTIFS POUR LA PROCHAINE PÉRIODE
{{nextPeriodObjectives}}

BESOINS EN FORMATION
{{trainingNeeds}}

ÉVALUATION GLOBALE
□ Excellent  □ Très bien  □ Bien  □ Satisfaisant  □ Insuffisant

Date : {{evaluationDate}}
Signature de l'évaluateur : {{evaluatorName}}
Signature de l'employé : {{employeeName}}
      `,
      isActive: true,
    },
    {
      name: 'Procès-Verbal de Réunion',
      description: 'Template pour procès-verbal de réunion',
      category: 'Administration',
      content: `
PROCÈS-VERBAL DE RÉUNION

Date : {{meetingDate}}
Heure : {{meetingTime}}
Lieu : {{meetingLocation}}
Objet : {{meetingSubject}}

PARTICIPANTS
{{participants}}

ORDRE DU JOUR
{{agenda}}

POINTS DISCUTÉS
{{discussionPoints}}

DÉCISIONS PRISES
{{decisions}}

ACTIONS À ENTREPRENDRE
{{actionItems}}

PROCHAINE RÉUNION
Date : {{nextMeetingDate}}
Lieu : {{nextMeetingLocation}}

Rédigé par : {{secretary}}
Date : {{reportDate}}
Signature : {{secretarySignature}}

Approuvé par : {{chairperson}}
Date : {{approvalDate}}
Signature : {{chairpersonSignature}}
      `,
      isActive: true,
    },
    {
      name: 'Fiche de Poste',
      description: 'Template pour description de poste',
      category: 'HR',
      content: `
FICHE DE POSTE

IDENTIFICATION DU POSTE
Intitulé du poste : {{jobTitle}}
Département : {{department}}
Supérieur hiérarchique : {{supervisor}}
Nombre de personnes encadrées : {{teamSize}}

MISSION PRINCIPALE
{{mainMission}}

ACTIVITÉS PRINCIPALES
{{mainActivities}}

COMPÉTENCES REQUISES

Compétences techniques :
{{technicalSkills}}

Compétences comportementales :
{{behavioralSkills}}

FORMATION ET EXPÉRIENCE
Formation : {{requiredEducation}}
Expérience : {{requiredExperience}}

CONDITIONS D'EXERCICE
Lieu de travail : {{workLocation}}
Horaires : {{workSchedule}}
Déplacements : {{travelRequirements}}

ÉVOLUTION DE CARRIÈRE
{{careerEvolution}}

Date de création : {{creationDate}}
Créé par : {{createdBy}}
Date de mise à jour : {{updateDate}}
Mis à jour par : {{updatedBy}}
      `,
      isActive: true,
    },
  ];

  const createdTemplates = [];

  for (const company of companies) {
    for (const templateData of templates) {
      try {
        const template = await prisma.documentTemplate.create({
          data: {
            ...templateData,
            companyId: company.id,
          },
        });
        createdTemplates.push(template);
        console.log(`✅ Created document template: ${template.name} for ${company.companyName}`);
      } catch (error) {
        console.error(`❌ Error creating document template ${templateData.name}:`, error.message);
      }
    }
  }

  console.log(`📑 Created ${createdTemplates.length} document templates`);
  return createdTemplates;
}

export async function seedDocuments(employees: any[], contracts: any[], templates: any[]) {
  console.log('📄 Seeding documents...');

  const documentTypes = [
    'CV', 'Diplôme', 'Certificat médical', 'Pièce d\'identité', 'Acte de naissance',
    'Certificat de travail', 'Attestation de salaire', 'Contrat de travail',
    'Fiche de paie', 'Rapport d\'évaluation', 'Demande de congé', 'Procès-verbal'
  ];

  const createdDocuments = [];

  for (const { employee } of employees.slice(0, 30)) { // Créer des documents pour 30 employés
    const documentCount = Math.floor(Math.random() * 5) + 2; // 2-6 documents par employé
    
    for (let i = 0; i < documentCount; i++) {
      const docType = documentTypes[Math.floor(Math.random() * documentTypes.length)];
      const template = templates.find(t => 
        t.companyId === employee.companyId && 
        t.name.toLowerCase().includes(docType.toLowerCase().split(' ')[0])
      );

      try {
        const document = await prisma.document.create({
          data: {
            name: `${docType} - ${employee.employeeNumber}`,
            type: docType,
            url: `https://documents.example.com/${employee.employeeNumber}/${docType.replace(/\s+/g, '_').toLowerCase()}.pdf`,
            description: `Document ${docType} pour l'employé ${employee.employeeNumber}`,
            employeeId: employee.id,
            companyId: employee.companyId,
            documentTemplateId: template?.id,
          },
        });

        createdDocuments.push(document);
        console.log(`✅ Created document: ${document.name}`);
      } catch (error) {
        console.error(`❌ Error creating document ${docType}:`, error.message);
      }
    }
  }

  // Créer des documents liés aux contrats
  for (const contract of contracts.slice(0, 20)) {
    try {
      const document = await prisma.document.create({
        data: {
          name: `Contrat ${contract.contractNumber}`,
          type: 'Contrat de travail',
          url: `https://documents.example.com/contracts/${contract.contractNumber}.pdf`,
          description: `Document contractuel pour le contrat ${contract.contractNumber}`,
          employeeId: contract.employeeId,
          companyId: contract.companyId,
          contractId: contract.id,
        },
      });

      createdDocuments.push(document);
      console.log(`✅ Created contract document: ${document.name}`);
    } catch (error) {
      console.error(`❌ Error creating contract document:`, error.message);
    }
  }

  console.log(`📄 Created ${createdDocuments.length} documents`);
  return createdDocuments;
}

export async function seedDocumentApprovals(documents: any[], employees: any[]) {
  console.log('✅ Seeding document approvals...');

  const createdApprovals = [];

  // Créer des approbations pour certains documents
  for (const document of documents.slice(0, 15)) { // 15 documents nécessitent une approbation
    // Trouver un approbateur (manager ou RH)
    const approvers = employees.filter(({ user }) => 
      user.role === 'ADMIN_HR' || user.role === 'MANAGER'
    );
    
    if (approvers.length === 0) continue;

    const approver = approvers[Math.floor(Math.random() * approvers.length)];
    const requester = employees.find(({ employee }) => employee.id === document.employeeId);
    
    if (!requester) continue;

    const statuses = [
      DocumentApprovalStatusEnum.PENDING,
      DocumentApprovalStatusEnum.APPROVED,
      DocumentApprovalStatusEnum.REJECTED
    ];
    
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    const approvalData: any = {
      status,
      comments: status === DocumentApprovalStatusEnum.APPROVED 
        ? 'Document approuvé conforme aux exigences'
        : status === DocumentApprovalStatusEnum.REJECTED
        ? 'Document non conforme, veuillez corriger et soumettre à nouveau'
        : 'En attente de vérification',
      documentId: document.id,
      approverId: approver.employee.id,
      requesterId: requester.employee.id,
    };

    if (status === DocumentApprovalStatusEnum.APPROVED) {
      approvalData.approvedAt = new Date();
    } else if (status === DocumentApprovalStatusEnum.REJECTED) {
      approvalData.rejectedAt = new Date();
    }

    try {
      const approval = await prisma.documentApproval.create({
        data: approvalData,
      });

      createdApprovals.push(approval);
      console.log(`✅ Created document approval for: ${document.name}`);
    } catch (error) {
      console.error(`❌ Error creating document approval:`, error.message);
    }
  }

  console.log(`✅ Created ${createdApprovals.length} document approvals`);
  return createdApprovals;
}
