generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["views"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// This table stores user information including email, password, role, avatar, and password reset details.
model User {
  id              String        @id @unique @default(uuid())
  email           String        @unique
  password        String?
  googleId        String?       @map("google_id")
  verified        <PERSON><PERSON><PERSON>       @default(false)
  verificationOtp String?       @map("verification_otp")
  role            RoleEnum
  passwordReset   Json          @default("{\"otp\": null, \"expiresAt\": null}") @map("password_reset_otp") @db.JsonB
  profile         Profile?
  jobOffers       JobOffer[]    @relation("SavedJobOffers")
  applications    Application[]
  employeeData    EmployeeData?
  documents       Document[]    @relation("UserDocuments")

  @@map("users")
}

// This table stores personal profile information of users including first name, last name, birth date, and address.
model Profile {
  id          String    @id @default(uuid())
  firstName   String    @map("first_name")
  lastName    String    @map("last_name")
  avatar      String?
  phoneNumber String?   @map("phone_number")
  birthDate   DateTime? @map("birth_date")
  addressId   String?   @unique @map("address_id")
  address     Address?  @relation(fields: [addressId], references: [id])
  userId      String    @unique @map("user_id")
  user        User      @relation(fields: [userId], references: [id])

  @@map("profiles")
}

// This table stores company information including company name, email, phone, website, and address.
model Company {
  id                      String         @id @default(uuid())
  companyName             String         @unique @map("company_name")
  email                   String         @unique
  phoneNumbers            String[]       @map("phone_numbers")
  website                 String?        @map("web_site")
  logo                    String?
  addressId               String         @unique @map("address_id")
  address                 Address?       @relation(fields: [addressId], references: [id])
  jobOffers               JobOffer[]
  employees               EmployeeData[]
  jobVacancies            JobVacancy[]
  officialName            String         @map("official_name")
  taxIdentificationNumber String         @map("tax_identification_number")
  industry                String
  description             String
  departments             Department[]
  documents               Document[]     @relation("CompanyDocuments")

  // New Payroll and Tax-related Fields
  taxSettings          CompanyTaxSettings?
  payrollConfiguration PayrollConfiguration?

  @@map("companies")
}

// Document model for both users and companies
model Document {
  id          String   @id @default(uuid())
  name        String
  type        String
  url         String
  description String?
  uploadedAt  DateTime @default(now()) @map("uploaded_at")

  // Relations
  userId String? @map("user_id")
  user   User?   @relation("UserDocuments", fields: [userId], references: [id])

  companyId String?  @map("company_id")
  company   Company? @relation("CompanyDocuments", fields: [companyId], references: [id])

  employeeId String?       @map("employee_id")
  employee   EmployeeData? @relation(fields: [employeeId], references: [id])

  @@map("documents")
}

// New model for company-wide tax and social security settings
model CompanyTaxSettings {
  id        String  @id @default(uuid())
  companyId String  @unique @map("company_id")
  company   Company @relation(fields: [companyId], references: [id])

  // Tax Calculation Parameters
  incomeTaxRate             Float @map("income_tax_rate")
  socialSecurityRate        Float @map("social_security_rate")
  unEmploymentInsuranceRate Float @map("unemployment_insurance_rate")
  healthInsuranceRate       Float @map("health_insurance_rate")
  pensionContributionRate   Float @map("pension_contribution_rate")

  // Tax Thresholds
  incomeTaxThreshold      Float? @map("income_tax_threshold")
  socialSecurityThreshold Float? @map("social_security_threshold")

  // Allowances and Deductions
  standardDeduction Float? @map("standard_deduction")
  familyAllowance   Float? @map("family_allowance")

  // Withholding and Reporting
  taxPaymentFrequency TaxPaymentFrequencyEnum? @map("tax_payment_frequency")

  @@map("company_tax_settings")
}

// New model for payroll-specific configurations
model PayrollConfiguration {
  id        String  @id @default(uuid())
  companyId String  @unique @map("company_id")
  company   Company @relation(fields: [companyId], references: [id])

  // Payroll Cycle
  payrollCycle PayrollCycleEnum? @map("payroll_cycle")
  paymentDay   Int               @map("payment_day") // Day of month for salary payment

  // Overtime Calculations
  overtimeMultiplier Float  @default(1.5) @map("overtime_multiplier")
  maxOvertimeHours   Float? @map("max_overtime_hours")

  // Bonus and Compensation
  bonusType            BonusCalculationEnum? @map("bonus_type")
  performanceBonusRate Float?                @map("performance_bonus_rate")

  // Leave and Time-off Calculations
  paidTimeOffAccrualRate Float? @map("paid_time_off_accrual_rate")

  @@map("payroll_configuration")
}

// This table stores address information including street, city, postal code, and country.
model Address {
  id         String   @id @default(uuid())
  street     String?
  city       String?
  postalCode String?  @map("postal_code")
  country    String
  companies  Company?
  user       Profile?

  @@map("address")
}

// This table stores job offer information including title, description, publish date, expiration date, and company.
model JobOffer {
  id             String             @id @default(uuid())
  title          String
  description    String
  publishDate    DateTime           @map("publish_date")
  expirationDate DateTime           @map("expiration_date")
  status         JobOfferStatusEnum @default(ACTIVE)
  location       String
  contractTypes  ContractTypeEnum[] @map("contract_types") // Changé en tableau
  minSalary      Float?             @map("min_salary")
  maxSalary      Float?             @map("max_salary")
  requiredSkills String[]           @map("required_skills")

  // Relations
  companyId String  @map("company_id")
  company   Company @relation(fields: [companyId], references: [id])

  // Relations optionnelles vers Department et Position
  departmentId String?     @map("department_id")
  department   Department? @relation(fields: [departmentId], references: [id])
  positionId   String?     @map("position_id")
  position     Position?   @relation(fields: [positionId], references: [id])

  // Autres relations existantes
  applications Application[]
  savedByUsers User[]        @relation("SavedJobOffers")

  @@map("job_offer")
}

// This table stores application information including application date, status, job reference, and user reference.
model Application {
  id                      String                @id @default(uuid())
  applicationDate         DateTime              @map("application_date")
  status                  ApplicationStatusEnum
  jobId                   String                @map("job_id")
  userId                  String                @map("user_id")
  coverLetter             String                @map("cover_letter")
  resume                  String                @map("resume")
  references              String?               @map("references")
  additionalDocuments     String[]              @map("additional_documents")
  preferredStartDate      DateTime?             @map("preferred_start_date")
  currentEmploymentStatus String?               @map("current_employment_status")
  desiredSalary           Float?                @map("desired_salary")

  jobOffer             JobOffer              @relation(fields: [jobId], references: [id])
  user                 User                  @relation(fields: [userId], references: [id])
  applicationResponses ApplicationResponse[]

  @@map("applications")
}

// This table stores application response information including response date, content, and application reference.
model ApplicationResponse {
  id                      String      @id @default(uuid())
  applicationResponseDate DateTime    @map("application_response_date")
  content                 String
  applicationId           String      @map("application_id")
  application             Application @relation(fields: [applicationId], references: [id])

  @@map("application_response")
}

// New model for Departments
model Department {
  id             String         @id @default(uuid())
  departmentName String?
  description    String?
  companyId      String         @map("company_id")
  company        Company        @relation(fields: [companyId], references: [id])
  positions      Position[]
  employees      EmployeeData[]
  jobOffers      JobOffer[]

  @@unique([departmentName, companyId])
  @@map("departments")
}

// New model for Positions
model Position {
  id             String         @id @default(uuid())
  positionTitle  String         @map("position_title")
  description    String?
  departmentId   String         @map("department_id")
  department     Department     @relation(fields: [departmentId], references: [id])
  employees      EmployeeData[]
  requiredSkills String[]       @map("required_skills")
  jobOffers      JobOffer[]

  @@map("positions")
}

// New model for Tasks
model Task {
  id            String           @id @default(uuid())
  title         String
  description   String?
  dueDate       DateTime         @map("due_date")
  status        TaskStatusEnum   @default(TODO)
  priority      TaskPriorityEnum @default(MEDIUM)
  assignedToId  String?          @map("assigned_to_id")
  assignedTo    EmployeeData?    @relation(fields: [assignedToId], references: [id])
  createdById   String           @map("created_by_id")
  createdBy     EmployeeData     @relation("TasksCreated", fields: [createdById], references: [id])
  startDate     DateTime?        @map("start_date")
  completedDate DateTime?        @map("completed_date")
  parentTaskId  String?          @map("parent_task_id")
  parentTask    Task?            @relation("SubTasks", fields: [parentTaskId], references: [id])
  subTasks      Task[]           @relation("SubTasks")

  @@map("tasks")
}

// This table stores information about employees including their name, birth date, hire date, position, company, and address.
model EmployeeData {
  id                      String                  @id @default(uuid())
  user                    User                    @relation(fields: [id], references: [id])
  hireDate                DateTime                @map("hire_date")
  companyId               String                  @map("company_id")
  company                 Company                 @relation(fields: [companyId], references: [id])
  salaries                Salary[]
  payslips                Payslip[]
  leaves                  Leave[]
  timesheets              Timesheet[]
  performanceEvaluations  PerformanceEvaluation[]
  trainings               Training[]
  issuedObjects           IssuedObject[]
  disciplinaryActions     DisciplinaryAction[]
  complaints              Complaint[]
  benefits                Benefit[]
  expenseReports          ExpenseReport[]
  departures              Departure[]
  inductions              Induction[]
  recommendationsSent     Recommendation[]        @relation("EmployeeSender")
  recommendationsReceived Recommendation[]        @relation("EmployeeReceiver")
  probationPeriods        ProbationPeriod[]
  documents               Document[]              @relation()

  // New fields for departments and positions
  departmentId String?     @map("department_id")
  department   Department? @relation(fields: [departmentId], references: [id])
  positionId   String?     @map("position_id")
  position     Position?   @relation(fields: [positionId], references: [id])

  // New relations for tasks
  assignedTasks Task[]
  createdTasks  Task[] @relation("TasksCreated")

  @@map("employee_data")
}

// This table stores salary information for employees including the amount and effective date.
model Payslip {
  id              String       @id @default(uuid())
  month           Int
  year            Int
  grossSalary     Float        @map("gross_salary")
  taxDeductions   Float        @map("tax_deductions")
  socialSecurity  Float        @map("social_security")
  otherDeductions Float        @map("other_deductions")
  netSalary       Float        @map("net_salary")
  employeeId      String       @map("employee_id")
  employee        EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("payslip")
}

model Salary {
  id                 String       @id @default(uuid())
  baseSalary         Float        @map("base_salary")
  housingAllowance   Float        @map("housing_allowance")
  transportAllowance Float        @map("transport_allowance")
  bonus              Float        @map("bonus")
  overtimeHours      Int          @map("overtime_hours")
  overtimeRate       Float        @map("overtime_rate")
  effectiveDate      DateTime     @map("effective_date")
  employeeId         String       @map("employee_id")
  employee           EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("salaries")
}

// This table stores leave information for employees including leave type, start date, end date, status, and employee reference.
model Leave {
  id         String          @id @default(uuid())
  leaveType  LeaveTypeEnum   @map("leave_type")
  startDate  DateTime        @map("start_date")
  endDate    DateTime        @map("end_date")
  reason     String?
  status     LeaveStatusEnum
  employeeId String          @map("employee_id")
  employee   EmployeeData    @relation(fields: [employeeId], references: [id])

  @@map("leaves")
}

// This table stores timesheet information for employees including period start, period end, regular hours, and overtime hours.
model Timesheet {
  id                  String       @id @default(uuid())
  periodStart         DateTime     @map("period_start")
  periodEnd           DateTime     @map("period_end")
  totalRegularHours   Float        @map("total_regular_hours")
  totalOvertimeHours  Float        @map("total_overtime_hours")
  totalUndertimeHours Float        @map("total_undertime_hours")
  employeeId          String       @map("employee_id")
  employee            EmployeeData @relation(fields: [employeeId], references: [id])
  workDays            WorkDay[]    @relation("TimesheetWorkDays")

  @@map("timesheets")
}

model WorkDay {
  id             String    @id @default(uuid())
  date           DateTime  @map("date")
  arrivalTime    DateTime? @map("arrival_time")
  departureTime  DateTime? @map("departure_time")
  regularHours   Float     @map("regular_hours")
  overtimeHours  Float     @map("overtime_hours")
  undertimeHours Float     @map("undertime_hours")
  timesheetId    String    @map("timesheet_id")
  timesheet      Timesheet @relation(name: "TimesheetWorkDays", fields: [timesheetId], references: [id])

  @@map("workdays")
}

// This table stores performance evaluation information for employees including period start, period end, goals, self-evaluation, and manager evaluation.
model PerformanceEvaluation {
  id                String       @id @default(uuid())
  periodStart       DateTime     @map("period_start")
  periodEnd         DateTime     @map("period_end")
  goals             String
  selfEvaluation    String       @map("self_evaluation")
  managerEvaluation String       @map("manager_evaluation")
  employeeId        String       @map("employee_id")
  employee          EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("performance_evaluations")
}

// This table stores training information for employees including training name, description, start date, end date, and employee reference.
model Training {
  id           String       @id @default(uuid())
  trainingName String       @map("training_name")
  description  String
  startDate    DateTime     @map("start_date")
  endDate      DateTime     @map("end_date")
  employeeId   String       @map("employee_id")
  employee     EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("trainings")
}

// This table stores issued object information for employees including object name, description, issued date, return date, and employee reference.
model IssuedObject {
  id          String       @id @default(uuid())
  objectName  String       @map("object_name")
  description String
  issuedDate  DateTime     @map("issued_date")
  returnDate  DateTime     @map("return_date")
  employeeId  String       @map("employee_id")
  employee    EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("issue_objects")
}

// This table stores disciplinary action information for employees including action date, description, action taken, and employee reference.
model DisciplinaryAction {
  id          String       @id @default(uuid())
  actionDate  DateTime     @map("action_date")
  description String
  actionTaken String       @map("action_taken")
  employeeId  String       @map("employee_id")
  employee    EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("disciplinary_actions")
}

// This table stores complaint information for employees including complaint date, description, resolution date, resolution details, and employee reference.
model Complaint {
  id                String       @id @default(uuid())
  complaintDate     DateTime     @map("complaint_date")
  description       String
  resolutionDate    DateTime     @map("resolution_date")
  resolutionDetails String       @map("resolution_details")
  employeeId        String       @map("employee_id")
  employee          EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("complaints")
}

// This table stores benefit information for employees including benefit type, description, authorized amount, and employee reference.
model Benefit {
  id               String          @id @default(uuid())
  benefitType      BenefitTypeEnum @map("benefit_type")
  description      String
  authorizedAmount Float           @map("authorized_amount")
  employeeId       String          @map("employee_id")
  employee         EmployeeData    @relation(fields: [employeeId], references: [id])

  @@map("benefits")
}

// This table stores expense report information for employees including description, amount, report date, and employee reference.
model ExpenseReport {
  id          String       @id @default(uuid())
  description String
  amount      Float
  reportDate  DateTime     @map("report_date")
  employeeId  String       @map("employee_id")
  employee    EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("expenses_reports")
}

// This table stores job vacancy information including job title, description, opening date, closing date, and company reference.
model JobVacancy {
  id          String   @id @default(uuid())
  jobTitle    String   @map("job_title")
  description String
  openingDate DateTime @map("opening_date")
  closingDate DateTime @map("closing_date")
  companyId   String   @map("company_id")
  company     Company  @relation(fields: [companyId], references: [id])

  @@map("jobs_vacancy")
}

// This table stores probation period information for employees including start date, end date, status, and employee reference.
model ProbationPeriod {
  id         String              @id @default(uuid())
  startDate  DateTime            @map("start_date")
  endDate    DateTime            @map("end_date")
  status     ProbationStatusEnum
  employeeId String              @map("employee_id")
  employee   EmployeeData        @relation(fields: [employeeId], references: [id])

  @@map("probation_periods")
}

// This table stores recommendation information including description, recommendation date, sender employee reference, and receiver employee reference.
model Recommendation {
  id                 String       @id @default(uuid())
  description        String
  recommendationDate DateTime     @map("recommendation_date")
  senderEmployeeId   String       @map("sender_employee_id")
  receiverEmployeeId String       @map("receiver_employee_id")
  senderEmployee     EmployeeData @relation("EmployeeSender", fields: [senderEmployeeId], references: [id])
  receiverEmployee   EmployeeData @relation("EmployeeReceiver", fields: [receiverEmployeeId], references: [id])

  @@map("recommendations")
}

// This table stores departure information for employees including departure date, reason, and employee reference.
model Departure {
  id            String       @id @default(uuid())
  departureDate DateTime     @map("departure_date")
  reason        String
  employeeId    String       @map("employee_id")
  employee      EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("departures")
}

// This table stores induction information for employees including start date, end date, trainer, feedback, and employee reference.
model Induction {
  id         String       @id @default(uuid())
  startDate  DateTime     @map("start_date")
  endDate    DateTime     @map("end_date")
  trainer    String
  feedback   String
  employeeId String       @map("employee_id")
  employee   EmployeeData @relation(fields: [employeeId], references: [id])

  @@map("inductions")
}

// Enums for roles, permissions, leave types, leave statuses, benefit types, application statuses, and probation statuses.
enum RoleEnum {
  SUPER_ADMIN
  ADMIN_HR
  ADMIN_RECRUITMENT
  OWNER
  EMPLOYEE
  CANDIDATE
}

enum PermissionEnum {
  CAN_CREATE
  CAN_EDIT
  CAN_DELETE
  CAN_READ
}

enum LeaveTypeEnum {
  ANNUAL
  SICK
  MATERNITY
  UNPAID
}

enum LeaveStatusEnum {
  REQUESTED
  APPROVED
  REJECTED
}

enum BenefitTypeEnum {
  INSURANCE
  VEHICLE
}

enum ApplicationStatusEnum {
  PENDING
  APPROVED
  REJECTED
}

enum ProbationStatusEnum {
  IN_PROGRESS
  CONFIRMED
  EXTENDED
}

// New enums for Tasks
enum TaskStatusEnum {
  TODO
  IN_PROGRESS
  REVIEW
  COMPLETED
  BLOCKED
}

enum TaskPriorityEnum {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum TaxPaymentFrequencyEnum {
  MONTHLY
  QUARTERLY
  ANNUALLY
}

enum PayrollCycleEnum {
  MONTHLY
  SEMI_MONTHLY
  BI_WEEKLY
  WEEKLY
}

enum BonusCalculationEnum {
  PERCENTAGE_OF_SALARY
  FIXED_AMOUNT
  PERFORMANCE_BASED
}

enum JobOfferStatusEnum {
  DRAFT
  ACTIVE
  EXPIRED
  FILLED
  CANCELLED
}

enum ContractTypeEnum {
  FULL_TIME
  PART_TIME
  TEMPORARY
  CONTRACT
  INTERNSHIP
  REMOTE
  HYBRID
  ON_SITE
  SEASONAL
  FREELANCE
}
