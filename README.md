# HR Management System

This is a comprehensive Human Resources Management System built with NestJS and Prisma. It provides a wide range of features to streamline HR processes, including employee management, recruitment, leave management, performance evaluations, and more.

## Features

- **Employee Management**: Manage employee information, salaries, payslips, leaves, timesheets, performance evaluations, trainings, issued objects, disciplinary actions, grievances, benefits, expense reports, departures, inductions, recommendations, and probation periods.
- **Recruitment**: Publish job offers, manage applications, and track application responses.
- **Administrator Roles and Permissions**: Define roles and permissions for administrators with different levels of access.
- **Individual Profiles**: Individuals can create profiles, upload documents (CV, cover letter, CNSS, marriage certificate, etc.), and apply for job offers.
- **Company and Address Management**: Store company and address information for companies, employees, individuals, and temporary employees.
- **Temporary Employee Management**: Manage temporary employees with start and end dates, positions, and associated company and address information.
- **Job Vacancy Management**: Manage job vacancies with opening and closing dates, job titles, and descriptions.

## Technologies Used

- [NestJS](https://nestjs.com/) - A progressive Node.js framework for building efficient and scalable server-side applications.
- [Prisma](https://www.prisma.io/) - Next-generation ORM for Node.js and TypeScript that provides database access with advanced query capabilities.
- [PostgreSQL](https://www.postgresql.org/) - Open-source relational database management system used for data storage.

## Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/marcos-mus/compulse-hr-api

   ```

2. Install dependencies:

   ```bash
   cd CODLABHR-BACKEND
   pnpm install

   ```

3. Set up the PostgreSQL database and update the DATABASE_URL environment variable in the .env file with your database connection string.

4. Deploy migrations:

   ```bash
    npx prisma migrate deploy
   ```

5. Generate the Prisma client:

```bash
 npx prisma generate
```

6. Start the development server:

```bash
 pnpm start:dev
```

The application should now be running at http://localhost:3000.
