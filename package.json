{"name": "codlabhr-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "generate": "npx prisma generate", "migrate": "npx prisma migrate dev", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prepare": "husky install", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:ci": "pnpm test:cov", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^1.8.1", "@nestjs/axios": "^3.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.3", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^10.0.3", "@nestjs/mapped-types": "*", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.0.0", "@nestjs/swagger": "^7.0.4", "@nestjs/throttler": "^4.2.1", "@railway/cli": "^3.3.1", "@types/axios": "^0.14.0", "@types/date-fns": "^2.6.0", "@types/jsonwebtoken": "^9.0.2", "@types/luxon": "^3.3.1", "@types/passport-google-oauth20": "^2.0.11", "@types/passport-jwt": "^3.0.8", "apple-signin": "^1.0.9", "argon2": "^0.30.3", "axios": "1.4.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "crypto": "^1.0.1", "date-fns": "^2.30.0", "google-auth-library": "^8.9.0", "handlebars": "^4.7.7", "helmet": "^7.0.0", "install": "^0.13.0", "ioredis": "^5.3.2", "joi": "^17.9.2", "jsonwebtoken": "^9.0.0", "jwt-decode": "^3.1.2", "luxon": "^3.3.0", "nestjs-redis": "^1.3.3", "nodemailer": "^6.9.3", "otplib": "^12.0.1", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "process": "^0.11.10", "pug": "^3.0.2", "qrcode": "^1.5.3", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "speakeasy": "^2.0.0", "swagger-ui-express": "^4.6.3", "twilio": "^4.19.0"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@prisma/client": "^4.15.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.13", "@types/jest": "28.1.8", "@types/jwt-decode": "^3.1.0", "@types/node": "^16.0.0", "@types/nodemailer": "^6.4.8", "@types/speakeasy": "^2.0.7", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.3", "jest": "28.1.3", "lint-staged": "^13.2.2", "nyc": "^15.1.0", "prettier": "^2.3.2", "prisma": "^4.15.0", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"src/**/*.{ts,css,html}": ["pnpm lint"]}}