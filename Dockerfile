# Utiliser Node.js 20 sur Alpine Linux 3.21
FROM node:20-alpine

# Installer OpenSSL 3 et les bibliothèques nécessaires pour Prisma
RUN apk add --no-cache openssl libssl3 libc6-compat

# Installer PNPM globalement
RUN npm install -g pnpm@9.1.0

# Définir le répertoire de travail
WORKDIR /app

# Copier uniquement les fichiers package.json et pnpm-lock.yaml pour optimiser le cache Docker
COPY package*.json ./

COPY pnpm-lock.yaml ./
# Installer les dépendances avec PNPM
RUN pnpm i --frozen-lockfile

# Copier tout le code source
COPY . .

# Générer le client Prisma
RUN npx prisma generate

# Construire l'application
RUN pnpm build

# Lancer l'application
CMD ["node", "dist/main.js"]
